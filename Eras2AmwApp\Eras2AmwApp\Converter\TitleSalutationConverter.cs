﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="TitleSalutationConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class TitleSalutationConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string strValue = " ";

            if (value is Title title)
            {
                strValue = title.Label;
            }
            else if(value is Salutation salutation)
            {
                strValue = salutation.Label;
            }

            return strValue;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
