﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="RauchmelderOrderColorState.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class RauchmelderOrderColorState : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            DeviceUiState uiState = (DeviceUiState)value;
            Guid? amwInfoKey = uiState.DeviceOrderState.AmwInfoKeyGuid;

            return DeviceHasError(amwInfoKey) ? Color.Red : DeviceIsCompleted(uiState) ? Color.Green : Color.LightGray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private bool DeviceIsCompleted(DeviceUiState uiState)
        {
            ProcessState processState = uiState.DeviceOrderState.ProcessState;
            DeviceOrderKind deviceOrderKind = uiState.DeviceOrderState.OrderKind;
            bool isMaintained = uiState.IsMaintained;

            return deviceOrderKind == DeviceOrderKind.Inspection || deviceOrderKind == DeviceOrderKind.Maintenance
                ? isMaintained
                : processState != ProcessState.InProgress;
        }

        private bool DeviceHasError(Guid? amwInfoKey)
        {
            return amwInfoKey != null;
        }
    }
}
