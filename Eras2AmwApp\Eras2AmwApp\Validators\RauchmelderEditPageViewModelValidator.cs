﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceEditPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using System;
    using FluentValidation;
    using ViewModels;

    public class RauchmelderEditPageViewModelValidator : AbstractValidator<RauchmelderEditPageViewModel>
    {
        public RauchmelderEditPageViewModelValidator()
        {
            RuleFor(x => x.SelectedRoom)
                .NotEmpty().WithMessage("Der Raum darf nicht leer sein.");

            RuleFor(x => x.DeviceNumber)
                .NotEmpty().WithMessage("Die Gerätenummer darf nicht leer sein.");
        }
    }
}
