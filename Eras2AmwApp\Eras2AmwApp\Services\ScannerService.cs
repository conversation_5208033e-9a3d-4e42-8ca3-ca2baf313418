﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ScannerService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Services
{
    using System.Threading.Tasks;
    using ZXing;
    using ZXing.Mobile;
    using Eras2AmwApp.Interfaces;

    public class ScannerService : IScannerService
    {
        //ZXING CONFIG
        private const int DelayBetweenAnalyzingFramesValue = 150;
        private const int InitialDelayBeforeAnalyzingFramesValue = 300;
        private const int DelayBetweenContinuousScansValue = 1000;
        private const bool UseNativeScanning = false;

        public async Task<Result> ReadBarcode()
        {
            var scanner = new MobileBarcodeScanner();
            MobileBarcodeScanningOptions opt = new MobileBarcodeScanningOptions
            {
                DelayBetweenAnalyzingFrames = DelayBetweenAnalyzingFramesValue,
                InitialDelayBeforeAnalyzingFrames = InitialDelayBeforeAnalyzingFramesValue,
                DelayBetweenContinuousScans = DelayBetweenContinuousScansValue,
                UseNativeScanning = UseNativeScanning
            };

            var result = await scanner.Scan(opt);
            scanner.Cancel();
            return result;
        }
    }
}
