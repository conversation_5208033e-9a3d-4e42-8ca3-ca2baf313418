﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:inputLayout="clr-namespace:Syncfusion.XForms.TextInputLayout;assembly=Syncfusion.Core.XForms"
             xmlns:converter="clr-namespace:Eras2AmwApp.Converter"
             xmlns:border="clr-namespace:Syncfusion.XForms.Border;assembly=Syncfusion.Core.XForms"
             xmlns:tabView="clr-namespace:Syncfusion.XForms.TabView;assembly=Syncfusion.SfTabView.XForms"
             xmlns:syncfusion="clr-namespace:Syncfusion.SfNumericTextBox.XForms;assembly=Syncfusion.SfNumericTextBox.XForms"
             xmlns:local="clr-namespace:Eras2AmwApp.CustomControls"
             x:Class="Eras2AmwApp.Pages.RauchmelderExchangePage">

    <NavigationPage.TitleView>

        <Grid>
            
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="50*"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <ImageButton    Source="changeIcon.png"
                            Grid.Column="1"
                            BackgroundColor="Transparent"
                            HorizontalOptions="End"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            Command="{Binding ChangeDeviceOrderKindCommand}">

            </ImageButton>

            <ImageButton    Source="barcodeIcon.png"
                            Grid.Column="2"
                            BackgroundColor="Transparent"
                            HorizontalOptions="Start"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            Command="{Binding ReadBarcodCommand}">

                <ImageButton.Triggers>
                    <DataTrigger TargetType="ImageButton"
                                 Binding="{Binding TabView.SelectedIndex}"
                                 Value="1">
                        <Setter Property="Command"
                                Value="{Binding NewReadBarcodCommand}"/>
                    </DataTrigger>
                </ImageButton.Triggers>

            </ImageButton>

            <ImageButton    Source="photoIcon.png"
                            Grid.Column="3"
                            BackgroundColor="Transparent"
                            HorizontalOptions="End"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            Command="{Binding PhotoCommand}">

                <ImageButton.Triggers>
                    <DataTrigger TargetType="ImageButton"
                                 Binding="{Binding TabView.SelectedIndex}"
                                 Value="1">
                        <Setter Property="Command"
                                Value="{Binding NewPhotoCommand}"/>
                    </DataTrigger>
                </ImageButton.Triggers>

            </ImageButton>

            <ImageButton    Source="confirmIcon.png"
                            Grid.Column="4"
                            BackgroundColor="Transparent"
                            Margin="0"
                            HorizontalOptions="End"
                            WidthRequest="35"
                            HeightRequest="35"
                            Command="{Binding ConfirmOldDeviceCommand}">

                <ImageButton.Triggers>
                    <DataTrigger TargetType="ImageButton"
                                 Binding="{Binding TabView.SelectedIndex}"
                                 Value="1">
                        <Setter Property="Command"
                                Value="{Binding SaveDeviceCommand}"/>
                        <Setter Property="Source"
                                Value="saveIcon.png"/>

                    </DataTrigger>
                </ImageButton.Triggers>

            </ImageButton>

        </Grid>

    </NavigationPage.TitleView>

    <ContentPage.Resources>

        <converter:IsNullConverter x:Key="IsNullConverter"></converter:IsNullConverter>
        <converter:StringDecimalConverter x:Key="StringDecimalConverter"></converter:StringDecimalConverter>
        <converter:DeviceClassTypeConverter x:Key="DeviceClassTypeConverter"></converter:DeviceClassTypeConverter>

    </ContentPage.Resources>

    <ContentPage.Content>

        <tabView:SfTabView x:Name="pageTabView" VisibleHeaderCount="2" TabHeaderPosition="Bottom" EnableSwiping="False">

            <tabView:SfTabItem Title="Aktuelles Gerät">

                <tabView:SfTabItem.Content>

                    <ScrollView>

                        <StackLayout>

                            <Label  x:Name="SignatureErrorText"
                                    Text="Das Ausbaudatum darf nicht leer sein oder es muss ein Fehler ausgewählt sein!"
                                    FontAttributes="Bold"
                                    FontSize="18"
                                    HorizontalTextAlignment="Center"
                                    TextColor="Red"
                                    IsVisible="{Binding ShowMaintainOrErrorWarning}"
                                    IsEnabled="{Binding ShowMaintainOrErrorWarning}">

                            </Label>

                            <Frame  Margin="5"
                                    BorderColor="LightGray"
                                    Padding="0"
                                    CornerRadius="0"
                                    HasShadow="True">

                                <Grid RowSpacing="0">

                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                    </Grid.RowDefinitions>

                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="50*"></ColumnDefinition>
                                        <ColumnDefinition Width="25*"></ColumnDefinition>
                                        <ColumnDefinition Width="25*"></ColumnDefinition>
                                    </Grid.ColumnDefinitions>

                                    <inputLayout:SfTextInputLayout  x:Name="OldDeviceNumber"
                                                                    InputViewPadding="5"
                                                                    Margin="5,0,0,0"
                                                                    Hint="{markupExtensions:Localisation DeviceNumber}"
                                                                    ContainerType="Outlined"
                                                                    Grid.Row="0"
                                                                    Grid.Column="0"
                                                                    VerticalOptions="Center"
                                                                    HasError="{Binding OldDeviceNumberHasError}"
                                                                    ErrorText="{Binding OldDeviceNumberErrorText}">

                                        <Entry Text="{Binding OldDeviceNumber}"
                                               VerticalOptions="End"
                                               FontSize="Medium">

                                        </Entry>

                                    </inputLayout:SfTextInputLayout>

                                    <StackLayout    Orientation="Horizontal"
                                                    Margin="0,0,5,0"
                                                    Grid.Row="0"
                                                    Grid.Column="1"
                                                    Grid.ColumnSpan="2"
                                                    HorizontalOptions="End"
                                                    Spacing="1">

                                        <Label  x:Name="DeviceClassLabel"
                                                Margin="5,0,0,0"
                                                Text="{Binding OldDeviceClass}"
                                                FontSize="20"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Start"
                                                TextColor="Black"
                                                FontAttributes="Bold">

                                        </Label>

                                        <Image  Margin="5,2,2,5"
                                                WidthRequest="40"
                                                HeightRequest="40"
                                                Source="{Binding OldDeviceClass, Converter={StaticResource DeviceClassTypeConverter}}">

                                        </Image>

                                    </StackLayout>

                                    <inputLayout:SfTextInputLayout  x:Name="OldRoom"
                                                                    InputViewPadding="5"
                                                                    Margin="5,0,0,0"
                                                                    Hint="{markupExtensions:Localisation Room}"
                                                                    ContainerType="Outlined"
                                                                    Grid.Row="1"
                                                                    Grid.Column="0"
                                                                    VerticalOptions="Center"
                                                                    IsEnabled="False">

                                        <Picker ItemsSource="{Binding RoomList}"
                                                SelectedItem="{Binding OldSelectedRoom}"
                                                ItemDisplayBinding="{Binding Label}"
                                                VerticalOptions="End"
                                                FontSize="Medium">

                                        </Picker>

                                    </inputLayout:SfTextInputLayout>

                                    <inputLayout:SfTextInputLayout  x:Name="OldOngoingNumber"
                                                                    InputViewPadding="5"
                                                                    Margin="5,0,5,0"
                                                                    Hint="Laufende Nummer"
                                                                    ContainerType="Outlined"
                                                                    Grid.Row="1"
                                                                    Grid.Column="1"
                                                                    Grid.ColumnSpan="2"
                                                                    VerticalOptions="Center"
                                                                    IsEnabled="False">

                                        <Entry Text="{Binding OldOngoingNumber}"
                                               VerticalOptions="End"
                                               FontSize="Medium">

                                        </Entry>

                                    </inputLayout:SfTextInputLayout>

                                    <inputLayout:SfTextInputLayout  x:Name="OldDeviceDescription"
                                                                    InputViewPadding="5"
                                                                    Margin="5,0,0,0"
                                                                    Hint="Bezeichnung"
                                                                    ContainerType="Outlined"
                                                                    Grid.Row="2"
                                                                    Grid.Column="1"
                                                                    Grid.ColumnSpan="2"
                                                                    VerticalOptions="Center"
                                                                    IsEnabled="False">

                                        <Entry  Text="{Binding OldArticleDescription}"
                                                FontSize="Medium">

                                        </Entry>

                                    </inputLayout:SfTextInputLayout>

                                    <inputLayout:SfTextInputLayout  x:Name="OldArticleNumber"
                                                                    InputViewPadding="5"
                                                                    Margin="5,0,5,0"
                                                                    Hint="Artikel Nummer"
                                                                    ContainerType="Outlined"
                                                                    Grid.Row="2"
                                                                    Grid.Column="0"
                                                                    VerticalOptions="Center"
                                                                    IsEnabled="False">

                                        <Entry  Text="{Binding OldArticleNumber}"
                                                FontSize="Medium">

                                        </Entry>

                                    </inputLayout:SfTextInputLayout>

                                    <inputLayout:SfTextInputLayout  x:Name="OldCalibrationDate"
                                                                    InputViewPadding="5"
                                                                    Margin="5,0,5,0"
                                                                    Hint="{markupExtensions:Localisation CalibrationDate}"
                                                                    ContainerType="Outlined"
                                                                    Grid.Row="3"
                                                                    Grid.Column="1"
                                                                    Grid.ColumnSpan="2"
                                                                    VerticalOptions="Center">

                                        <Picker x:Name="OldEichdatum"
                                                ItemsSource="{Binding PickerYearList}"
                                                Title="Jahr auswählen:"
                                                SelectedItem="{Binding OldDeviceCalibrationDate}">

                                        </Picker>

                                    </inputLayout:SfTextInputLayout>

                                    <inputLayout:SfTextInputLayout  x:Name="OldInstallationDate"
                                                                    InputViewPadding="5"
                                                                    ContainerType="Outlined"
                                                                    Margin="5,0,5,0"
                                                                    Hint="{markupExtensions:Localisation InstallactionDate}"
                                                                    Grid.Row="4"
                                                                    Grid.Column="0"
                                                                    VerticalOptions="Center">

                                        <DatePicker Date="{Binding OldDeviceInstallationDate}"
                                                    VerticalOptions="End"
                                                    FontSize="Medium">

                                        </DatePicker>

                                        <inputLayout:SfTextInputLayout.TrailingView>

                                            <border:SfBorder    WidthRequest="70"
                                                                CornerRadius="10" 
                                                                HeightRequest="30"  
                                                                BackgroundColor="#538EEC">
                                                <Label  TextColor="White"
                                                        VerticalTextAlignment="Center" 
                                                        HorizontalTextAlignment="Center" 
                                                        Text="heute">
                                                    <Label.GestureRecognizers>
                                                        <TapGestureRecognizer   Command="{Binding SetInstallationDateCommand}"
                                                                                NumberOfTapsRequired="1" />
                                                    </Label.GestureRecognizers>
                                                </Label>
                                            </border:SfBorder>
                                        </inputLayout:SfTextInputLayout.TrailingView>

                                    </inputLayout:SfTextInputLayout>

                                    <inputLayout:SfTextInputLayout  x:Name="OldDeinstallationDate"
                                                                    InputViewPadding="5"
                                                                    ContainerType="Outlined"
                                                                    Margin="5,0,5,0"
                                                                    Hint="{markupExtensions:Localisation DeinstallationDate}"
                                                                    Grid.Row="4"
                                                                    Grid.Column="1"
                                                                    Grid.ColumnSpan="2" 
                                                                    VerticalOptions="Center"
                                                                    HasError="{Binding OldDeviceDeinstallationDateHasError}"
                                                                    ErrorText="{Binding OldDeviceDeinstallationDateErrorText}">

                                        <local:NullableDatePicker   NullableDate="{Binding OldDeviceDeinstallationDate}"
                                                                    VerticalOptions="End"
                                                                    FontSize="Medium">

                                        </local:NullableDatePicker>

                                        <inputLayout:SfTextInputLayout.TrailingView>

                                            <border:SfBorder    WidthRequest="70"
                                                                CornerRadius="10" 
                                                                HeightRequest="30"  
                                                                BackgroundColor="#538EEC">
                                                <Label  TextColor="White"
                                                VerticalTextAlignment="Center" 
                                                HorizontalTextAlignment="Center" 
                                                Text="heute">
                                                    <Label.GestureRecognizers>
                                                        <TapGestureRecognizer   Command="{Binding SetDeinstallationDateCommand}"
                                                                        NumberOfTapsRequired="1" />
                                                    </Label.GestureRecognizers>
                                                </Label>
                                            </border:SfBorder>
                                        </inputLayout:SfTextInputLayout.TrailingView>

                                    </inputLayout:SfTextInputLayout>

                                    <inputLayout:SfTextInputLayout  x:Name="OldReadingValue"
                                                                    InputViewPadding="5"
                                                                    Margin="5,0,5,0"
                                                                    Hint="Ablesung"
                                                                    ContainerType="Outlined"
                                                                    Grid.Row="5"
                                                                    Grid.Column="0"
                                                                    VerticalOptions="Center"
                                                                    IsEnabled="False">

                                        <syncfusion:SfNumericTextBox    Value="{Binding OldDeviceReading}" 
                                                                        VerticalOptions="End"
                                                                        FontSize="Medium"
                                                                        MaximumNumberDecimalDigits="3"
                                                                        AllowNull="True">

                                        </syncfusion:SfNumericTextBox>

                                    </inputLayout:SfTextInputLayout>

                                    <StackLayout    Orientation="Horizontal"
                                                    Margin="0,0,5,10"
                                                    Grid.Row="5"
                                                    Grid.Column="1"
                                                    Grid.ColumnSpan="2"
                                                    HorizontalOptions="Start"
                                                    Spacing="1">

                                        <CheckBox   x:Name="maintainSwitch"
                                                    HorizontalOptions="Center"
                                                    VerticalOptions="Center"
                                                    Color="Black"
                                                    IsChecked="{Binding OldDeviceIsMaintained}">
                                        </CheckBox>

                                        <Label  Text="Wartung"
                                                Margin="2,0,0,0"
                                                FontSize="Medium"
                                                VerticalOptions="Center"
                                                TextColor="Black">

                                        </Label>

                                    </StackLayout>

                                    <inputLayout:SfTextInputLayout  x:Name="OldAmwKey"
                                                                    InputViewPadding="5"
                                                                    Grid.Row="6"
                                                                    Grid.Column="0"
                                                                    Grid.ColumnSpan="3"
                                                                    Margin="5,0,5,0"
                                                                    Hint="{markupExtensions:Localisation SelectError}"
                                                                    ContainerType="Outlined"
                                                                    HorizontalOptions="FillAndExpand">

                                        <Picker SelectedItem="{Binding OldSelectedAmwKey}"
                                                ItemsSource="{Binding AmwInfoKeyList}"
                                                ItemDisplayBinding="{Binding Info}"
                                                VerticalOptions="End"
                                                FontSize="Medium">
                                        </Picker>

                                    </inputLayout:SfTextInputLayout>

                                    <inputLayout:SfTextInputLayout  x:Name="OldNote"
                                                                    InputViewPadding="5"
                                                                    Margin="5,0,5,0"
                                                                    Hint="Notiz:"
                                                                    ContainerType="Outlined"
                                                                    Grid.Row="7"
                                                                    Grid.Column="0"
                                                                    Grid.ColumnSpan="3"
                                                                    VerticalOptions="Center">

                                        <Entry  Text="{Binding OldDeviceNote}"
                                                VerticalOptions="End"
                                                FontSize="Medium">

                                        </Entry>

                                    </inputLayout:SfTextInputLayout>

                                    <Frame  Margin="5,0,5,5"
                                            BorderColor="LightGray"
                                            Padding="10"
                                            CornerRadius="5"
                                            HasShadow="True"
                                            Grid.Row="8"
                                            Grid.Column="0"
                                            Grid.ColumnSpan="3">

                                        <Frame.Triggers>
                                            <DataTrigger TargetType="Frame"
                                                         Binding="{Binding AdditionalArticleList.Count}"
                                                         Value="0">
                                                <Setter Property="IsVisible"
                                                        Value="False" />

                                            </DataTrigger>
                                        </Frame.Triggers>

                                        <Grid>

                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                            </Grid.RowDefinitions>

                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="75*"></ColumnDefinition>
                                                <ColumnDefinition Width="60"></ColumnDefinition>
                                                <ColumnDefinition Width="20*"></ColumnDefinition>
                                            </Grid.ColumnDefinitions>

                                            <Label Text="Zubehörteile Artikel"
                                                   FontSize="Medium"
                                                   FontAttributes="Bold"
                                                   HorizontalOptions="Start"
                                                   Margin="10,0,5,0"
                                                   Grid.Row="0"
                                                   Grid.Column="0"
                                                   Grid.ColumnSpan="2">

                                            </Label>

                                            <local:CustomEntry  Grid.Row="1"
                                                                Grid.Column="1"
                                                                Placeholder="#"
                                                                Text="{Binding AdditionalArticlesNumber}"
                                                                MaxLength="5"
                                                                FontSize="Medium"
                                                                HorizontalTextAlignment="Center"
                                                                VerticalTextAlignment="Center">

                                                <Entry.Triggers>
                                                    <DataTrigger TargetType="Entry"
                                                                 Binding="{Binding Source={x:Reference pickerArticlesList},Path=SelectedItem,Converter={StaticResource IsNullConverter}}"
                                                                 Value="False">
                                                        <Setter Property="Text"
                                                                Value="1" />
                                                    </DataTrigger>

                                                    <DataTrigger TargetType="Entry"
                                                                 Binding="{Binding Source={x:Reference pickerArticlesList},Path=SelectedItem,TargetNullValue=''}"
                                                                 Value="">
                                                        <Setter Property="Text"
                                                                Value="" />
                                                    </DataTrigger>

                                                </Entry.Triggers>
                                            </local:CustomEntry>

                                            <Picker x:Name="pickerArticlesList"
                                                    Margin="5,0,5,0"
                                                    Grid.Row="1"
                                                    Grid.Column="0"
                                                    ItemsSource="{Binding AdditionalArticleList}"
                                                    SelectedItem="{Binding SelectedAdditionalArticle}"
                                                    ItemDisplayBinding="{Binding Label}">

                                            </Picker>

                                            <ImageButton    x:Name="AddButton"
                                                            Source="newIcon.png"
                                                            BackgroundColor="Transparent"
                                                            Margin="5,0,10,5"
                                                            WidthRequest="40"
                                                            HeightRequest="40"
                                                            Grid.Row="1"
                                                            Grid.Column="2"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Start"
                                                            Command="{Binding AddArticleCommand}"
                                                            CommandParameter="{Binding SelectedAdditionalArticle}">

                                            </ImageButton>

                                        </Grid>

                                    </Frame>

                                    <Frame  Margin="5"
                                            BorderColor="LightGray"
                                            Padding="10"
                                            CornerRadius="5"
                                            HasShadow="True"
                                            Grid.Row="9"
                                            Grid.Column="0"
                                            Grid.ColumnSpan="3">

                                        <Frame.Triggers>
                                            <DataTrigger TargetType="Frame"
                                                         Binding="{Binding SelectedAdditionalArticleListCount}"
                                                         Value="0">
                                                <Setter Property="IsVisible"
                                                        Value="False" />

                                            </DataTrigger>
                                        </Frame.Triggers>

                                        <Grid>

                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                            </Grid.RowDefinitions>

                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="50*"></ColumnDefinition>
                                                <ColumnDefinition Width="20*"></ColumnDefinition>
                                                <ColumnDefinition Width="100"></ColumnDefinition>
                                            </Grid.ColumnDefinitions>

                                            <Label Text="Hinzugefügte Zubehörteile:"
                                                   FontSize="Medium"
                                                   FontAttributes="Bold"
                                                   HorizontalOptions="Start"
                                                   Margin="10,0,5,0"
                                                   Grid.Row="0"
                                                   Grid.Column="0"
                                                   Grid.ColumnSpan="2">

                                            </Label>

                                            <ListView x:Name="selectedArticlesList"
                                                      HeightRequest="110"
                                                      Margin="5,0,5,0"
                                                      Grid.Row="1"
                                                      Grid.Column="0"
                                                      RowHeight="55"
                                                      SelectionMode="Single"
                                                      IsPullToRefreshEnabled="False"
                                                      HasUnevenRows="False"
                                                      CachingStrategy="RetainElement"
                                                      ItemsSource="{Binding SelectedAdditionalArticleList}"
                                                      SelectedItem="{Binding SelectedToRemoveAdditionalAricle}">

                                                <ListView.ItemTemplate>
                                                    <DataTemplate>
                                                        <ViewCell>
                                                            <Frame  Margin="5"
                                                                    BorderColor="LightGray"
                                                                    Padding="0"
                                                                    CornerRadius="0"
                                                                    HasShadow="True">

                                                                <Grid>
                                                                    <Grid.RowDefinitions>
                                                                        <RowDefinition Height="Auto"></RowDefinition>
                                                                    </Grid.RowDefinitions>

                                                                    <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="180"></ColumnDefinition>
                                                                        <ColumnDefinition Width="60"></ColumnDefinition>
                                                                    </Grid.ColumnDefinitions>

                                                                    <Label  Text="{Binding AdditionalArticle.Label}"
                                                                            Grid.Row="0"
                                                                            Grid.Column="0"
                                                                            Margin="5"
                                                                            FontSize="Small"
                                                                            VerticalOptions="Center">
                                                                    </Label>

                                                                    <Label  Text="{Binding Amount}"
                                                                            Grid.Row="0"
                                                                            Grid.Column="1"
                                                                            Margin="5"
                                                                            FontSize="Small"
                                                                            HorizontalOptions="End">
                                                                    </Label>

                                                                </Grid>
                                                            </Frame>


                                                        </ViewCell>
                                                    </DataTemplate>
                                                </ListView.ItemTemplate>

                                            </ListView>

                                            <inputLayout:SfTextInputLayout  x:Name="SelectedQuantity"
                                                                            InputViewPadding="5"
                                                                            Margin="0"
                                                                            Hint="Menge:"
                                                                            ContainerType="Outlined"
                                                                            Grid.Row="1"
                                                                            Grid.Column="2"
                                                                            VerticalOptions="Start">

                                                <inputLayout:SfTextInputLayout.Triggers>
                                                    <DataTrigger TargetType="inputLayout:SfTextInputLayout"
                                                         Binding="{Binding SelectedToRemoveAdditionalAricle, TargetNullValue=''}"
                                                         Value="">
                                                        <Setter Property="IsVisible"
                                                        Value="False" />

                                                    </DataTrigger>
                                                </inputLayout:SfTextInputLayout.Triggers>

                                                <local:CustomEntry  Text="{Binding SelectedToRemoveAdditionalAricle.Amount,Converter={StaticResource StringDecimalConverter}}"
                                                            FontSize="Medium"
                                                            MaxLength="5">
                                                </local:CustomEntry>

                                            </inputLayout:SfTextInputLayout>

                                            <ImageButton    x:Name="RemoveButton"
                                                            Source="minusIcon.png"
                                                            BackgroundColor="Transparent"
                                                            Margin="5,0,10,5"
                                                            WidthRequest="40"
                                                            HeightRequest="40"
                                                            Grid.Row="1"
                                                            Grid.Column="1"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Start"
                                                            Command="{Binding RemoveArticleCommand}"
                                                            CommandParameter="{Binding SelectedToRemoveAdditionalAricle}">
                                                <ImageButton.Triggers>
                                                    <DataTrigger TargetType="ImageButton"
                                                                 Binding="{Binding SelectedToRemoveAdditionalAricle, TargetNullValue=''}"
                                                                 Value="">
                                                        <Setter Property="IsVisible" Value="False" />
                                                    </DataTrigger>

                                                    <DataTrigger TargetType="ImageButton"
                                                                 Binding="{Binding SelectedToRemoveAdditionalAricle.IsCreatedByApp}"
                                                                 Value="false">
                                                        <Setter Property="IsVisible" Value="False" />
                                                    </DataTrigger>
                                                </ImageButton.Triggers>

                                            </ImageButton>

                                        </Grid>
                                    </Frame>

                                </Grid>

                            </Frame>

                        </StackLayout>

                    </ScrollView>

                </tabView:SfTabItem.Content>

            </tabView:SfTabItem>

            <tabView:SfTabItem Title="Neue Gerät">

                <tabView:SfTabItem.Content>

                    <Grid>

                        <Frame  Margin="10,10,10,0"
                                BorderColor="LightGray"
                                Padding="0"
                                CornerRadius="0"
                                HasShadow="True">

                            <Grid RowSpacing="0">

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="50*"></ColumnDefinition>
                                    <ColumnDefinition Width="25*"></ColumnDefinition>
                                    <ColumnDefinition Width="25*"></ColumnDefinition>
                                </Grid.ColumnDefinitions>

                                <inputLayout:SfTextInputLayout  x:Name="NewDeviceNumber"
                                                                InputViewPadding="5"
                                                                Margin="5,0,0,0"
                                                                Hint="{markupExtensions:Localisation DeviceNumber}"
                                                                ContainerType="Outlined"
                                                                Grid.Row="0"
                                                                Grid.Column="0"
                                                                VerticalOptions="Center"
                                                                HasError="{Binding NewDeviceNumberHasError}"
                                                                ErrorText="{Binding NewDeviceNumberErrorText}">

                                    <Entry  Text="{Binding NewDeviceNumber}"
                                            VerticalOptions="End"
                                            FontSize="Medium">

                                    </Entry>

                                </inputLayout:SfTextInputLayout>

                                <StackLayout    x:Name="NewDeviceClass"
                                                Orientation="Horizontal"
                                                Margin="0,0,5,0"
                                                Grid.Row="0"
                                                Grid.Column="1"
                                                Grid.ColumnSpan="2"
                                                HorizontalOptions="End"
                                                Spacing="1">

                                    <Label  x:Name="NewDeviceClassLabel"
                                            Margin="5,0,0,0"
                                            Text="Neue RM"
                                            FontSize="20"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            TextColor="Black"
                                            FontAttributes="Bold">

                                    </Label>

                                    <Image  Margin="5,2,2,5"
                                            WidthRequest="40"
                                            HeightRequest="40"
                                            Source="{Binding NewDeviceClass, Converter={StaticResource DeviceClassTypeConverter}}">

                                    </Image>

                                </StackLayout>

                                <inputLayout:SfTextInputLayout  x:Name="NewRoom"
                                                                InputViewPadding="5"
                                                                Margin="5,0,0,0"
                                                                Hint="{markupExtensions:Localisation Room}"
                                                                ContainerType="Outlined"
                                                                Grid.Row="1"
                                                                Grid.Column="0"
                                                                VerticalOptions="Center"
                                                                HasError="{Binding NewSelectedRoomHasError}"
                                                                ErrorText="{Binding NewSelectedRoomErrorText}">

                                    <Picker ItemsSource="{Binding RoomList}"
                                            SelectedItem="{Binding NewSelectedRoom}"
                                            ItemDisplayBinding="{Binding Label}"
                                            VerticalOptions="End"
                                            FontSize="Medium">

                                    </Picker>

                                </inputLayout:SfTextInputLayout>

                                <inputLayout:SfTextInputLayout  x:Name="NewOngoingNumber"
                                                                InputViewPadding="5"
                                                                Margin="5,0,5,0"
                                                                Hint="{markupExtensions:Localisation OngoingNumber}"
                                                                ContainerType="Outlined"
                                                                Grid.Row="1"
                                                                Grid.Column="1"
                                                                Grid.ColumnSpan="2"
                                                                VerticalOptions="Center"
                                                                HasError="{Binding NewOngoingNumberHasError}"
                                                                ErrorText="{Binding NewOngoingNumberErrorText}">

                                    <Entry  Text="{Binding NewOngoingNumber}"
                                            Keyboard="Numeric"
                                            VerticalOptions="End"
                                            FontSize="Medium">

                                    </Entry>

                                </inputLayout:SfTextInputLayout>

                                <inputLayout:SfTextInputLayout  x:Name="NewArticleDescription"
                                                                InputViewPadding="5"
                                                                Margin="5,0,0,0"
                                                                Hint="{markupExtensions:Localisation Description}"
                                                                ContainerType="Outlined"
                                                                Grid.Row="2"
                                                                Grid.Column="1"
                                                                Grid.ColumnSpan="2"
                                                                VerticalOptions="Center"
                                                                HasError="{Binding NewSelectedDeviceCatalogHasError}"
                                                                ErrorText="{Binding NewSelectedDeviceCatalogErrorText}"
                                                                IsEnabled="{Binding IsDeviceCatalogEnabled}">

                                    <Picker  ItemsSource="{Binding DeviceCatalogList}"
                                             SelectedItem="{Binding NewSelectedDeviceCatalog}"
                                             ItemDisplayBinding="{Binding ArticleDescription}"
                                             FontSize="Medium">

                                    </Picker>

                                </inputLayout:SfTextInputLayout>

                                <inputLayout:SfTextInputLayout  x:Name="NewArticleNumber"
                                                                InputViewPadding="5"
                                                                Margin="5,0,5,0"
                                                                Hint="Artikel Nummer"
                                                                ContainerType="Outlined"
                                                                Grid.Row="2"
                                                                Grid.Column="0"
                                                                VerticalOptions="Center"
                                                                HasError="{Binding NewSelectedDeviceCatalogHasError}"
                                                                ErrorText="{Binding NewSelectedDeviceCatalogErrorText}"
                                                                IsEnabled="{Binding IsDeviceCatalogEnabled}">

                                    <Picker  ItemsSource="{Binding DeviceCatalogList}"
                                             SelectedItem="{Binding NewSelectedDeviceCatalog}"
                                             ItemDisplayBinding="{Binding ArticleNumber}"
                                             FontSize="Medium">

                                    </Picker>

                                </inputLayout:SfTextInputLayout>

                                <inputLayout:SfTextInputLayout  x:Name="NewCalibrationDate"
                                                                InputViewPadding="5"
                                                                Margin="5,0,5,0"
                                                                Hint="{markupExtensions:Localisation CalibrationDate}"
                                                                ContainerType="Outlined"
                                                                Grid.Row="3"
                                                                Grid.Column="1"
                                                                Grid.ColumnSpan="2"
                                                                VerticalOptions="Center">

                                    <Picker x:Name="NewEichdatum"
                                            ItemsSource="{Binding PickerYearList}"
                                            Title="Jahr auswählen:"
                                            SelectedItem="{Binding NewDeviceCalibrationDate}">

                                    </Picker>

                                </inputLayout:SfTextInputLayout>

                                <inputLayout:SfTextInputLayout  x:Name="NewInstalationDate"
                                                                ContainerType="Outlined"
                                                                TrailingViewPosition="Inside" 
                                                                InputViewPadding="5"
                                                                Margin="5,0,5,0"
                                                                Hint="{markupExtensions:Localisation InstallactionDate}"
                                                                Grid.Row="4"
                                                                Grid.Column="0"
                                                                VerticalOptions="Center"
                                                                HasError="{Binding NewDeviceInstallationDateHasError}"
                                                                ErrorText="{Binding NewDeviceInstallationDateErrorText}">

                                    <DatePicker Date="{Binding NewDeviceInstallationDate}"
                                                FontSize="Medium">

                                    </DatePicker>
                                </inputLayout:SfTextInputLayout>

                                <inputLayout:SfTextInputLayout  x:Name="DeinstallationDate"
                                                                InputViewPadding="5"
                                                                Margin="5,0,5,0"
                                                                ContainerType="Outlined"
                                                                Hint="{markupExtensions:Localisation DeinstallationDate}"
                                                                Grid.Row="4"
                                                                Grid.Column="1"
                                                                Grid.ColumnSpan="2"
                                                                VerticalOptions="Center"
                                                                IsEnabled="False"
                                                                IsVisible="False">

                                    <local:NullableDatePicker   VerticalOptions="End"
                                                                FontSize="Medium"
                                                                TextColor="LightGray"
                                                                IsEnabled="False">

                                    </local:NullableDatePicker>

                                </inputLayout:SfTextInputLayout>

                                <inputLayout:SfTextInputLayout  x:Name="NewReadingValue"
                                                                InputViewPadding="5"
                                                                Margin="5,0,5,0"
                                                                Hint="Ablesung"
                                                                ContainerType="Outlined"
                                                                Grid.Row="5"
                                                                Grid.Column="0"
                                                                VerticalOptions="Center"
                                                                IsEnabled="False">

                                    <syncfusion:SfNumericTextBox    Value="{Binding NewDeviceReading}" 
                                                                    VerticalOptions="End"
                                                                    FontSize="Medium"
                                                                    MaximumNumberDecimalDigits="3">

                                    </syncfusion:SfNumericTextBox>

                                </inputLayout:SfTextInputLayout>

                                <StackLayout    Orientation="Horizontal"
                                                Margin="0,0,5,10"
                                                Grid.Row="5"
                                                Grid.Column="1"
                                                Grid.ColumnSpan="2"
                                                HorizontalOptions="Start"
                                                Spacing="1">

                                    <CheckBox   HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                Color="Black"
                                                IsChecked="{Binding NewDeviceIsMaintained}">
                                    </CheckBox>

                                    <Label  Text="Wartung"
                                            Margin="2,0,0,0"
                                            FontSize="Medium"
                                            VerticalOptions="Center"
                                            TextColor="Black">

                                    </Label>

                                </StackLayout>

                                <inputLayout:SfTextInputLayout  x:Name="NewAmwKey"
                                                                InputViewPadding="5"
                                                                Grid.Row="6"
                                                                Grid.Column="0"
                                                                Grid.ColumnSpan="3"
                                                                Margin="5,0,5,0"
                                                                Hint="{markupExtensions:Localisation SelectError}"
                                                                ContainerType="Outlined"
                                                                HorizontalOptions="FillAndExpand">

                                    <Picker SelectedItem="{Binding NewSelectedAmwKey}"
                                            ItemsSource="{Binding AmwInfoKeyList}"
                                            ItemDisplayBinding="{Binding Info}"
                                            VerticalOptions="End"
                                            FontSize="Medium">
                                    </Picker>

                                </inputLayout:SfTextInputLayout>

                                <inputLayout:SfTextInputLayout  x:Name="NewDeviceNote"
                                                                InputViewPadding="5"
                                                                Margin="5,0,5,0"
                                                                Hint="Notiz:"
                                                                ContainerType="Outlined"
                                                                Grid.Row="7"
                                                                Grid.Column="0"
                                                                Grid.ColumnSpan="3"
                                                                VerticalOptions="Center">

                                    <Entry  Text="{Binding NewDeviceNote}"
                                            VerticalOptions="End"
                                            FontSize="Medium">

                                    </Entry>

                                </inputLayout:SfTextInputLayout>

                                <Frame  Margin="5,0,5,5"
                                        BorderColor="LightGray"
                                        Padding="10"
                                        CornerRadius="5"
                                        HasShadow="True"
                                        Grid.Row="8"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="3">

                                    <Frame.Triggers>
                                        <DataTrigger    TargetType="Frame"
                                                        Binding="{Binding AdditionalArticleList.Count}"
                                                        Value="0">

                                            <Setter Property="IsVisible" Value="False" />
                                        </DataTrigger>
                                    </Frame.Triggers>

                                    <Grid>

                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"></RowDefinition>
                                            <RowDefinition Height="Auto"></RowDefinition>
                                            <RowDefinition Height="Auto"></RowDefinition>
                                        </Grid.RowDefinitions>

                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="75*"></ColumnDefinition>
                                            <ColumnDefinition Width="60"></ColumnDefinition>
                                            <ColumnDefinition Width="20*"></ColumnDefinition>
                                        </Grid.ColumnDefinitions>

                                        <Label  Text="Zubehörteile Artikel"
                                                FontSize="Medium"
                                                FontAttributes="Bold"
                                                HorizontalOptions="Start"
                                                Margin="10,0,5,0"
                                                Grid.Row="0"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="2">

                                        </Label>

                                        <local:CustomEntry  Grid.Row="1"
                                                            Grid.Column="1"
                                                            Placeholder="#"
                                                            Text="{Binding NewAdditionalArticlesNumber}"
                                                            MaxLength="5"
                                                            FontSize="Medium"
                                                            HorizontalTextAlignment="Center"
                                                            VerticalTextAlignment="Center">

                                            <Entry.Triggers>
                                                <DataTrigger    TargetType="Entry"
                                                                Binding="{Binding Source={x:Reference newPickerArticlesList},Path=SelectedItem,Converter={StaticResource IsNullConverter}}"
                                                                Value="False">

                                                    <Setter Property="Text" Value="1" />
                                                </DataTrigger>

                                                <DataTrigger    TargetType="Entry"
                                                                Binding="{Binding Source={x:Reference newPickerArticlesList},Path=SelectedItem,TargetNullValue=''}"
                                                                Value="">
                                                    <Setter Property="Text" Value="" />
                                                </DataTrigger>

                                            </Entry.Triggers>
                                        </local:CustomEntry>

                                        <Picker x:Name="newPickerArticlesList"
                                                Margin="5,0,5,0"
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                ItemsSource="{Binding NewAdditionalArticleList}"
                                                SelectedItem="{Binding NewSelectedAdditionalArticle}"
                                                ItemDisplayBinding="{Binding Label}">

                                        </Picker>

                                        <ImageButton    Source="newIcon.png"
                                                        BackgroundColor="Transparent"
                                                        Margin="5,0,10,5"
                                                        WidthRequest="40"
                                                        HeightRequest="40"
                                                        Grid.Row="1"
                                                        Grid.Column="2"
                                                        VerticalOptions="Start"
                                                        HorizontalOptions="Start"
                                                        Command="{Binding AddNewArticleCommand}"
                                                        CommandParameter="{Binding NewSelectedAdditionalArticle}">

                                        </ImageButton>

                                    </Grid>

                                </Frame>

                                <Frame  Margin="5"
                                        BorderColor="LightGray"
                                        Padding="10"
                                        CornerRadius="5"
                                        HasShadow="True"
                                        Grid.Row="9"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="3">

                                    <Frame.Triggers>
                                        <DataTrigger    TargetType="Frame"
                                                        Binding="{Binding NewSelectedAdditionalArticleListCount}"
                                                        Value="0">
                                            <Setter Property="IsVisible" Value="False" />

                                        </DataTrigger>
                                    </Frame.Triggers>

                                    <Grid>

                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"></RowDefinition>
                                            <RowDefinition Height="Auto"></RowDefinition>
                                            <RowDefinition Height="Auto"></RowDefinition>
                                        </Grid.RowDefinitions>

                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="50*"></ColumnDefinition>
                                            <ColumnDefinition Width="20*"></ColumnDefinition>
                                            <ColumnDefinition Width="100"></ColumnDefinition>
                                        </Grid.ColumnDefinitions>

                                        <Label  Text="Hinzugefügte Zubehörteile:"
                                                FontSize="Medium"
                                                FontAttributes="Bold"
                                                HorizontalOptions="Start"
                                                Margin="10,0,5,0"
                                                Grid.Row="0"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="2">

                                        </Label>

                                        <ListView   HeightRequest="110"
                                                    Margin="5,0,5,0"
                                                    Grid.Row="1"
                                                    Grid.Column="0"
                                                    RowHeight="55"
                                                    SelectionMode="Single"
                                                    IsPullToRefreshEnabled="False"
                                                    HasUnevenRows="False"
                                                    CachingStrategy="RetainElement"
                                                    ItemsSource="{Binding NewSelectedAdditionalArticleList}"
                                                    SelectedItem="{Binding NewSelectedToRemoveAdditionalAricle}">

                                            <ListView.ItemTemplate>
                                                <DataTemplate>
                                                    <ViewCell>
                                                        <Frame  Margin="5"
                                                        BorderColor="LightGray"
                                                        Padding="0"
                                                        CornerRadius="0"
                                                        HasShadow="True">
                                                            <Grid>
                                                                <Grid.RowDefinitions>
                                                                    <RowDefinition Height="Auto"></RowDefinition>
                                                                </Grid.RowDefinitions>

                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="180"></ColumnDefinition>
                                                                    <ColumnDefinition Width="60"></ColumnDefinition>
                                                                </Grid.ColumnDefinitions>

                                                                <Label  Text="{Binding AdditionalArticle.Label}"
                                                                Grid.Row="0"
                                                                Grid.Column="0"
                                                                Margin="5"
                                                                FontSize="Small"
                                                                VerticalOptions="Center">
                                                                </Label>

                                                                <Label  Text="{Binding Amount}"
                                                                Grid.Row="0"
                                                                Grid.Column="1"
                                                                Margin="5"
                                                                FontSize="Small"
                                                                HorizontalOptions="End">
                                                                </Label>
                                                            </Grid>
                                                        </Frame>


                                                    </ViewCell>
                                                </DataTemplate>
                                            </ListView.ItemTemplate>

                                        </ListView>

                                        <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                                        Margin="0"
                                                                        Hint="Menge:"
                                                                        ContainerType="Outlined"
                                                                        Grid.Row="1"
                                                                        Grid.Column="2"
                                                                        VerticalOptions="Start">

                                            <inputLayout:SfTextInputLayout.Triggers>
                                                <DataTrigger    TargetType="inputLayout:SfTextInputLayout"
                                                                Binding="{Binding NewSelectedToRemoveAdditionalAricle, TargetNullValue=''}"
                                                                Value="">
                                                    <Setter Property="IsVisible"
                                                            Value="False" />

                                                </DataTrigger>
                                            </inputLayout:SfTextInputLayout.Triggers>

                                            <local:CustomEntry  Text="{Binding NewSelectedToRemoveAdditionalAricle.Amount,Converter={StaticResource StringDecimalConverter}}"
                                                        FontSize="Medium"
                                                        MaxLength="5">
                                            </local:CustomEntry>
                                        </inputLayout:SfTextInputLayout>

                                        <ImageButton    Source="minusIcon.png"
                                                        BackgroundColor="Transparent"
                                                        Margin="5,0,10,5"
                                                        WidthRequest="40"
                                                        HeightRequest="40"
                                                        Grid.Row="1"
                                                        Grid.Column="1"
                                                        VerticalOptions="Start"
                                                        HorizontalOptions="Start"
                                                        Command="{Binding RemoveNewArticleCommand}"
                                                        CommandParameter="{Binding NewSelectedToRemoveAdditionalAricle}">

                                            <ImageButton.Triggers>
                                                <DataTrigger    TargetType="ImageButton"
                                                                Binding="{Binding NewSelectedToRemoveAdditionalAricle, TargetNullValue=''}"
                                                                Value="">
                                                    <Setter Property="IsVisible" Value="False" />
                                                </DataTrigger>

                                                <DataTrigger    TargetType="ImageButton"
                                                                Binding="{Binding NewSelectedToRemoveAdditionalAricle.IsCreatedByApp}"
                                                                Value="false">
                                                    <Setter Property="IsVisible" Value="False" />
                                                </DataTrigger>
                                            </ImageButton.Triggers>

                                        </ImageButton>

                                    </Grid>

                                </Frame>

                            </Grid>

                        </Frame>

                    </Grid>

                </tabView:SfTabItem.Content>

            </tabView:SfTabItem>

        </tabView:SfTabView>

    </ContentPage.Content>
    
</ContentPage>