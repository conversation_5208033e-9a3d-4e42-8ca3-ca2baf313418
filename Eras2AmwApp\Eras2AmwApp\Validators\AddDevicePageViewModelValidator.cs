﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AddDevicePageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using FluentValidation;
    using ViewModels;
    using Eras2AmwApp.Common.Ioc;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using System.Linq;

    public class AddDevicePageViewModelValidator : AbstractValidator<AddDevicePageViewModel>
    {
        private readonly IDbContextFactory contextFactory = NinjectKernel.Get<IDbContextFactory>();

        public AddDevicePageViewModelValidator()
        {
            RuleFor(x => x.DeviceNumber)
                .Must(ValidateDeviceNumber)
                .WithMessage("Die Gerätenummer existiert bereits in dieser Nutzeinheit und kann nicht noch einmal vergeben werden.")
                .NotEmpty().WithMessage("Die Gerätenummer darf nicht leer sein.");

            RuleFor(x => x.OngoingNumber)
                .Must(ValidateOnGoingNumber)
                .WithMessage("Die Laufende Nummer existiert bereits in dieser Nutzeinheit und kann nicht noch einmal vergeben werden.") 
                .NotEmpty().WithMessage("Die Laufende Nummer darf nicht leer sein.");  

            RuleFor(x => x.SelectedRoom)
                .NotEmpty().WithMessage("Der Raum darf nicht leer sein.");

            RuleFor(x => x.SelectedDeviceKind)
                .NotEmpty().WithMessage("Die Gerätetyp darf nicht leer sein.");

            RuleFor(x => x.SelectedDeviceCatalog)
                .NotEmpty().WithMessage("Das Feld darf nicht leer sein.");
        }

        private bool ValidateOnGoingNumber(AddDevicePageViewModel device, string ongoingNumber)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return !context.Devices.Any(y => y.OngoingNumber == ongoingNumber && y.NutzeinheitGuid == device.NutzeinheitGuid);
            }
        }

        private bool ValidateDeviceNumber(AddDevicePageViewModel device, string deviceNumber)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return !context.Devices.Any(y => y.Number == deviceNumber && y.NutzeinheitGuid == device.NutzeinheitGuid);
            }
        }
    }
}
