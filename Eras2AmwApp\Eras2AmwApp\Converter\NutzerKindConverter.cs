﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitStatusConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Endiancode.Utilities.Extensions;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class NutzerKindConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string nutzerKind = "";

            if(value is NutzerKind kind)
            {
                nutzerKind = kind.GetDescription();
            }

            return nutzerKind;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
            {
                return null;
            }

            Enum.TryParse(value.ToString(), out NutzerKind nutzerKind);
            NutzerKind selectedKind = nutzerKind;

            return selectedKind;
        }
    }
}
