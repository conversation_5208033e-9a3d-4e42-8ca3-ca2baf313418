﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AdditionalArticleVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Models
{
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using GalaSoft.MvvmLight;

    public class AdditionalArticleVM : ViewModelBase
    {
        public AdditionalArticle AdditionalArticle { get; set; }

        public bool IsCreatedByApp { get; set;}

        private decimal _amount;
        public decimal Amount
        {
            get { return _amount; }
            set { Set(ref _amount, value); }
        }
    }
}
