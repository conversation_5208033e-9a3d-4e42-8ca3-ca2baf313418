﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0D59031E-194B-4C50-8D59-F354EC78BF9C}</ProjectGuid>
    <ProjectTypeGuids>{EFBA0AD7-5A72-4C68-AF49-83D382785DCF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TemplateGuid>{c9e5eea5-ca05-42a1-839b-61506e0a37df}</TemplateGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Eras2AmwApp.Droid</RootNamespace>
    <AssemblyName>Eras2AmwApp.Android</AssemblyName>
    <AndroidApplication>True</AndroidApplication>
    <AndroidResgenFile>Resources\Resource.designer.cs</AndroidResgenFile>
    <AndroidResgenClass>Resource</AndroidResgenClass>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <MonoAndroidResourcePrefix>Resources</MonoAndroidResourcePrefix>
    <MonoAndroidAssetsPrefix>Assets</MonoAndroidAssetsPrefix>
    <AndroidUseLatestPlatformSdk>false</AndroidUseLatestPlatformSdk>
    <TargetFrameworkVersion>v11.0</TargetFrameworkVersion>
    <AndroidEnableSGenConcurrent>true</AndroidEnableSGenConcurrent>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidHttpClientHandlerType>Xamarin.Android.Net.AndroidClientHandler</AndroidHttpClientHandlerType>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug</OutputPath>
    <DefineConstants>DEBUG;DEVELOPMENT</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidLinkMode>None</AndroidLinkMode>
    <AndroidSupportedAbis />
    <AndroidHttpClientHandlerType>Xamarin.Android.Net.AndroidClientHandler</AndroidHttpClientHandlerType>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <MandroidI18n />
    <JavaMaximumHeapSize>2G</JavaMaximumHeapSize>
    <Debugger>Xamarin</Debugger>
    <IntermediateOutputPath>C:\Users\<USER>\AppData\Local\Temp\vs7EA7.tmp\Debug\</IntermediateOutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>false</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidManagedSymbols>true</AndroidManagedSymbols>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
    <DefineConstants>
    </DefineConstants>
    <IntermediateOutputPath>C:\Users\<USER>\AppData\Local\Temp\vs7EA7.tmp\Release\</IntermediateOutputPath>
    <AndroidPackageFormat>aab</AndroidPackageFormat>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidCreatePackagePerAbi>false</AndroidCreatePackagePerAbi>
    <AndroidKeyStore>false</AndroidKeyStore>
    <EmbedAssembliesIntoApk>true</EmbedAssembliesIntoApk>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'StandaloneDevelopment|AnyCPU' ">
    <DebugSymbols>false</DebugSymbols>
    <OutputPath>bin\StandaloneDevelopment\</OutputPath>
    <Optimize>true</Optimize>
    <DebugType>portable</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <LangVersion>8.0</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <EmbedAssembliesIntoApk>true</EmbedAssembliesIntoApk>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DefineConstants>DEVELOPMENT,STANDALONE_APP</DefineConstants>
    <AndroidManifest>Properties\StandaloneDevelopmentAndroidManifest.xml</AndroidManifest>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
    <AndroidPackageFormat>aab</AndroidPackageFormat>
    <AndroidCreatePackagePerAbi>false</AndroidCreatePackagePerAbi>
    <AndroidSupportedAbis />
    <MandroidI18n />
    <IntermediateOutputPath>C:\Users\<USER>\AppData\Local\Temp\vs7EA7.tmp\StandaloneDevelopment\</IntermediateOutputPath>
    <AndroidKeyStore>false</AndroidKeyStore>
    <AndroidSigningKeyStore>C:\AzureDevOps\Keystore\DevelopmentRelease\Eras2AmwAppDevRelease.keystore</AndroidSigningKeyStore>
    <AndroidSigningKeyAlias>Eras2AmwAppDevRelease</AndroidSigningKeyAlias>
    <AndroidSigningStorePass>1aquT7dyvsX5</AndroidSigningStorePass>
    <AndroidSigningKeyPass>1aquT7dyvsX5</AndroidSigningKeyPass>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Development|AnyCPU'">
    <DebugSymbols>false</DebugSymbols>
    <OutputPath>bin\Development\</OutputPath>
    <DefineConstants>DEVELOPMENT</DefineConstants>
    <Optimize>true</Optimize>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DebugType>portable</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <LangVersion>8.0</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <AndroidManifest>Properties\DevelopmentAndroidManifest.xml</AndroidManifest>
    <IntermediateOutputPath>C:\Users\<USER>\AppData\Local\Temp\vs7EA7.tmp\Development\</IntermediateOutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'StandaloneRelease|AnyCPU'">
    <DebugSymbols>false</DebugSymbols>
    <OutputPath>bin\StandaloneRelease\</OutputPath>
    <Optimize>true</Optimize>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DebugType>portable</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <DefineConstants>STANDALONE_APP</DefineConstants>
    <AndroidManifest>Properties\StandaloneReleaseAndroidManifest.xml</AndroidManifest>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
    <IntermediateOutputPath>C:\Users\<USER>\AppData\Local\Temp\vs7EA7.tmp\StandaloneRelease\</IntermediateOutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Mono.Android" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Ninject">
      <Version>3.3.6</Version>
    </PackageReference>
    <PackageReference Include="Polly">
      <Version>8.5.2</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.Buttons">
      <Version>19.3.0.46</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.Cards">
      <Version>19.3.0.46</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.Core">
      <Version>19.3.0.46</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.Expander">
      <Version>19.3.0.46</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfBusyIndicator">
      <Version>19.3.0.46</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfCalendar">
      <Version>19.3.0.46</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfNumericTextBox">
      <Version>19.3.0.46</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfPopupLayout">
      <Version>19.3.0.46</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfSchedule">
      <Version>19.3.0.46</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfTabView">
      <Version>19.3.0.46</Version>
    </PackageReference>
    <PackageReference Include="Xam.Plugin.Media">
      <Version>5.0.1</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Android.Support.v4">
      <Version>28.0.0.3</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Android.Support.v7.AppCompat">
      <Version>28.0.0.3</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.AndroidX.Legacy.Support.V4">
      <Version>1.0.0.10</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.AndroidX.Lifecycle.LiveData">
      <Version>2.3.1.3</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Essentials">
      <Version>1.7.0</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Forms">
      <Version>5.0.0.2478</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Google.Android.Material">
      <Version>1.4.0.4</Version>
    </PackageReference>
    <PackageReference Include="ZXing.Net.Mobile">
      <Version>2.4.1</Version>
    </PackageReference>
    <PackageReference Include="ZXing.Net.Mobile.Forms">
      <Version>2.4.1</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CustomRenderers\CustomEntryRenderer.cs" />
    <Compile Include="CustomRenderers\NullableDatePickerRenderer.cs" />
    <Compile Include="Ioc\NinjectModules.cs" />
    <Compile Include="MainActivity.cs" />
    <AndroidAsset Include="Assets\test_eras2_amw.sqlite3" />
    <None Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Resources\Resource.designer.cs" />
    <Compile Include="Services\CloseApplicationService.cs" />
    <Compile Include="Services\PlatformService.cs" />
    <Compile Include="Services\PlatformServiceDevelopment.cs" />
    <Compile Include="SplashActivity.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\.editorconfig">
      <Link>.editorconfig</Link>
    </None>
    <None Include="Resources\AboutResources.txt" />
    <None Include="Assets\AboutAssets.txt" />
    <None Include="Properties\AndroidManifest.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\Tabbar.xml" />
    <AndroidResource Include="Resources\layout\Toolbar.xml" />
    <AndroidResource Include="Resources\values\styles.xml" />
    <AndroidResource Include="Resources\values\colors.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\icon.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\icon_round.xml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Endiancode.Utilities\Endiancode.Utilities.csproj">
      <Project>{5cc97221-1fe2-43ff-a161-b760ff354ed6}</Project>
      <Name>Endiancode.Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Eras2AmwApp.Bl\Eras2AmwApp.BusinessLogic.csproj">
      <Project>{E93FB02E-479F-4AD5-BAAF-9CE9202C5C95}</Project>
      <Name>Eras2AmwApp.BusinessLogic</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Eras2AmwApp.Common\Eras2AmwApp.Common.csproj">
      <Project>{bad6e1aa-1f82-35ea-2976-56e6fdacab92}</Project>
      <Name>Eras2AmwApp.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Eras2AmwApp\Eras2AmwApp.csproj">
      <Project>{4F49F36F-D656-460B-ACB3-E2C1B6981ABA}</Project>
      <Name>Eras2AmwApp</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <LinkDescription Include="LinkDescription.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\xml\file_paths.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\lockOpen.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\lockClosed.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\infoIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\downloadIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\deleteIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\settingsIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\editIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\maintainIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\moreIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\saveIcon.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\photoIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\barcodeIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\newIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\minusIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\overviewIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\splashscreen.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\splashLogo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\adaptiveIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\mipmap-hdpi\ic_launcher.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\mipmap-mdpi\ic_launcher.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\mipmap-xhdpi\ic_launcher.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\mipmap-xxhdpi\ic_launcher.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\mipmap-xxxhdpi\ic_launcher.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\eras_Logo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidEnvironment Include="environment.txt">
      <SubType>Designer</SubType>
    </AndroidEnvironment>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\newDateIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\photoGalleryIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\nelist.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\firedetector.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\coldmeter.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\warmmeter.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\confirmIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\signatureIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Properties\DevelopmentAndroidManifest.xml" />
    <None Include="Properties\ReleaseAndroidManifest.xml" />
    <None Include="Properties\StandaloneDevelopmentAndroidManifest.xml" />
    <None Include="Properties\StandaloneReleaseAndroidManifest.xml" />
    <AndroidResource Include="Resources\drawable\appDevelopmentIcon.png" />
    <AndroidResource Include="Resources\drawable\appReleaseIcon.png" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\iconStandalone.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\iconStandalone_round.xml" />
    <AndroidResource Include="Resources\drawable\appStandaloneDevelopmentIcon.png" />
    <AndroidResource Include="Resources\drawable\appStandaloneReleaseIcon.png" />
    <AndroidResource Include="Resources\drawable\adaptiveIconStandalone.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\wmzmeter.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\hkve.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\changeIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\szmeter.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\forwardIcon.png">
      <Generator>MSBuild:UpdateGeneratedFiles</Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\liveSyncOffIcon.png">
      <Generator>MSBuild:UpdateGeneratedFiles</Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\drawable\liveSyncOnIcon.png">
      <Generator>MSBuild:UpdateGeneratedFiles</Generator>
    </AndroidResource>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.CSharp.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties TriggeredFromHotReload="False" />
    </VisualStudio>
  </ProjectExtensions>
</Project>