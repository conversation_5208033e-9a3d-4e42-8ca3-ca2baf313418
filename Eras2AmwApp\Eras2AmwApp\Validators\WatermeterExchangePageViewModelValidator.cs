﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="WatermeterExchangePageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using FluentValidation;
    using ViewModels;
    using Eras2AmwApp.Common.Ioc;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using System.Linq;
    using System;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;

    public class WatermeterExchangePageViewModelValidator : AbstractValidator<WatermeterExchangePageViewModel>
    {
        private readonly IDbContextFactory contextFactory = NinjectKernel.Get<IDbContextFactory>();

        public WatermeterExchangePageViewModelValidator()
        {
            RuleFor(x => x.OldDeviceNumber)
                .NotEmpty().WithMessage("Die Gerätenummer darf nicht leer sein.");

            RuleFor(x => x.NewDeviceNumber)
                .Must(ValidateDeviceNumber)
                .WithMessage("Die Gerätenummer existiert bereits in dieser Nutzeinheit und kann nicht noch einmal vergeben werden.")
                .NotEmpty().WithMessage("Die Gerätenummer darf nicht leer sein.");

            RuleFor(x => x.OldDeviceMeasureUnit)
                .Must(ValidateUnit).WithMessage("Die Einheit darf nicht leer sein.");

            //RuleFor(x => x.NewOngoingNumber)
            //    .Must(ValidateOnGoingNumber)
            //    .WithMessage("Die Laufende Nummer existiert bereits in dieser Nutzeinheit und kann nicht noch einmal vergeben werden.")
            //    .NotEmpty().WithMessage("Die Laufende Nummer darf nicht leer sein.");

            RuleFor(x => x.NewSelectedRoom)
                .NotEmpty().WithMessage("Der Raum darf nicht leer sein.");

            RuleFor(x => x.NewSelectedDeviceKind)
                .NotEmpty().WithMessage("Die Gerätetyp darf nicht leer sein.");

            RuleFor(x => x.NewSelectedDeviceCatalog)
                .NotEmpty().WithMessage("Das Feld darf nicht leer sein.");

            RuleFor(x => x.OldDeviceDeinstallationDate)
                .NotNull()
                .WithMessage("Das Ausbaudatum darf nicht leer sein.");

            RuleFor(x => x.NewDeviceInstallationDate)
                .Must(ValidateCorrectDate)
                .WithMessage("Das neue Einzugsdatum muss größer sein als das vorherige Auszugsdatum.");

            RuleFor(x => x.NewDeviceMeasureUnit)
                .Must(ValidateUnit).WithMessage("Die Einheit darf nicht leer sein.");
        }

        //private bool ValidateOnGoingNumber(WatermeterExchangePageViewModel device, string ongoingNumber)
        //{
        //    using (Eras2AmwContext context = contextFactory.CreateAmw())
        //    {
        //        return !context.Devices.Any(y => y.OngoingNumber == ongoingNumber && y.NutzeinheitGuid == device.NutzeinheitGuid);
        //    }
        //}

        private bool ValidateDeviceNumber(WatermeterExchangePageViewModel device, string deviceNumber)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return !context.Devices.Any(y => y.Number == deviceNumber && y.NutzeinheitGuid == device.NutzeinheitGuid);
            }
        }

        private bool ValidateCorrectDate(WatermeterExchangePageViewModel device, DateTime newDeviceInstallationDate)
        {
            if(device.OldDeviceDeinstallationDate.HasValue)
            {
                return device.OldDeviceDeinstallationDate.Value < newDeviceInstallationDate;
            }

            return true;
        }

        private bool ValidateUnit(WatermeterExchangePageViewModel device, UnitKind? unit)
        {
            if (device.NewDeviceClass != DeviceClass.WMZ)
            {
                return true;
            }

            if (unit == UnitKind.KWH || unit == UnitKind.MWH)
            {
                return true;
            }
            return false;
        }
    }
}
