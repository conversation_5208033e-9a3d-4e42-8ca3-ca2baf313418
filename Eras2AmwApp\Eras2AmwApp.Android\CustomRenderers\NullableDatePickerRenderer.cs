﻿using System;
using Xamarin.Forms;
using Xamarin.Forms.Platform.Android;
using Android.App;
using Android.Widget;
using System.ComponentModel;
using Eras2AmwApp.Droid.CustomRenderers;
using Eras2AmwApp.CustomControls;
using Android.Content;

[assembly: ExportRenderer(typeof(NullableDatePicker), typeof(NullableDatePickerRenderer))]
namespace Eras2AmwApp.Droid.CustomRenderers
{


    public class NullableDatePickerRenderer : ViewRenderer<NullableDatePicker, EditText>
    {
        DatePickerDialog _dialog;

        public NullableDatePickerRenderer(Context context) : base(context)
        {
        }

        protected override void OnElementChanged(ElementChangedEventArgs<NullableDatePicker> e)
        {
            base.OnElementChanged(e);

            this.SetNativeControl(new EditText(Context));
            if (Control == null || e.NewElement == null)
                return;

            Control.Click += OnPickerClick;
            if(Element.NullableDate != null)
            {
                this.Control.Text = Element.Date.ToString(Element.Format);
            }
            else
            {
                Control.Text = "tt.mm.jjjj";
            }
            Control.KeyListener = null;
            Control.FocusChange += OnPickerFocusChange;
            Control.Enabled = Element.IsEnabled;

        }

        protected override void OnElementPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            base.OnElementPropertyChanged(sender, e);

            if (e.PropertyName == Xamarin.Forms.DatePicker.DateProperty.PropertyName ||
                e.PropertyName == Xamarin.Forms.DatePicker.FormatProperty.PropertyName ||
                e.PropertyName == NullableDatePicker.NullableDateProperty.PropertyName)
            {
                if (Element.NullableDate != null)
                {
                    SetDate(Element.Date);
                }
                else
                {
                    Control.Text = "tt.mm.jjjj";
                }
            }
        }

        void OnPickerFocusChange(object sender, FocusChangeEventArgs e)
        {
            if (e.HasFocus)
            {
                ShowDatePicker();
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (Control != null)
            {
                this.Control.Click -= OnPickerClick;
                this.Control.FocusChange -= OnPickerFocusChange;

                if (_dialog != null)
                {
                    _dialog.Hide();
                    _dialog.Dispose();
                    _dialog = null;
                }
            }

            base.Dispose(disposing);
        }

        void OnPickerClick(object sender, EventArgs e)
        {
            ShowDatePicker();
        }

        void SetDate(DateTime date)
        {
            this.Control.Text = date.ToString("d");
            Element.Date = date;
        }

        private void ShowDatePicker()
        {
            CreateDatePickerDialog(Element.Date.Year, Element.Date.Month - 1, Element.Date.Day);
            _dialog.Show();
        }

        void CreateDatePickerDialog(int year, int month, int day)
        {
            NullableDatePicker view = Element;
            _dialog = new DatePickerDialog(Context, (o, e) =>
            {
                view.Date = e.Date;
                ((IElementController)view).SetValueFromRenderer(VisualElement.IsFocusedProperty, false);
                Control.ClearFocus();

                _dialog = null;
            }, year, month, day);

            _dialog.SetButton("Ok", (sender, e) =>
            {
                SetDate(_dialog.DatePicker.DateTime);
                Element.Format = Element._originalFormat;
                Element.AssignValue();
            });
            _dialog.SetButton2("Löschen", (sender, e) =>
            {
                Element.CleanDate();
                Control.Text = Element.Format;
            });
        }
    }
}