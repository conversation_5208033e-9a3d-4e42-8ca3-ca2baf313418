﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="StatusConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using Xamarin.Forms;

    public class OrderKindConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            List<OrderKind> orderKind = (List<OrderKind>)value;
            Color appointmentColor = Color.Gray;

            if (orderKind.Contains(OrderKind.Assembly) && orderKind.Contains(OrderKind.Maintenance))
            {
                appointmentColor = Color.FromHex("#538EEC");
            }
            else if (orderKind.Contains(OrderKind.Assembly))
            {
                appointmentColor = Color.FromHex("#009688");
            }
            else if(orderKind.Contains(OrderKind.Maintenance))
            {
                appointmentColor = Color.Red;
            }

            return appointmentColor;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
