﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceDataTemplateSelector.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.DataTemplates
{
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Models;
    using Xamarin.Forms;

    public class DeviceDataTemplateSelector : DataTemplateSelector
    {
        public DataTemplate RauchmelderTemplate { get; set; }

        public DataTemplate WatermeterTemplate { get; set; }

        public DataTemplate TestmelderTemplate { get; set; }

        protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
        {
            DataTemplate dataTemplate = TestmelderTemplate;

            if (item is DeviceVM deviceVM)
            {
                if(deviceVM.Device is Rauchmelder)
                {
                    dataTemplate = RauchmelderTemplate;
                }
                else if(deviceVM.Device is Watermeter)
                {
                    dataTemplate = WatermeterTemplate;
                }
            }
            return dataTemplate;
        }
    }
}
