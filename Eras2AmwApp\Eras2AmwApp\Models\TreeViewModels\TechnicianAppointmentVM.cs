﻿//  <copyright file="TechnicianAppointmentVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Models.TreeViewModels
{
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using GalaSoft.MvvmLight;
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;

    public class TechnicianAppointmentVM : ViewModelBase
    {
        private string _fileName;
        public string FileName
        {
            get { return _fileName; }
            set { Set(ref _fileName, value); }
        }

        private List<Nutzer> _nutzerList;
        public List<Nutzer> NutzerList
        {
            get { return _nutzerList; }
            set { Set(ref _nutzerList, value); }
        }

        private ObservableCollection<TechnicianAppointmentVM> _appointmentTermine;
        public ObservableCollection<TechnicianAppointmentVM> AppointmentTermine
        {
            get { return _appointmentTermine; }
            set { Set(ref _appointmentTermine, value); }
        }

        public Appointment Appointment { get; set; }

        private Guid _nutzerGuid;
        public Guid NutzerGuid
        {
            get { return _nutzerGuid; }
            set { Set(ref _nutzerGuid, value); }
        }
    }
}
