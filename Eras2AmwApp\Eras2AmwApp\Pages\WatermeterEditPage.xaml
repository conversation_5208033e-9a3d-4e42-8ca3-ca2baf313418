﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:inputLayout="clr-namespace:Syncfusion.XForms.TextInputLayout;assembly=Syncfusion.Core.XForms"
             xmlns:converter="clr-namespace:Eras2AmwApp.Converter"
             xmlns:border="clr-namespace:Syncfusion.XForms.Border;assembly=Syncfusion.Core.XForms"
             xmlns:local="clr-namespace:Eras2AmwApp.CustomControls"
             xmlns:syncfusion="clr-namespace:Syncfusion.SfNumericTextBox.XForms;assembly=Syncfusion.SfNumericTextBox.XForms"
             x:Class="Eras2AmwApp.Pages.WatermeterEditPage">

    <NavigationPage.TitleView>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="50*"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <ImageButton    Source="changeIcon.png"
                            Grid.Column="1"
                            BackgroundColor="Transparent"
                            HorizontalOptions="End"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            Command="{Binding ChangeDeviceOrderKindCommand}">

            </ImageButton>

            <ImageButton    Source="deleteIcon.png"
                            Grid.Column="2"
                            BackgroundColor="Transparent"
                            HorizontalOptions="End"
                            IsEnabled="{Binding IsDeviceVisible}"
                            IsVisible="{Binding IsDeviceVisible}"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            Command="{Binding DeleteDeviceCommand}">

            </ImageButton>

            <ImageButton    Source="barcodeIcon.png"
                            Grid.Column="3"
                            BackgroundColor="Transparent"
                            HorizontalOptions="Start"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            Command="{Binding ReadBarcodCommand}">

            </ImageButton>

            <ImageButton    Source="photoIcon.png"
                            Grid.Column="4"
                            BackgroundColor="Transparent"
                            HorizontalOptions="End"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            Command="{Binding PhotoCommand}">

            </ImageButton>

            <ImageButton    Source="saveIcon.png"
                            Grid.Column="5"
                            BackgroundColor="Transparent"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            HorizontalOptions="End"
                            Command="{Binding SaveDeviceCommand}">
            </ImageButton>

        </Grid>
        
    </NavigationPage.TitleView>

    <ContentPage.Resources>

        <converter:IsNullConverter x:Key="IsNullConverter"></converter:IsNullConverter>
        <converter:StringDecimalConverter x:Key="StringDecimalConverter"></converter:StringDecimalConverter>
        <converter:DeviceClassTypeConverter x:Key="DeviceClassTypeConverter"></converter:DeviceClassTypeConverter>
        <converter:IsDeviceHkvvConverter x:Key="IsDeviceHkvvConverter"></converter:IsDeviceHkvvConverter>
        <converter:DeviceUnitConverter x:Key="DeviceUnitConverter"></converter:DeviceUnitConverter>
        <converter:DeviceOrderKindValueConverter x:Key="DeviceOrderKindValueConverter"></converter:DeviceOrderKindValueConverter>
    </ContentPage.Resources>

    <ContentPage.Content>
        
        <ScrollView>
            
            <StackLayout>

                <Label  x:Name="SignatureErrorText"
                        Text="Das Gerät muss gewartet/geprüft werden oder es muss ein Fehler ausgewählt werden!"
                        FontAttributes="Bold"
                        FontSize="Medium"
                        HorizontalTextAlignment="Center"
                        TextColor="Red"
                        IsVisible="{Binding ShowMaintainOrErrorWarning}"
                        IsEnabled="{Binding ShowMaintainOrErrorWarning}">

                </Label>

                <Frame  Margin="10,10,10,5"
                        BorderColor="LightGray"
                        Padding="0"
                        CornerRadius="0"
                        HasShadow="True">

                    <Grid RowSpacing="0">

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                            <ColumnDefinition Width="25*"></ColumnDefinition>
                            <ColumnDefinition Width="25*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <inputLayout:SfTextInputLayout  x:Name="DeviceNumber"
                                                        InputViewPadding="5"
                                                        Margin="5,0,0,0"
                                                        Hint="{markupExtensions:Localisation DeviceNumber}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="0"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding DeviceNumberHasError}"
                                                        ErrorText="{Binding DeviceNumberErrorText}">

                            <Entry  Text="{Binding DeviceNumber}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <StackLayout    Orientation="Horizontal"
                                        Margin="0,0,5,0"
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        Grid.ColumnSpan="2"
                                        HorizontalOptions="End"
                                        Spacing="1">

                            <Label  x:Name="DeviceClassLabel"
                                    Margin="5,0,0,0"
                                    Text="{Binding DeviceKind}"
                                    FontSize="20"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    TextColor="Black"
                                    FontAttributes="Bold">

                            </Label>

                            <Image  Margin="5,2,2,5"
                                    WidthRequest="40"
                                    HeightRequest="40"
                                    Source="{Binding DeviceClass, Converter={StaticResource DeviceClassTypeConverter}}">

                            </Image>

                        </StackLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Room"
                                                            InputViewPadding="5"
                                                            Margin="5,0,0,0"
                                                            Hint="{markupExtensions:Localisation Room}"
                                                            ContainerType="Outlined"
                                                            Grid.Row="1"
                                                            Grid.Column="0"
                                                            VerticalOptions="Center"
                                                            HasError="{Binding SelectedRoomHasError}"
                                                            ErrorText="{Binding SelectedRoomErrorText}">

                            <Picker ItemsSource="{Binding RoomList}"
                                        SelectedItem="{Binding SelectedRoom}"
                                        ItemDisplayBinding="{Binding Label}"
                                        VerticalOptions="End"
                                        FontSize="Medium">

                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="OngoingNumber"
                                                            InputViewPadding="5"
                                                            Margin="5,0,5,0"
                                                            Hint="Laufende Nummer"
                                                            ContainerType="Outlined"
                                                            Grid.Row="1"
                                                            Grid.Column="1"
                                                            Grid.ColumnSpan="2"
                                                            VerticalOptions="Center"
                                                            IsEnabled="False">

                            <Entry  Text="{Binding OngoingNumber}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="DeviceDescription"
                                                            InputViewPadding="5"
                                                            Margin="5,0,0,0"
                                                            Hint="Bezeichnung"
                                                            ContainerType="Outlined"
                                                            Grid.Row="2"
                                                            Grid.Column="1"
                                                            Grid.ColumnSpan="2"
                                                            VerticalOptions="Center"
                                                            IsEnabled="False">

                            <Entry  Text="{Binding ArticleDescription}"
                                        FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="ArticleNumber"
                                                            InputViewPadding="5"
                                                            Margin="5,0,5,0"
                                                            Hint="Artikel Nummer"
                                                            ContainerType="Outlined"
                                                            Grid.Row="2"
                                                            Grid.Column="0"
                                                            VerticalOptions="Center"
                                                            IsEnabled="False">

                            <Entry  Text="{Binding ArticleNumber}"
                                        FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="DeviceMeasureUnit"
                                                        InputViewPadding="5"
                                                        Margin="5,0,5,0"
                                                        Hint="Einheit"
                                                        ContainerType="Outlined"
                                                        Grid.Row="3"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Picker ItemsSource="{Binding UnitKindList}"
                                    Title="Einheit type:"
                                    SelectedItem="{Binding DeviceMeasureUnit, Converter={StaticResource DeviceUnitConverter}}">

                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="CalibrationDate"
                                                        InputViewPadding="5"
                                                        Margin="5,0,5,0"
                                                        ContainerType="Outlined"
                                                        Hint="{markupExtensions:Localisation CalibrationDate}"
                                                        Grid.Row="3"
                                                        Grid.Column="1"
                                                        Grid.ColumnSpan="2"
                                                        VerticalOptions="Center">

                            <Picker x:Name="Eichdatum"
                                        ItemsSource="{Binding PickerYearList}"
                                        Title="Jahr auswählen:"
                                        SelectedItem="{Binding DeviceCalibrationDate}">

                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="InstallationDate"
                                                        InputViewPadding="5"
                                                        ContainerType="Outlined"
                                                        Margin="5,0,5,0"
                                                        Hint="{markupExtensions:Localisation InstallactionDate}"
                                                        Grid.Row="4"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <DatePicker Date="{Binding DeviceInstallationDate}"
                                            VerticalOptions="End"
                                            FontSize="Medium">

                            </DatePicker>

                            <inputLayout:SfTextInputLayout.TrailingView>

                                <border:SfBorder    WidthRequest="70"
                                                    CornerRadius="10" 
                                                    HeightRequest="30"   
                                                    BackgroundColor="#538EEC">

                                    <Label  TextColor="White"
                                                VerticalTextAlignment="Center" 
                                                HorizontalTextAlignment="Center" 
                                                Text="heute">

                                        <Label.GestureRecognizers>
                                            <TapGestureRecognizer   Command="{Binding SetInstallationDateCommand}"
                                                                        NumberOfTapsRequired="1" />
                                        </Label.GestureRecognizers>
                                    </Label>
                                </border:SfBorder>
                            </inputLayout:SfTextInputLayout.TrailingView>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="DeinstallationDate"
                                                            InputViewPadding="5"
                                                            Margin="5,0,5,0"
                                                            ContainerType="Outlined"
                                                            Hint="{markupExtensions:Localisation DeinstallationDate}"
                                                            Grid.Row="4"
                                                            Grid.Column="1"
                                                            Grid.ColumnSpan="2"
                                                            VerticalOptions="Center">

                            <local:NullableDatePicker   NullableDate="{Binding DeviceDeinstallationDate}"
                                                            VerticalOptions="End"
                                                            FontSize="Medium">

                            </local:NullableDatePicker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="ReadingDate"
                                                        InputViewPadding="5"
                                                        ContainerType="Outlined"
                                                        Margin="5,0,5,0"
                                                        Hint="Ablesedatum"
                                                        Grid.Row="5"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <DatePicker Date="{Binding DeviceReadingDate}"
                                        VerticalOptions="End"
                                        FontSize="Medium">

                            </DatePicker>

                            <inputLayout:SfTextInputLayout.TrailingView>

                                <border:SfBorder    WidthRequest="70"
                                                    CornerRadius="10" 
                                                    HeightRequest="30"   
                                                    BackgroundColor="#538EEC">

                                    <Label  TextColor="White"
                                            VerticalTextAlignment="Center" 
                                            HorizontalTextAlignment="Center" 
                                            Text="heute">

                                        <Label.GestureRecognizers>
                                            <TapGestureRecognizer   Command="{Binding SetReadingDateCommand}"
                                                                    NumberOfTapsRequired="1" />
                                        </Label.GestureRecognizers>
                                    </Label>
                                </border:SfBorder>
                            </inputLayout:SfTextInputLayout.TrailingView>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="ReadingValue"
                                                        InputViewPadding="5"
                                                        Margin="5,0,5,0"
                                                        Hint="{Binding PreviousDeviceReading}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="5"
                                                        Grid.Column="1"
                                                        Grid.ColumnSpan="2"
                                                        VerticalOptions="Center">

                            <syncfusion:SfNumericTextBox    Value="{Binding DeviceReading}" 
                                                            VerticalOptions="End"
                                                            FontSize="Medium"
                                                            MaximumNumberDecimalDigits="3"
                                                            AllowNull="True">
                                <syncfusion:SfNumericTextBox.Triggers>
                                    <DataTrigger    TargetType="syncfusion:SfNumericTextBox"
                                                                Binding="{Binding DeviceClass,Converter={StaticResource IsDeviceHkvvConverter}}"
                                                                Value="True">
                                        <Setter Property="MaximumNumberDecimalDigits" Value="0"/>
                                        <Setter Property="Value" Value="0"/>
                                    </DataTrigger>
                                </syncfusion:SfNumericTextBox.Triggers>
                            </syncfusion:SfNumericTextBox>

                        </inputLayout:SfTextInputLayout>

                        <StackLayout    Orientation="Horizontal"
                                        Margin="0,0,5,10"
                                        Grid.Row="6"
                                        Grid.Column="1"
                                        Grid.ColumnSpan="2"
                                        HorizontalOptions="Start"
                                        Spacing="1">

                            <CheckBox   x:Name="maintainSwitch"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        Color="Gray"
                                        IsChecked="{Binding IsMaintained}"
                                        IsEnabled="False">
                                <CheckBox.Triggers>
                                    <DataTrigger    TargetType="CheckBox"
                                                    Binding="{Binding DeviceOrderKind, Converter={StaticResource DeviceOrderKindValueConverter}}"
                                                    Value="Inspection">
                                        <Setter Property="IsEnabled"
                                                Value="True" />
                                    </DataTrigger>
                                </CheckBox.Triggers>
                            </CheckBox>

                            <Label  Text="Wartung/Prüfung"
                                    Margin="2,0,0,0"
                                    FontSize="Medium"
                                    VerticalOptions="Center">

                            </Label>

                        </StackLayout>

                        <inputLayout:SfTextInputLayout  x:Name="AmwKey"
                                                        InputViewPadding="5"
                                                        Grid.Row="7"
                                                        Grid.Column="0"
                                                        Grid.ColumnSpan="3"
                                                        Margin="5,0,5,0"
                                                        Hint="{markupExtensions:Localisation SelectError}"
                                                        ContainerType="Outlined"
                                                        HorizontalOptions="FillAndExpand">

                            <Picker SelectedItem="{Binding SelectedAmwKey}"
                                    ItemsSource="{Binding AmwInfoKeyList}"
                                    ItemDisplayBinding="{Binding Info}"
                                    VerticalOptions="End"
                                    FontSize="Medium">
                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Note"
                                                        InputViewPadding="5"
                                                        Margin="5,0,5,0"
                                                        Hint="Notiz:"
                                                        ContainerType="Outlined"
                                                        Grid.Row="8"
                                                        Grid.Column="0"
                                                        Grid.ColumnSpan="3"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding DeviceNote}"
                                        VerticalOptions="End"
                                        FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <Frame  Margin="5,0,5,5"
                                BorderColor="LightGray"
                                Padding="10"
                                CornerRadius="5"
                                HasShadow="True"
                                Grid.Row="9"
                                Grid.Column="0"
                                Grid.ColumnSpan="3">

                            <Frame.Triggers>
                                <DataTrigger TargetType="Frame"
                                                 Binding="{Binding AdditionalArticleList.Count}"
                                                 Value="0">
                                    <Setter Property="IsVisible"
                                                Value="False" />

                                </DataTrigger>
                            </Frame.Triggers>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="75*"></ColumnDefinition>
                                    <ColumnDefinition Width="60"></ColumnDefinition>
                                    <ColumnDefinition Width="20*"></ColumnDefinition>
                                </Grid.ColumnDefinitions>

                                <Label Text="Zubehörteile Artikel"
                                           FontSize="Medium"
                                           FontAttributes="Bold"
                                           HorizontalOptions="Start"
                                           Margin="10,0,5,0"
                                           Grid.Row="0"
                                           Grid.Column="0"
                                           Grid.ColumnSpan="2">

                                </Label>

                                <local:CustomEntry
                                           Grid.Row="1"
                                           Grid.Column="1"
                                           Placeholder="#"
                                           Text="{Binding AdditionalArticlesNumber}"
                                           MaxLength="5"
                                           FontSize="Medium"
                                           HorizontalTextAlignment="Center"
                                           VerticalTextAlignment="Center">

                                    <Entry.Triggers>
                                        <DataTrigger    TargetType="Entry"
                                                        Binding="{Binding Source={x:Reference pickerArticlesList},Path=SelectedItem,Converter={StaticResource IsNullConverter}}"
                                                        Value="False">
                                            <Setter Property="Text"
                                                        Value="1" />
                                        </DataTrigger>

                                        <DataTrigger TargetType="Entry"
                                                         Binding="{Binding Source={x:Reference pickerArticlesList},Path=SelectedItem,TargetNullValue=''}"
                                                         Value="">
                                            <Setter Property="Text"
                                                        Value="" />
                                        </DataTrigger>

                                    </Entry.Triggers>
                                </local:CustomEntry>

                                <Picker x:Name="pickerArticlesList"
                                            Margin="5,0,5,0"
                                            Grid.Row="1"
                                            Grid.Column="0"
                                            ItemsSource="{Binding AdditionalArticleList}"
                                            SelectedItem="{Binding SelectedAdditionalArticle}"
                                            ItemDisplayBinding="{Binding Label}">

                                </Picker>

                                <ImageButton    x:Name="AddButton"
                                                    Source="newIcon.png"
                                                    BackgroundColor="Transparent"
                                                    Margin="5,0,10,5"
                                                    WidthRequest="40"
                                                    HeightRequest="40"
                                                    Grid.Row="1"
                                                    Grid.Column="2"
                                                    VerticalOptions="Start"
                                                    HorizontalOptions="Start"
                                                    Command="{Binding AddArticleCommand}"
                                                    CommandParameter="{Binding SelectedAdditionalArticle}">

                                </ImageButton>

                            </Grid>

                        </Frame>

                        <Frame  Margin="5"
                                BorderColor="LightGray"
                                Padding="10"
                                CornerRadius="5"
                                HasShadow="True"
                                Grid.Row="10"
                                Grid.Column="0"
                                Grid.ColumnSpan="3">

                            <Frame.Triggers>
                                <DataTrigger TargetType="Frame"
                                                 Binding="{Binding SelectedAdditionalArticleListCount}"
                                                 Value="0">
                                    <Setter Property="IsVisible"
                                                Value="False" />

                                </DataTrigger>
                            </Frame.Triggers>

                            <Grid>

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="50*"></ColumnDefinition>
                                    <ColumnDefinition Width="20*"></ColumnDefinition>
                                    <ColumnDefinition Width="100"></ColumnDefinition>
                                </Grid.ColumnDefinitions>

                                <Label Text="Hinzugefügte Zubehörteile:"
                                           FontSize="Medium"
                                           FontAttributes="Bold"
                                           HorizontalOptions="Start"
                                           Margin="10,0,5,0"
                                           Grid.Row="0"
                                           Grid.Column="0"
                                           Grid.ColumnSpan="2">

                                </Label>

                                <ListView x:Name="selectedArticlesList"
                                              HeightRequest="110"
                                              Margin="5,0,5,0"
                                              Grid.Row="1"
                                              Grid.Column="0"
                                              RowHeight="55"
                                              SelectionMode="Single"
                                              IsPullToRefreshEnabled="False"
                                              HasUnevenRows="False"
                                              CachingStrategy="RetainElement"
                                              ItemsSource="{Binding SelectedAdditionalArticleList}"
                                              SelectedItem="{Binding SelectedToRemoveAdditionalAricle}">

                                    <ListView.ItemTemplate>
                                        <DataTemplate>
                                            <ViewCell>
                                                <Frame  Margin="5"
                                                            BorderColor="LightGray"
                                                            Padding="0"
                                                            CornerRadius="0"
                                                            HasShadow="True">

                                                    <Grid>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition Height="Auto"></RowDefinition>
                                                        </Grid.RowDefinitions>

                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="180"></ColumnDefinition>
                                                            <ColumnDefinition Width="60"></ColumnDefinition>
                                                        </Grid.ColumnDefinitions>

                                                        <Label  Text="{Binding AdditionalArticle.Label}"
                                                                Grid.Row="0"
                                                                Grid.Column="0"
                                                                Margin="5"
                                                                FontSize="Small"
                                                                VerticalOptions="Center">
                                                        </Label>

                                                        <Label  Text="{Binding Amount}"
                                                                Grid.Row="0"
                                                                Grid.Column="1"
                                                                Margin="5"
                                                                FontSize="Small"
                                                                HorizontalOptions="End">
                                                        </Label>
                                                    </Grid>
                                                </Frame>


                                            </ViewCell>
                                        </DataTemplate>
                                    </ListView.ItemTemplate>

                                </ListView>

                                <inputLayout:SfTextInputLayout  x:Name="SelectedQuantity"
                                                                    InputViewPadding="5"
                                                                    Margin="0"
                                                                    Hint="Menge:"
                                                                    ContainerType="Outlined"
                                                                    Grid.Row="1"
                                                                    Grid.Column="2"
                                                                    VerticalOptions="Start">

                                    <inputLayout:SfTextInputLayout.Triggers>
                                        <DataTrigger TargetType="inputLayout:SfTextInputLayout"
                                                         Binding="{Binding SelectedToRemoveAdditionalAricle, TargetNullValue=''}"
                                                         Value="">
                                            <Setter Property="IsVisible"
                                                        Value="False" />

                                        </DataTrigger>
                                    </inputLayout:SfTextInputLayout.Triggers>

                                    <local:CustomEntry  Text="{Binding SelectedToRemoveAdditionalAricle.Amount,Converter={StaticResource StringDecimalConverter}}"
                                                            FontSize="Medium"
                                                            MaxLength="5">
                                    </local:CustomEntry>
                                </inputLayout:SfTextInputLayout>

                                <ImageButton    x:Name="RemoveButton"
                                                    Source="minusIcon.png"
                                                    BackgroundColor="Transparent"
                                                    Margin="5,0,10,5"
                                                    WidthRequest="40"
                                                    HeightRequest="40"
                                                    Grid.Row="1"
                                                    Grid.Column="1"
                                                    VerticalOptions="Start"
                                                    HorizontalOptions="Start"
                                                    Command="{Binding RemoveArticleCommand}"
                                                    CommandParameter="{Binding SelectedToRemoveAdditionalAricle}">
                                    <ImageButton.Triggers>
                                        <DataTrigger TargetType="ImageButton"
                                                         Binding="{Binding SelectedToRemoveAdditionalAricle, TargetNullValue=''}"
                                                         Value="">
                                            <Setter Property="IsVisible" Value="False" />
                                        </DataTrigger>

                                        <DataTrigger TargetType="ImageButton"
                                                         Binding="{Binding SelectedToRemoveAdditionalAricle.IsCreatedByApp}"
                                                         Value="false">
                                            <Setter Property="IsVisible" Value="False" />
                                        </DataTrigger>
                                    </ImageButton.Triggers>

                                </ImageButton>

                            </Grid>

                        </Frame>

                    </Grid>

                </Frame>

            </StackLayout>

        </ScrollView>
        
    </ContentPage.Content>
    
</ContentPage>