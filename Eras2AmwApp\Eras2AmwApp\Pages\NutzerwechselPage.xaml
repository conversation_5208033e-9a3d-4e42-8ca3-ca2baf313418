﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:inputLayout="clr-namespace:Syncfusion.XForms.TextInputLayout;assembly=Syncfusion.Core.XForms"
             xmlns:converter="clr-namespace:Eras2AmwApp.Converter"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.NutzerwechselPage">
    
    <ContentPage.Resources>
        <converter:DateTimeConverter x:Key="DateTimeConverter"></converter:DateTimeConverter>
    </ContentPage.Resources>
    
    <ContentPage.Content>
        <ScrollView>

            <Grid RowSpacing="0">
                
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="50*"></ColumnDefinition>
                    <ColumnDefinition Width="50*"></ColumnDefinition>
                </Grid.ColumnDefinitions>

                <Frame  Margin="5,5,5,5"
                        BorderColor="Gray" 
                        CornerRadius="5"
                        HasShadow="True"
                        Grid.Row="0" Grid.ColumnSpan="2">

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <Label  Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2"
                                Text="{markupExtensions:Localisation OldNutzer}"
                                FontSize="Large"
                                FontAttributes="Bold"
                                HorizontalOptions="Center"
                                TextColor="Black">
                        </Label>

                        <inputLayout:SfTextInputLayout  x:Name="Nutzer"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        ContainerType="Outlined"  
                                                        Hint="Nutzer:"
                                                        Grid.Row="1" Grid.Column="0"
                                                        VerticalOptions="Center"
                                                        IsEnabled="False">

                            <Entry  Text="{Binding OldNutzerNameTitleSalutation}" 
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Kind"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Kind}"
                                                        ContainerType="Outlined"  
                                                        Grid.Row="1" Grid.Column="1"
                                                        VerticalOptions="Center"
                                                        IsEnabled="False">

                            <Entry  Text="{Binding OldNutzerKind}" 
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Contact"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Contact}"
                                                        ContainerType="Outlined"  
                                                        Grid.Row="2" Grid.Column="0"
                                                        VerticalOptions="Center"
                                                        IsEnabled="False">

                            <Entry  Text="{Binding OldNutzerContact}" 
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="MoveInDate"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation MoveInDate}"
                                                        ContainerType="Outlined"  
                                                        Grid.Row="2" Grid.Column="1"
                                                        VerticalOptions="Center"
                                                        IsEnabled="False">

                            <DatePicker Date="{Binding OldNutzerMoveInDate}" 
                                        VerticalOptions="End"
                                        FontSize="Medium">

                            </DatePicker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="MoveOutDate"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation MoveOutDate}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="3"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding OldNutzerMoveOutDateHasError}"
                                                        ErrorText="{Binding OldNutzerMoveOutDateErrorText}">

                            <DatePicker Date="{Binding OldNutzerMoveOutDate}"
                                        VerticalOptions="End"
                                        FontSize="Medium">

                            </DatePicker>

                        </inputLayout:SfTextInputLayout>

                    </Grid>

                </Frame>

                <Frame  Margin="5,5,5,5"
                        BorderColor="Gray" 
                        CornerRadius="5"
                        HasShadow="True"
                        Grid.Row="1" Grid.ColumnSpan="2">

                    <Grid>

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <Label  Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2"
                                Text="{markupExtensions:Localisation NewNutzer}"
                                FontSize="Large"
                                FontAttributes="Bold"
                                HorizontalOptions="Center"
                                TextColor="Black">
                        </Label>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerMoveInDate"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation MoveInDate}"
                                                        ContainerType="Outlined"  
                                                        Grid.Row="1" Grid.Column="0"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding NutzerMoveInDateHasError}"
                                                        ErrorText="{Binding NutzerMoveInDateErrorText}">

                            <DatePicker Date="{Binding NutzerMoveInDate}" 
                                        VerticalOptions="End"
                                        FontSize="Medium">  

                            </DatePicker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerKind"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Kind}"
                                                        ContainerType="Outlined"  
                                                        Grid.Row="1" Grid.Column="1"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding SelectedNutzerKindHasError}"
                                                        ErrorText="{Binding SelectedNutzerKindErrorText}">

                            <Picker ItemsSource="{Binding NutzerKindList}"
                                    SelectedItem="{Binding SelectedNutzerKind}"
                                    ItemDisplayBinding="{Binding NutzerKindType}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Picker>
                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerTitle"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Title}"
                                                        ContainerType="Outlined"  
                                                        Grid.Row="2" Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Picker ItemsSource="{Binding NutzerTitleList}"
                                    SelectedItem="{Binding NutzerTitle}"
                                    ItemDisplayBinding="{Binding Label}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerSalutation"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Salutation}"
                                                        ContainerType="Outlined"  
                                                        Grid.Row="2" Grid.Column="1"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding NutzerSalutationHasError}"
                                                        ErrorText="{Binding NutzerSalutationErrorText}">

                            <Picker ItemsSource="{Binding NutzerSalutationList}"
                                    SelectedItem="{Binding NutzerSalutation}"
                                    ItemDisplayBinding="{Binding Label}"
                                    VerticalOptions="End"
                                    FontSize="Medium">
                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerName1"
                                                        InputViewPadding="5"
                                                        Margin="0" 
                                                        Hint="Name1:" 
                                                        ContainerType="Outlined"  
                                                        Grid.Row="3" Grid.Column="0"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding NutzerName1HasError}"
                                                        ErrorText="{Binding NutzerName1ErrorText}">

                            <Entry  Text="{Binding NutzerName1}" 
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerName2"
                                                        InputViewPadding="5"
                                                        Margin="0" 
                                                        Hint="Name2:" 
                                                        ContainerType="Outlined"  
                                                        Grid.Row="3" Grid.Column="1"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzerName2}" 
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerName3"
                                                        InputViewPadding="5"
                                                        Margin="0" 
                                                        Hint="Name3:" 
                                                        ContainerType="Outlined"  
                                                        Grid.Row="4" Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzerName3}" 
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerName4"
                                                        InputViewPadding="5"
                                                        Margin="0" 
                                                        Hint="Name4:" 
                                                        ContainerType="Outlined"  
                                                        Grid.Row="4" Grid.Column="1"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzerName4}" 
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="CommunicationType"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation ContactType}"
                                                        ContainerType="Outlined"  
                                                        Grid.Row="5" Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Picker ItemsSource="{Binding NutzerCommunicationTypeList}"
                                    SelectedItem="{Binding SelectedNutzerCommunicationType}"
                                    ItemDisplayBinding="{Binding CommunicationType}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="CommunicationValue"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Contact}"
                                                        ContainerType="Outlined"  
                                                        Grid.Row="5" Grid.Column="1"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzerCommunicationValue}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerNote"
                                                        InputViewPadding="5"
                                                        Margin="0" 
                                                        Hint="Notiz:" 
                                                        ContainerType="Outlined"  
                                                        Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="2"
                                                        VerticalOptions="Center">

                            <Editor Text="{Binding NutzerNote}" 
                                VerticalOptions="End"
                                FontSize="Medium">

                            </Editor>

                        </inputLayout:SfTextInputLayout>
                    </Grid>
                </Frame>

                <ImageButton    x:Name="SaveButton"
                                Source="saveIcon.png"
                                BackgroundColor="Transparent"
                                HorizontalOptions="End"
                                Margin="0"
                                Grid.Row="2"
                                Grid.Column="1"
                                WidthRequest="70"
                                HeightRequest="70"
                                Command="{Binding SaveNutzerwechselCommand}">
                </ImageButton>

            </Grid>
        </ScrollView>
        
    </ContentPage.Content>
</ContentPage>