﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IAppointmentNutzeinheitDetailsDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using System.Collections.Generic;
using System.Threading.Tasks;
using static Eras2AmwApp.Implementations.AppointmentNutzeinheitDetailsDialog;

namespace Eras2AmwApp.Interfaces
{
    public interface IAppointmentNutzeinheitDetailsDialog
    {
        Task<DialogResponse> ShowAppointmentNutzeinheitInfo(Dictionary<string, string> dictOfAddressNutzeinheiten);
    }
}
