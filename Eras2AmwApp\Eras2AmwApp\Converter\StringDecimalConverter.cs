﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitStatusConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class StringDecimalConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if(value is null)
            {
                return null;
            }
            decimal convertedValue = (decimal)value;
            return convertedValue.ToString(culture);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if(value is null)
            {
                return null;
            }
            string decimalString = (string)value;

            if (decimal.TryParse(decimalString, out _))
            {
                return System.Convert.ToDecimal(decimalString, culture);
            }
            return null;
        }
    }
}
