﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="INewAppointmentDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Interfaces
{
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Threading.Tasks;

    public interface INewAppointmentDialog
    {
        Task<DateTime?> ShowNewAppointmentDialog(<PERSON><PERSON><PERSON> nutzer);
    }
}
