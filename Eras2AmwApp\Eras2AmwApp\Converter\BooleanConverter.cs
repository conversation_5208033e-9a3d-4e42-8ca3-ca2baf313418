﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="BooleanConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class BooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool booleanValue = (bool)value;
            string booleanState = booleanValue.ToString();

            return booleanState;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
