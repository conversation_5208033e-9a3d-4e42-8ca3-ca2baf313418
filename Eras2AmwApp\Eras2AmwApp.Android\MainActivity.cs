﻿namespace Eras2AmwApp.Droid
{
    using Android.App;
    using Android.Content.PM;
    using Android.OS;
    using Android.Runtime;
    using Android.Views;
    using Eras2AmwApp.Pages;
    using System.Linq;
    using Xamarin.Forms;
    using ZXing.Mobile;
    using Xamarin.Essentials;
    using Eras2AmwApp.Droid.Services;

    [Activity(Label = "Eras2AmwApp", Icon = "@mipmap/icon", Theme = "@style/MainTheme", ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation, ScreenOrientation = ScreenOrientation.Portrait)]
    public class MainActivity : Xamarin.Forms.Platform.Android.FormsAppCompatActivity
    {
        private const int navigationBackButtonID = 16908332;

        protected override void OnCreate(Bundle savedInstanceState)
        {
            TabLayoutResource = Resource.Layout.Tabbar;
            ToolbarResource = Resource.Layout.Toolbar;

            base.OnCreate(savedInstanceState);

            Platform.Init(this, savedInstanceState);
            Forms.Init(this, savedInstanceState);
            Syncfusion.XForms.Android.PopupLayout.SfPopupLayoutRenderer.Init();

            LoadApplication(new App());

            ZXing.Net.Mobile.Forms.Android.Platform.Init();
            MobileBarcodeScanner.Initialize(Application);

            AndroidX.AppCompat.Widget.Toolbar toolbar = FindViewById<AndroidX.AppCompat.Widget.Toolbar>(Resource.Id.toolbar);
            SetSupportActionBar(toolbar);
        }

        public override void OnRequestPermissionsResult(int requestCode, string[] permissions, [GeneratedEnum] Permission[] grantResults)
        {
            ZXing.Net.Mobile.Android.PermissionsHandler.OnRequestPermissionsResult(requestCode, permissions, grantResults);
            Platform.OnRequestPermissionsResult(requestCode, permissions, grantResults);
            base.OnRequestPermissionsResult(requestCode, permissions, grantResults);
        }

        public override bool OnOptionsItemSelected(IMenuItem item)
        {
            if (item.ItemId == navigationBackButtonID)
            {
                Page currentMainPage = Xamarin.Forms.Application.Current.MainPage.Navigation.NavigationStack.LastOrDefault();

                switch(currentMainPage)
                {
                    case NutzeinheitPage nutzeinheitPage:
                        nutzeinheitPage.CustomBackButtonAction();
                        return false;

                    case WatermeterEditPage watermeterPage:
                        watermeterPage.CustomBackButtonAction();
                        return false;

                    case RauchmelderEditPage rauchmelderPage:
                        rauchmelderPage.CustomBackButtonAction();
                        return false;

                    case WatermeterExchangePage exchangePage:
                        exchangePage.CustomBackButtonAction();
                        return false;
                }
            }

            return base.OnOptionsItemSelected(item);
        }
    }
}