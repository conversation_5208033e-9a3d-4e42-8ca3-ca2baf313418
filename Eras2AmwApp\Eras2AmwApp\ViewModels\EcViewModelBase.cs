﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="EvViewModelBase.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Reflection;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;
    using AutoMapper;
    using Common.Interfaces;
    using FluentValidation;
    using FluentValidation.Results;
    using GalaSoft.MvvmLight;
    using Interfaces;
    using Serilog;

    public abstract class EcViewModelBase : ViewModelBase
    {
        protected readonly IEcNavigationService navigationService;

        protected readonly ILogger logger;

        protected readonly ILocalisationService localisationService;

        protected readonly IServiceLocator serviceLocator;

        protected readonly IAppSettings appSettings;

        protected readonly IMapper mapper;

        protected EcViewModelBase(IServiceLocator serviceLocator, IEcNavigationService navigationService)
        {
            this.serviceLocator = serviceLocator ?? throw new ArgumentNullException(nameof(serviceLocator));
            
            logger = serviceLocator.Logger;
            localisationService = serviceLocator.LocalisationService;
            appSettings = serviceLocator.AppSettings;
            mapper = serviceLocator.Mapper;

            mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            this.navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
            
            ValidatorOptions.Global.CascadeMode = CascadeMode.Stop;
        }

        public bool IsSetupDone { get; set; }

        public virtual Task SetupAsync(object navigationData)
        {
            IsSetupDone = true;
            return Task.CompletedTask;
        }

        protected void UpdateErrorMessages(ValidationResult result)
        {
            if (result == null)
            {
                throw new ArgumentNullException(nameof(result));
            }

            ClearErrorMessages();

            if (result.IsValid)
            {
                return;
            }

            foreach (ValidationFailure error in result.Errors)
            {
                var properyName = error.PropertyName;
                var message = error.ErrorMessage;

                PropertyInfo prop = GetType().GetProperty($"{properyName}ErrorText");
                prop?.SetValue(this,  message);

                PropertyInfo prop2 = GetType().GetProperty($"{properyName}HasError");
                prop2?.SetValue(this, true);
            }
        }

        private void ClearErrorMessages()
        {
            IEnumerable<PropertyInfo> errorProperties = GetType().GetProperties().Where(x => x.Name.EndsWith("HasError"));

            foreach (PropertyInfo errorProperty in errorProperties)
            {
                errorProperty.SetValue(this, false);
            }
        }

        protected string RemoveUnsupportedCharacters(string inputString)
        {
            return Regex.Replace(inputString, @"[^\u0000-\u007F\u00E4\u00F6\u00FC\u00C4\u00D6\u00DC\u00DF]+", string.Empty);
        }
    }
}