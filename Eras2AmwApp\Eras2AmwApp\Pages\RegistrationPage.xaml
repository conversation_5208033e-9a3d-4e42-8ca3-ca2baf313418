﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:buttons="clr-namespace:Syncfusion.XForms.Buttons;assembly=Syncfusion.Buttons.XForms"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.RegistrationPage">
    
    <ContentPage.Content>
        <StackLayout>

            <Label Text="{Binding AppTitle}"
                   HorizontalOptions="Center"
                   VerticalOptions="Center"
                   TextColor="Black"
                   FontSize="35" />

            <Label  Text="Registrierung"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    TextColor="Black"
                    FontSize="32" />

            <buttons:SfButton   Margin="5,5,5,0"
                                Text="QrCode Laden"
                                Command="{Binding LoadQrCodeCommand}"
                                Style="{DynamicResource SyncfusionButtonBlue}" />

            <!--<buttons:SfButton   Margin="5,5,5,0"
                                Text="Test Datenbank"
                                Command="{Binding LoadTestAdminCommand}"
                                Style="{DynamicResource SyncfusionButtonBlue}" />-->   

        </StackLayout>
    </ContentPage.Content>
</ContentPage>