﻿namespace Eras2AmwApp.Pages
{
    using System;
    using Eras2AmwApp.ViewModels;
    using Xamarin.Forms;
    using Xamarin.Forms.Xaml;

    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NutzeinheitPage : ContentPage
    {
        public static readonly BindableProperty EnableBackButtonOverrideProperty =
            BindableProperty.Create(
                nameof(EnableBackButtonOverride),
                typeof(bool),
                typeof(NutzeinheitPage),
                true);

        private readonly NutzeinheitPageViewModel nutzeinheitVM;

        public NutzeinheitPage(NutzeinheitPageViewModel viewModel)
        {
            InitializeComponent();
            
            BindingContext = nutzeinheitVM = viewModel;
        }

        public Action CustomBackButtonAction => async () => await nutzeinheitVM.DisplayUnsavedDataWarning();

        public bool EnableBackButtonOverride
        {
            get => (bool)GetValue(EnableBackButtonOverrideProperty);
            set => SetValue(EnableBackButtonOverrideProperty, value);
        }

        protected override bool OnBackButtonPressed()
        {
            ShowExitDialog();

            return true;
        }

        private void ShowExitDialog()
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                await nutzeinheitVM.DisplayUnsavedDataWarning();
            });
        }
    }
}