﻿namespace Eras2AmwApp.Pages
{
    using Eras2AmwApp.ViewModels;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using Xamarin.Forms.Xaml;

    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class AppointmentPage : ContentPage
	{
        public AppointmentPage(AppointmentPageViewModel viewModel)
        {
            InitializeComponent();

            BindingContext = deviceVM = viewModel;
            viewModel.SfDataGrid = appointmentDataGrid;
            viewModel.TreeViewModel = treeView;

            NavigationPage.SetHasNavigationBar(this, false);
        }

        private readonly AppointmentPageViewModel deviceVM;

        protected override bool OnBackButtonPressed()
        {
            ShowExitWarningDialog();

            return true;
        }

        private void ShowExitWarningDialog()
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                await deviceVM.DisplayExitWarning();
            });
        }

    }
}