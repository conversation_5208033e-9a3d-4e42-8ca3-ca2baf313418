﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:inputLayout="clr-namespace:Syncfusion.XForms.TextInputLayout;assembly=Syncfusion.Core.XForms"
             xmlns:converter="clr-namespace:Eras2AmwApp.Converter"
             xmlns:buttons="clr-namespace:Syncfusion.XForms.Buttons;assembly=Syncfusion.Buttons.XForms"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:behaviors="clr-namespace:Eras2AmwApp.Behaviors"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.OrderEditPage">

    <ContentPage.Resources>
        <converter:TitleSalutationConverter x:Key="TitleSalutationConverter"></converter:TitleSalutationConverter>
    </ContentPage.Resources>

    <ContentPage.Behaviors>
        <behaviors:EventToCommandBehavior EventName="Appearing" 
                                          Command="{Binding AppearingCommand}">
        </behaviors:EventToCommandBehavior>
    </ContentPage.Behaviors>

    <ContentPage.Content>
        <ScrollView>
            <StackLayout>

            <Frame  Margin="10"
                    Padding="0"
                    BorderColor="LightGray"
                    CornerRadius="0"
                    HasShadow="True">

                <Grid RowSpacing="0">

                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="50*"></ColumnDefinition>
                        <ColumnDefinition Width="25*"></ColumnDefinition>
                        <ColumnDefinition Width="Auto"></ColumnDefinition>
                    </Grid.ColumnDefinitions>

                    <Label x:Name="MainText"
                           Grid.Row="0"
                           Grid.Column="0"
                           Margin="30,20,0,0"
                           Text="{Binding Address}"
                           TextColor="Black"
                           FontSize="Medium"
                           FontAttributes="Bold">

                    </Label>

                    <inputLayout:SfTextInputLayout  x:Name="OrderNote"
                                                    InputViewPadding="5"
                                                    Grid.Row="1"
                                                    Grid.Column="0"
                                                    Grid.ColumnSpan="2"
                                                    Margin="30,10,0,0"
                                                    Hint="{markupExtensions:Localisation OrderNote}"
                                                    ContainerType="Outlined"
                                                    VerticalOptions="Start">

                        <Entry  Text="{Binding OrderNote}"
                                VerticalOptions="End"
                                BackgroundColor="White"
                                FontSize="Small">
                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <inputLayout:SfTextInputLayout  x:Name="AeNote"
                                                    InputViewPadding="5"
                                                    Grid.Row="2"
                                                    Grid.Column="0"
                                                    Grid.ColumnSpan="2"
                                                    Margin="30,5,0,0"
                                                    Hint="{markupExtensions:Localisation AeNote}"
                                                    ContainerType="Outlined"
                                                    VerticalOptions="Center">

                        <Entry  Text="{Binding AbrechnungseinheitNote}"
                                VerticalOptions="End"
                                BackgroundColor="White"
                                FontSize="Small">
                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <Label  x:Name="OrderNumber"
                            Grid.Row="0"
                            Grid.Column="2"
                            HorizontalTextAlignment="End"
                            Margin="0,20,20,0"
                            Text="{Binding OrderNumber}"
                            FontSize="Medium"
                            TextColor="Black"
                            FontAttributes="Bold">

                    </Label>

                    <ImageButton    Source="saveIcon.png"
                                    BackgroundColor="Transparent"
                                    Grid.Row="1"
                                    Grid.Column="2"
                                    HorizontalOptions="End"
                                    Margin="5,0,5,5"
                                    HeightRequest="50"
                                    WidthRequest="50"
                                    Command="{Binding UpdateAuftrageNoticeCommand} ">

                    </ImageButton>

                    <ImageButton    Source="saveIcon.png"
                                    BackgroundColor="Transparent"
                                    Grid.Row="2"
                                    Grid.Column="2"
                                    HorizontalOptions="End"
                                    Margin="5,0,5,5"
                                    HeightRequest="50"
                                    WidthRequest="50"
                                    Command="{Binding UpdateAbrechnungseinheitNoticeCommand}">

                    </ImageButton>

                    </Grid>
            </Frame>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="90*"></ColumnDefinition>
                    <ColumnDefinition Width="10*"></ColumnDefinition>
                </Grid.ColumnDefinitions>

                <buttons:SfSegmentedControl x:Name="SegmentedControl"
                                            Grid.Row="1"
                                            Grid.Column="0"
                                            VisibleSegmentsCount="{Binding SegmentNumber}"
                                            CornerRadius="10"
                                            SegmentBackgroundColor="#538EEC"
                                            FontColor="White"
                                            FontSize="16"
                                            Margin="10,5,5,0"
                                            SelectedIndex="{Binding SelectedSegment}"
                                            ItemsSource="{Binding AbrechnungsPeopleNames}">
                    <buttons:SfSegmentedControl.Behaviors>
                        <behaviors:EventToCommandBehavior EventName="SelectionChanged"
                                                          Command="{Binding SegmentChangedCommand}">
                        </behaviors:EventToCommandBehavior>
                    </buttons:SfSegmentedControl.Behaviors>
                </buttons:SfSegmentedControl>

                <ImageButton    x:Name="AddDeviceButton"
                                Source="newIcon.png"
                                BackgroundColor="Transparent"
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0"
                                HeightRequest="60"
                                WidthRequest="60"
                                Command="{Binding AddSegmentCommand}">
                </ImageButton>

                <Frame  Grid.Row="2"
                        Grid.Column="0"
                        Grid.ColumnSpan="2"
                        Margin="10"
                        Padding="5"
                        BorderColor="LightGray"
                        CornerRadius="0"
                        HasShadow="True">

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50*" />
                            <ColumnDefinition Width="50*" />
                        </Grid.ColumnDefinitions>

                        <inputLayout:SfTextInputLayout  x:Name="Titel"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Title}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="0"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Picker ItemsSource="{Binding PersonTitleList}"
                                    SelectedItem="{Binding PersonTitle}"
                                    ItemDisplayBinding="{Binding Label}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Salutation"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Salutation}"
                                                        ContainerBackgroundColor="White"
                                                        ContainerType="Outlined"
                                                        Grid.Row="0"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding PersonSalutationHasError}"
                                                        ErrorText="{Binding PersonSalutationErrorText}">

                            <Picker ItemsSource="{Binding PersonSalutationList}"
                                    SelectedItem="{Binding PersonSalutation}"
                                    ItemDisplayBinding="{Binding Label}"
                                    VerticalOptions="End"
                                    BackgroundColor="White"
                                    FontSize="Medium">

                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="LastName"
                                                        InputViewPadding="5"
                                                        ContainerBackgroundColor="White"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation SecondName}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="1"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding PersonLastNameHasError}"
                                                        ErrorText="{Binding PersonLastNameErrorText}">

                            <Entry  Text="{Binding PersonLastName}"
                                    VerticalOptions="End"
                                    FontSize="Medium">
                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="FirstName"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation FirstName}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="1"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding PersonFirstNameHasError}"
                                                        ErrorText="{Binding PersonFirstNameErrorText}">

                            <Entry  Text="{Binding PersonFirstName}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Position"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Position}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="2"
                                                        Grid.Column="0"
                                                        Grid.ColumnSpan="2"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding PersonPositionHasError}"
                                                        ErrorText="{Binding PersonPositionErrorText}">

                            <Entry  Text="{Binding PersonPosition}"
                                    VerticalOptions="End"
                                    FontSize="Medium">
                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="KontaktType"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation ContactType}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="3"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding PersonCommunicationTypeHasError}"
                                                        ErrorText="{Binding PersonCommunicationTypeErrorText}">

                            <Picker ItemsSource="{Binding PersonCommunicationTypeList}"
                                    SelectedItem="{Binding PersonCommunicationType}"
                                    ItemDisplayBinding="{Binding CommunicationType}"
                                    VerticalOptions="End"
                                    BackgroundColor="White"
                                    FontSize="Medium">

                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="KontactValue"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Contact}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="3"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding PersonCommunicationValueHasError}"
                                                        ErrorText="{Binding PersonCommunicationValueErrorText}">

                            <Entry  Text="{Binding PersonCommunicationValue}"
                                    VerticalOptions="End"
                                    FontSize="Medium">
                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Note"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation PersonNote}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="4"
                                                        Grid.Column="0"
                                                        Grid.ColumnSpan="2"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding PersonNote}"
                                    VerticalOptions="End"
                                    FontSize="Medium">
                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <ImageButton    Source="saveIcon.png"
                                        BackgroundColor="Transparent"
                                        Grid.Row="5"
                                        Grid.Column="1"
                                        HorizontalOptions="End"
                                        Margin="0"
                                        HeightRequest="60"
                                        WidthRequest="60"
                                        Command="{Binding SaveSegmentCommand}"
                                        CommandParameter="{Binding SelectedSegment}">
                        </ImageButton>

                    </Grid>
                </Frame>

            </Grid>

            </StackLayout>
        </ScrollView>
    </ContentPage.Content>
</ContentPage>