﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IDeviceOrderKindChangeDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Interfaces
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using System.Threading.Tasks;

    public interface IDeviceOrderKindChangeDialog
    {
        Task<DeviceOrderKind> ShowChangeDeviceOrderKindDialog(DeviceOrderKind deviceOrderKind);
    }
}
