﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitStatusConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class NutzeinheitStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            ProcessState nutzeinheitProcessState = (ProcessState)value;
            string imageState;

            switch (nutzeinheitProcessState)
            {
                case ProcessState.InProgress:
                    imageState = "lockOpen.png";
                    break;
                case ProcessState.Updating:
                    imageState = "lockOpen.png";
                    break;
                case ProcessState.Creating:
                    imageState = "lockOpen.png";
                    break;
                case ProcessState.Completed:
                    imageState = "lockClosed.png";
                    break;
                default:
                    imageState = "lockOpen.png";
                    break;
            }

            return imageState;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
