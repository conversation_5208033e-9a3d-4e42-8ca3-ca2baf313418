﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="LoginService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Exceptions;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2App.Database;
    using Eras2AmwApp.WebService.Interfaces;
    using Microsoft.EntityFrameworkCore;
    using Ninject.Activation;
    using Polly;
    using Polly.Timeout;
    using Serilog;

    public class LoginService : ILoginService
    {
        private const int SyncHintAfterDays = 1;

        private readonly IAmwWebservice webservice;
        private readonly IDbContextFactory contextFactory;
        private readonly IAppSettings appSettings;
        private readonly ILocalisationService localisationService;
        private readonly ILogger logger;

        private Eras2AppContext databaseContext;

        public LoginService(IAmwWebservice webservice, IDbContextFactory contextFactory, IAppSettings appSettings, ILocalisationService localisationService, ILogger logger)
        {
            this.webservice = webservice ?? throw new ArgumentNullException(nameof(webservice));
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
            this.localisationService = localisationService ?? throw new ArgumentNullException(nameof(localisationService));
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public bool NetworkAccess { get; set; }

        public bool ShouldSyncOrders
        {
            get
            {
                bool existsOrders;

                using (Eras2AmwContext context = contextFactory.CreateAmw())
                {
                    existsOrders = context.Orders.Any();
                }

                using (Eras2AppContext context = contextFactory.CreateApp())
                {
                    User user = context.Users.SingleOrDefault();

                    bool isSyncNeeded = user?.LastWebserviceSyncDate != null && DateTime.Now > user.LastWebserviceSyncDate.Value.AddDays(SyncHintAfterDays);
                    isSyncNeeded &= existsOrders;

                    return isSyncNeeded;
                }
            }
        }

        public bool IsUserTimestampValid(User user)
        {
            if (user == null)
            {
                throw new ArgumentNullException(nameof(user));
            }

            return DateTime.Now <= user.LastWebserviceLoginDate.AddDays(appSettings.UserLoginValidLocalLoginDays);
        }

        public async Task<Guid> AdminTestLoginAsync(string name, string password)
        {
            if (name == null)
            {
                throw new ArgumentNullException(nameof(name));
            }

            if (password == null)
            {
                throw new ArgumentNullException(nameof(password));
            }
            Guid userGuid;

            using (databaseContext = contextFactory.CreateApp())
            {
                try
                {
                    userGuid = await DoTestLoginAsync(name, password);
                    User user = databaseContext.Users.Single();
                    user.LastWebserviceSyncDate = DateTime.Now;
                    databaseContext.SaveChanges();
                    
                }
                catch (Exception exception)
                {
                    logger.Error(exception, "AdminTestLoginAsync failed!");
                    throw;
                }
            }

            ReplaceMainDatabaseWithTestDataAsync();
            return userGuid;
        }

        public async Task<Guid> LoginAsync(string name, string password)
        {
            if (name == null)
            {
                throw new ArgumentNullException(nameof(name));
            }

            if (password == null)
            {
                throw new ArgumentNullException(nameof(password));
            }

            Guid userGuid;

            using (databaseContext = contextFactory.CreateApp())
            {
                if (!NetworkAccess && IsUserChange(name))
                {
                    throw new Eras2AmwException(localisationService.Get("UserLoginFailed6"));
                }

                try
                {
                    if (NetworkAccess)
                    {
                        AsyncTimeoutPolicy timeOutPolicy = Policy.TimeoutAsync(30, TimeoutStrategy.Pessimistic);

                        userGuid = await timeOutPolicy.ExecuteAsync(async () => await DoRemoteLoginAsync(name, password).ConfigureAwait(false));

                        if (!ExistsLocalOrders())
                        {
                            await SyncOrders().ConfigureAwait(false);
                        }

                        await webservice.LogoutAsync();
                    }
                    else
                    {
                        userGuid = DoLocalLogin(name, password);
                    }
                }
                catch (WebException exp) when (webservice.IsServerNotAvailableStatusError(exp))
                {
                    logger.Warning(exp, localisationService.Get("UserLoginFailed7"));
                    userGuid = DoLocalLogin(name, password);
                }
            }

            return userGuid;
        }

        private void ReplaceMainDatabaseWithTestDataAsync()
        {
            try
            {
                string oldDatabasePath = Path.Combine(appSettings.DatabaseDirectory, "eras2_amw.sqlite3");
                string newTestAdminDatabasePath = Path.Combine(appSettings.DatabaseDirectory, "test_eras2_amw.sqlite3");

                // Check if the main database file exists and delete it
                if (File.Exists(oldDatabasePath)&& File.Exists(newTestAdminDatabasePath))
                {
                    File.Delete(oldDatabasePath);
                }

                if (File.Exists(newTestAdminDatabasePath))
                {
                    // Rename the test database to the main database name
                    File.Move(newTestAdminDatabasePath, oldDatabasePath);
                }
            }
            catch (Exception exception)
            {
                logger.Error(exception, "ReplaceMainDatabaseWithTestDataAsync failed!");
                throw; 
            }
        }

        private async Task SyncOrders()
        {
            User user = databaseContext.Users.Single();

            await webservice.SyncOrdersAsync(user.Guid).ConfigureAwait(false);

            user.LastWebserviceSyncDate = DateTime.Now;

            databaseContext.SaveChanges();
        }

        private bool ExistsLocalOrders()
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Orders.Any();
            }
        }

        private async Task<Guid> DoRemoteLoginAsync(string name, string password)
        {
            if (name == null)
            {
                throw new ArgumentNullException(nameof(name));
            }

            if (password == null)
            {
                throw new ArgumentNullException(nameof(password));
            } 

            Guid userGuid = await webservice.LoginAsync(name, password).ConfigureAwait(false);

            var newUser = new User
            {
                Guid = userGuid,
                Customer = databaseContext.Customers.Single(),
                Name = name,
                Password = password,
                LastWebserviceLoginDate = DateTime.Now,
                IsLiveSyncEnabled = true
            };

            User oldUser = databaseContext.Users.SingleOrDefault();

            await HandleUser(newUser, oldUser);

            databaseContext.SaveChanges();

            return userGuid;
        }

        private async Task<Guid> DoTestLoginAsync(string name, string password)
        {
            if (name == null)
            {
                throw new ArgumentNullException(nameof(name));
            }

            if (password == null)
            {
                throw new ArgumentNullException(nameof(password));
            }

            var newUser = new User
            {
                Guid = Guid.Parse("1F6AD0D2-8216-43AB-86A2-549805C56B71"),
                Customer = databaseContext.Customers.Single(),
                Name = name,
                Password = password,
                LastWebserviceLoginDate = DateTime.Now,
                IsLiveSyncEnabled = false
            };

            User oldUser = databaseContext.Users.SingleOrDefault();

            await HandleUser(newUser, oldUser);

            databaseContext.SaveChanges();

            return newUser.Guid;
        }

        private bool IsUserChange(string name)
        {
            if (name == null)
            {
                throw new ArgumentNullException(nameof(name));
            }

            User currentUser = databaseContext.Users.SingleOrDefault();

            return currentUser != null && currentUser.Name != name;
        }

        private async Task HandleUser(User newUser, User oldUser)
        {
            if (newUser == null)
            {
                throw new ArgumentNullException(nameof(newUser));
            }

            if (oldUser == null)
            {
                CreateUser(newUser);
            }
            else if (oldUser.Guid == newUser.Guid)
            {
                UpdateUser(oldUser, newUser);
            }
            else
            {
                await ChangeUser(oldUser, newUser);
            }
        }

        private void CreateUser(User user)
        {
            if (user == null)
            {
                throw new ArgumentNullException(nameof(user));
            }

            databaseContext.Users.Add(user);
        }

        public void DeleteUsers()
        {
            using (var context = contextFactory.CreateApp())
            {
                User user = context.Users.FirstOrDefault();

                if (user != null)
                {
                    context.Users.Remove(user);
                    context.SaveChanges();
                }   
            }
        }

        public void WipeDatabase()
        {
            using (var context = contextFactory.CreateAmw())
            {
                var abrechnungseinheiten = context.Abrechnungseinheiten.ToList();
                context.Abrechnungseinheiten.RemoveRange(abrechnungseinheiten);

                var additionalArticles = context.AdditionalArticles.ToList();
                context.AdditionalArticles.RemoveRange(additionalArticles);

                var addresses = context.Addresses.ToList();
                context.Addresses.RemoveRange(addresses);

                var amwInfoKeys = context.AmwInfoKeys.ToList();
                context.AmwInfoKeys.RemoveRange(amwInfoKeys);

                var appointments = context.Appointments.ToList();
                context.Appointments.RemoveRange(appointments);

                var appointmentsStoreUsers = context.AppointmentUsers.ToList();
                context.AppointmentUsers.RemoveRange(appointmentsStoreUsers);

                var appointmentsNutzeinheiten = context.AppointmentNutzeinheiten.ToList();
                context.AppointmentNutzeinheiten.RemoveRange(appointmentsNutzeinheiten);

                var articles = context.Articles.ToList();
                context.Articles.RemoveRange(articles);

                var customers = context.Customers.ToList();
                context.Customers.RemoveRange(customers);

                var devices = context.Devices.ToList();
                context.Devices.RemoveRange(devices);

                var devicesAdditionalArticles = context.DeviceAdditionalArticles.ToList();
                context.DeviceAdditionalArticles.RemoveRange(devicesAdditionalArticles);

                var deviceOrderStates = context.DeviceOrderStates.ToList();
                context.DeviceOrderStates.RemoveRange(deviceOrderStates);

                var deviceCatalogs = context.DeviceCatalog.ToList();
                context.DeviceCatalog.RemoveRange(deviceCatalogs);

                var deviceConsumptions = context.DeviceConsumptions.ToList();
                context.DeviceConsumptions.RemoveRange(deviceConsumptions);

                var deviceKinds = context.DeviceKinds.ToList();
                context.DeviceKinds.RemoveRange(deviceKinds);

                var manufacturers = context.Manufacturers.ToList();
                context.Manufacturers.RemoveRange(manufacturers);

                var nutzeinheiten = context.Nutzeinheiten.ToList();
                context.Nutzeinheiten.RemoveRange(nutzeinheiten);

                var nutzeinheitOrderStates = context.NutzeinheitOrderStates.ToList();
                context.NutzeinheitOrderStates.RemoveRange(nutzeinheitOrderStates);

                var nutzers = context.Nutzer.ToList();
                context.Nutzer.RemoveRange(nutzers);

                var nutzerComm = context.NutzerCommunication.ToList();
                context.NutzerCommunication.RemoveRange(nutzerComm);

                var nutzerCoOwnership = context.NutzerCoOwnership.ToList();
                context.NutzerCoOwnership.RemoveRange(nutzerCoOwnership);

                var nutzerPersonen = context.NutzerPersonen.ToList();
                context.NutzerPersonen.RemoveRange(nutzerPersonen);

                var nutzerQuadratmeter = context.NutzerQuadratmeter.ToList();
                context.NutzerQuadratmeter.RemoveRange(nutzerQuadratmeter);

                var orders = context.Orders.ToList();
                context.Orders.RemoveRange(orders);

                var orderers = context.Orderer.ToList();
                context.Orderer.RemoveRange(orderers);

                var orderPositions = context.OrderPositions.ToList();
                context.OrderPositions.RemoveRange(orderPositions);

                var persons = context.Persons.ToList();
                context.Persons.RemoveRange(persons);

                var personsAbrechungs = context.PersonAbrechnungseinheiten.ToList();
                context.PersonAbrechnungseinheiten.RemoveRange(personsAbrechungs);

                var personsComm = context.PersonCommunications.ToList();
                context.PersonCommunications.RemoveRange(personsComm);

                var photos = context.Photos.ToList();
                context.Photos.RemoveRange(photos);

                var readingKinds = context.ReadingKinds.ToList();
                context.ReadingKinds.RemoveRange(readingKinds);

                var rooms = context.Rooms.ToList();
                context.Rooms.RemoveRange(rooms);

                var salutations = context.Salutations.ToList();
                context.Salutations.RemoveRange(salutations);

                var signatures = context.Signatures.ToList();
                context.Signatures.RemoveRange(signatures);

                context.SaveChanges();
            }
        }

        private void UpdateUser(User oldUser, User newUser)
        {
            if (oldUser == null)
            {
                throw new ArgumentNullException(nameof(oldUser));
            }

            if (newUser == null)
            {
                throw new ArgumentNullException(nameof(newUser));
            }

            oldUser.LastWebserviceLoginDate = newUser.LastWebserviceLoginDate;
            oldUser.Name = newUser.Name;
            oldUser.Password = newUser.Password;
        }

        private async Task ChangeUser(User oldUser, User newUser)
        {
            if (oldUser == null)
            {
                throw new ArgumentNullException(nameof(oldUser));
            }

            if (newUser == null)
            {
                throw new ArgumentNullException(nameof(newUser));
            }

            if (ExistsLocalOrders())
            {
                await webservice.SyncOrdersAsync(oldUser.Guid).ConfigureAwait(false);
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.RecreateDatabase();
            }
            
            databaseContext.Users.Remove(oldUser);
            databaseContext.Users.Add(newUser);
        }

        private Guid DoLocalLogin(string name, string password)
        {
            using (Eras2AppContext context = contextFactory.CreateApp())
            {
                if (!context.Users.Any())
                {
                    throw new UnauthorizedAccessException(localisationService.Get("UserLoginFailed1"));
                }

                User user = context.Users.SingleOrDefault(x => x.Name == name && x.Password == password);
                
                if (user == null)
                {
                    throw new UnauthorizedAccessException(localisationService.Get("UserLoginFailed2"));
                }

                if (!IsUserTimestampValid(user))
                {
                    throw new UnauthorizedAccessException(localisationService.Get("UserLoginFailed3"));
                }

                return user.Guid;
            }
        }

        public User GetAppUser()
        {
            using(var context = contextFactory.CreateApp())
            {
                return context.Users.First();
            }
        }

        public string GetLoginAppTitle()
        {
            string apptitle = "ERAS2 AMW";

            if(appSettings.IsStandaloneApp)
            {
                apptitle = "ERAS2 RMW";
            }

            return apptitle;
        }

        public bool GetUserLiveSyncState()
        {
            using (var context = contextFactory.CreateApp())
            {
                return context.Users.First().IsLiveSyncEnabled;
            }
        }

        public void UpdateUserLiveSyncState(bool liveSyncState)
        {
            using (var context = contextFactory.CreateApp())
            {
                User user = context.Users.First();
                if(user.IsLiveSyncEnabled != liveSyncState)
                {
                    user.IsLiveSyncEnabled = liveSyncState;
                    context.SaveChanges();
                }
            }
        }
    }
}
