﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using System;
    using System.Collections.Generic;
    using System.Linq;

    public class DeviceService : IDeviceService
    {
        private readonly IDbContextFactory contextFactory;
        private readonly bool isStandalone;

        public DeviceService(IDbContextFactory contextFactory, IAppSettings appSettings)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));

            isStandalone = appSettings.IsStandaloneApp;
        }

        public Device GetDevice(Guid deviceGuid)
        {
            if (deviceGuid == null)
            {
                throw new ArgumentNullException(nameof(deviceGuid));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Devices
                    .Include(x => x.OrderStates)
                        .ThenInclude(x => x.Device)
                    .Include(x => x.Room)
                    .Include(x => x.DeviceAdditionalArticles)
                        .ThenInclude(DeviceAdditionalArticles => DeviceAdditionalArticles.AdditionalArticle)
                    .Include(x => x.DeviceCatalog)
                        .ThenInclude(DeviceCatalog => DeviceCatalog.DeviceKind)
                    .Include(x => x.DeviceConsumptions)
                    .Where(x => x.Guid == deviceGuid).SingleOrDefault();
            }
        }

        public void UpdateDevice(Device device)
        {
            if(device == null)
            {
                throw new ArgumentNullException(nameof(device));
            }

            using(Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(device);
                context.Entry(device).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void UpdateDeviceConsumption(DeviceConsumption deviceConsumption)
        {
            if (deviceConsumption == null)
            {
                throw new ArgumentNullException(nameof(deviceConsumption));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(deviceConsumption);
                context.Entry(deviceConsumption).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void UpdateDeviceAdditonalArticle(DeviceAdditionalArticle deviceAdditionalArticle)
        {
            if (deviceAdditionalArticle == null)
            {
                throw new ArgumentNullException(nameof(deviceAdditionalArticle));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(deviceAdditionalArticle);
                context.Entry(deviceAdditionalArticle).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void SaveDeviceAdditonalArticle(DeviceAdditionalArticle deviceAdditionalArticle)
        {
            if (deviceAdditionalArticle == null)
            {
                throw new ArgumentNullException(nameof(deviceAdditionalArticle));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.DeviceAdditionalArticles.Add(deviceAdditionalArticle);
                context.SaveChanges();
            }
        }

        public void RemoveDeviceAdditionalArticle(List<DeviceAdditionalArticle> deviceAdditionalArticles)
        {
            if (deviceAdditionalArticles == null)
            {
                throw new ArgumentNullException(nameof(deviceAdditionalArticles));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.DeviceAdditionalArticles.RemoveRange(deviceAdditionalArticles);
                context.SaveChanges();
            }
        }

        public void AddDevice(Device device)
        {
            if (device == null)
            {
                throw new ArgumentNullException(nameof(device));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Devices.Add(device);
                context.SaveChanges();
            }
        }

        public DeviceOrderState GetDeviceOrderState(Guid deviceGuid, Guid orderGuid)
        {
            if (deviceGuid == null)
            {
                throw new ArgumentNullException(nameof(deviceGuid));
            }

            if (orderGuid == null)
            {
                throw new ArgumentNullException(nameof(orderGuid));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.DeviceOrderStates.Include(x => x.AmwInfoKey)
                    .Where(x => x.DeviceGuid == deviceGuid && x.OrderGuid == orderGuid).SingleOrDefault();
            }
        }

        public void UpdateDeviceOrderState(DeviceOrderState deviceOrderState)
        {
            if (deviceOrderState is null)
            {
                throw new ArgumentNullException(nameof(deviceOrderState));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(deviceOrderState);
                context.Entry(deviceOrderState).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void AddDeviceOrderState(DeviceOrderState deviceOrderState)
        {
            if (deviceOrderState == null)
            {
                throw new ArgumentNullException(nameof(deviceOrderState));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.DeviceOrderStates.Add(deviceOrderState);
                context.SaveChanges();
            }
        }

        public List<DeviceKind> GetDeviceKinds()
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                if(isStandalone)
                {
                    return context.DeviceKinds.Where(x=> x.Class == DeviceClass.RM).ToList();
                }
                else
                {
                    return context.DeviceKinds.ToList();
                }
            }
        }

        public List<DeviceCatalog> GetDeviceCatalogList(Guid deviceKindGuid)
        {
            if (deviceKindGuid == null)
            {
                throw new ArgumentNullException(nameof(deviceKindGuid));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.DeviceCatalog.Include(x => x.DeviceKind)
                                            .Where(x => x.DeviceKindGuid == deviceKindGuid).ToList();
            }
        }

        public ReadingKind GetRauchmelderReadingKind()
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.ReadingKinds.Where(x => x.LabelShort == "AB").Single();
            }
        }

        public void DeleteDevice(IDevice device)
        {
            if (device == null)
            {
                throw new ArgumentNullException(nameof(device));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                Device dalDevice = context.Devices.Where(x => x.IsCreatedByApp).Single(x => x.Guid == device.DeviceGuid);
                context.Devices.Remove(dalDevice);
                context.SaveChanges();
            }
        }

        public string GetPlaceholderOngoingNumber(Guid nutzeinheitGuid)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                var ongoingNumberList = context.Devices.Where(y => y.NutzeinheitGuid == nutzeinheitGuid);
                int nextOngoingNumber = 100;
                if (ongoingNumberList.Any())
                {
                    int highestOngoingNumber = ongoingNumberList.Max(x => Convert.ToInt32(x.OngoingNumber));
                    nextOngoingNumber = (highestOngoingNumber % 100 == 0) ? highestOngoingNumber += 100 : (int)Math.Ceiling(highestOngoingNumber / 100.0) * 100;
                }
                return nextOngoingNumber.ToString();
            }
        }

        public string GetPreviousCalibarationDate(Guid nutzeinheitGuid)
        {
            if (nutzeinheitGuid == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheitGuid));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                var nutzeinheit = context.Nutzeinheiten.Find(nutzeinheitGuid);

                var abrechnungseinheit = context.Abrechnungseinheiten
                    .Include(x => x.Nutzeinheiten)
                    .ThenInclude(Nutzeinheiten => Nutzeinheiten.Devices)
                    .Single(x => x.Nutzeinheiten.Contains(nutzeinheit));

                var deviceList = abrechnungseinheit.Nutzeinheiten.SelectMany(x => x.Devices);
                deviceList = deviceList.Where(y => y.InstallationDate.Date == DateTime.Now.Date && y.CalibrationDate.HasValue);

                if(!deviceList.Any())
                {
                    return " ";
                }

                return deviceList.OrderByDescending(x => x.InstallationDate).First().CalibrationDate.Value.Year.ToString();
            }
        }
    }
}
