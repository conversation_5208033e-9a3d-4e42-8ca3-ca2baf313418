﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ServiceLocator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Implementations
{
    using System;
    using System.Collections.Generic;
    using AutoMapper;
    using Interfaces;
    using Ioc;
    using Serilog;

    public class ServiceLocator : IServiceLocator
    {
        public ServiceLocator(IAppSettings appSettings, ILogger logger, ILocalisationService localisationService, IMapper mapper, IResourceService resourceService)
        {
            ResourceService = resourceService ?? throw new ArgumentNullException(nameof(resourceService));
            AppSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
            LocalisationService = localisationService ?? throw new ArgumentNullException(nameof(localisationService));
            Mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public IAppSettings AppSettings { get; }

        public ILogger Logger { get; }

        public ILocalisationService LocalisationService { get; }

        public IMapper Mapper { get; }

        public IResourceService ResourceService { get; }

        public T GetService<T>() where T : class
        {
            try
            {
                return NinjectKernel.Get<T>();
            }
            catch (KeyNotFoundException)
            {
                throw new ApplicationException("The requested service is not registered");
            }
        }
    }
}