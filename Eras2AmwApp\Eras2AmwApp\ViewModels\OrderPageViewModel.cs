﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.Models;
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq;
    using System.Threading.Tasks;
    using Eras2AmwApp.Implementations;
    using Eras2AmwApp.WebService.EventArgs;
    using Eras2AmwApp.WebService.Interfaces;
    using Syncfusion.SfBusyIndicator.XForms;
    using Xamarin.Essentials;
    using Xamarin.Forms;

    using INutzeinheitService = BusinessLogic.Interfaces.INutzeinheitService;
    using IOrderService = BusinessLogic.Interfaces.IOrderService;
    using IStammdatenService = BusinessLogic.Interfaces.IStammdatenService;

    public class OrderPageViewModel : EcViewModelBase
    {
        #region fields

        private readonly IAppointmentService appointmentService;
        private readonly IOrderService orderService;
        private readonly INewAppointmentDialog newAppointmentDialog;
        private readonly IStammdatenService stammdatenService;
        private readonly INutzeinheitService nutzeinheitService;
        private readonly INutzerService nutzerService;
        private readonly IBackupService backupService;
        private readonly IAmwWebservice webservice;
        private readonly IEcDialogService dialogService;
        private readonly IDeviceService deviceService;
        private readonly ILoginService loginService;

        private Command orderEditCommand;
        private Command nutzeinheitStateChangeCommand;
        private Command addNutzeinheitCommand;
        private Command appearingCommand;
        private Command disappearingCommand;
        private Command orderIconButtonCommand;
        private Command nutzeinheitIconButtonCommand;
        private Command nutzeinheitAmwInfoCommand;
        private Command newAppointmentCommand;
        
        private Appointment _selectedAppointment;

        private OrderState _orderState;
        private ProcessState _deviceNutzeinheitenState;
        private string _orderLabel;
        private string _address;
        private DateTime _startTime;
        private DateTime _endTime;
        private bool _hasOrderNote;
        private string _orderNumber;
        private DateTime? _billingPeriodEnd;
        private NutzeinheitVM _selectedNutzeinheit;

        private string _nutzeinheitenHeaderNote;
        private AppointmentVm _selectedCalendarAppointmentVm;
        private AppointmentVm _selectedListAppointmentVm;
        private List<AppointmentTechnician> _prevModelAppAppointments;

        #endregion

        #region ctor

        public OrderPageViewModel(
            IServiceLocator serviceLocator, 
            IEcNavigationService navigationService, 
            IAppointmentService appointmentService, 
            INutzerService nutzerService,
            IBackupService backupService,
            IAmwWebservice webservice,
            IOrderService orderService, 
            IStammdatenService stammdatenService,
            INutzeinheitService nutzeinheitService,
            IEcDialogService dialogService,
            IDeviceService deviceService,
            INewAppointmentDialog newAppointmentDialog,
            ILoginService loginService)
           : base(serviceLocator, navigationService)
        {
            this.appointmentService = appointmentService ?? throw new ArgumentNullException(nameof(appointmentService));
            this.orderService = orderService ?? throw new ArgumentNullException(nameof(orderService));
            this.stammdatenService = stammdatenService ?? throw new ArgumentNullException(nameof(stammdatenService));
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.nutzerService = nutzerService ?? throw new ArgumentNullException(nameof(nutzerService));
            this.backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            this.webservice = webservice ?? throw new ArgumentNullException(nameof(webservice));
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
            this.newAppointmentDialog = newAppointmentDialog ?? throw new ArgumentNullException(nameof(newAppointmentDialog));
            this.loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));

            AppointmentNutzeinheiten = new ObservableCollection<NutzeinheitVM>();
        }

        #endregion

        #region commands

        public Command AppearingCommand => appearingCommand ?? (appearingCommand = new Command(AppearingExecute));

        public Command DisappearingCommand => disappearingCommand ?? (disappearingCommand = new Command(DisappearingExecute));

        public Command OrderEditCommand => orderEditCommand ?? (orderEditCommand = new Command<Appointment>(OrderEditExecute));

        public Command NutzeinheitStateChangeCommand => nutzeinheitStateChangeCommand ?? (nutzeinheitStateChangeCommand = new Command<NutzeinheitOrder>(NutzeinheitStateChangeExecute));

        public Command AddNutzeinheitCommand => addNutzeinheitCommand = addNutzeinheitCommand ?? new Command<Appointment>(AddNutzeinheitExecute);

        public Command OrderIconButtonCommand => orderIconButtonCommand ?? (orderIconButtonCommand = new Command<Appointment>(OrderIconButtonExecute));

        public Command NutzeinheitIconButtonCommand => nutzeinheitIconButtonCommand ?? (nutzeinheitIconButtonCommand = new Command<NutzeinheitVM>(NutzeinheitIconButtonExecute));

        public Command NutzeinheitAmwInfoCommand => nutzeinheitAmwInfoCommand ?? (nutzeinheitAmwInfoCommand = new Command<NutzeinheitVM>(NutzeinheitAmwInfoExecute));

        public Command NewAppointmentCommand => newAppointmentCommand ?? (newAppointmentCommand = new Command<NutzeinheitVM>(NewAppointmentExecute));

        #endregion

        #region properties

        public AppointmentVm SelectedCalendarAppointmentVm
        {
            get { return _selectedCalendarAppointmentVm; }
            set { Set(ref _selectedCalendarAppointmentVm, value); }
        }

        public AppointmentVm SelectedListAppointmentVm
        {
            get { return _selectedListAppointmentVm; }
            set { Set(ref _selectedListAppointmentVm, value); }
        }

        public Appointment SelectedAppointment
        {
            get { return _selectedAppointment; }
            set { Set(ref _selectedAppointment, value); }
        }

        public OrderState OrderState
        {
            get { return _orderState; }
            set { Set(ref _orderState, value); }
        }

        public ProcessState DeviceNutzeinheitenState
        {
            get { return _deviceNutzeinheitenState; }
            set { Set(ref _deviceNutzeinheitenState, value); }
        }

        public DateTime StartTime
        {
            get { return _startTime; }
            set { Set(ref _startTime, value); }
        }

        public DateTime EndTime
        {
            get { return _endTime; }
            set { Set(ref _endTime, value); }
        }

        public string Address
        {
            get { return _address; }
            set { Set(ref _address, value); }
        }

        public string OrderLabel
        {
            get { return _orderLabel; }
            set { Set(ref _orderLabel, value); }
        }

        public string OrderNumber
        {
            get { return _orderNumber; }
            set { Set(ref _orderNumber, value); }
        }

        public bool HasOrderNote
        {
            get { return _hasOrderNote; }
            set { Set(ref _hasOrderNote, value); }
        }

        public string NutzeinheitenHeaderNote
        {
            get { return _nutzeinheitenHeaderNote; }
            set { Set(ref _nutzeinheitenHeaderNote, value); }
        }

        public DateTime? BillingPeriodEnd
        {
            get { return _billingPeriodEnd; }
            set { Set(ref _billingPeriodEnd, value); }
        }

        public NutzeinheitVM SelectedNutzeinheit
        {
            get { return _selectedNutzeinheit; }
            set
            {
                if (value != _selectedNutzeinheit)
                {
                    Set(ref _selectedNutzeinheit, value);
                    NavigateToSelectedSelectedNutzeinheit(_selectedNutzeinheit);
                }
            }
        }

        public ObservableCollection<NutzeinheitVM> AppointmentNutzeinheiten { get; set; }

        public List<AppointmentTechnician> PrevModelAppAppointments
        {
            get { return _prevModelAppAppointments; }
            set { Set(ref _prevModelAppAppointments, value); }
        }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is AppointmentVm selectedAppointment)
            {
                Appointment appointment = appointmentService.GetAppointment(selectedAppointment.Guid);
                SelectedAppointment = appointment;

                if (selectedAppointment.ParentViewModel is AppointmentPageViewModel viewModel)
                {
                    SelectedCalendarAppointmentVm = viewModel.AppointmentsList.SingleOrDefault(x => x.Guid == selectedAppointment.Guid);
                    SelectedListAppointmentVm = viewModel.DataGridAppointmentList.SingleOrDefault(x => x.Guid == selectedAppointment.Guid);
                    PrevModelAppAppointments = viewModel.AppAppointments;
                }

                ReadDbOrder();
            }
            return base.SetupAsync(navigationData);
        }

        public NutzeinheitVM CreateNutzeinheitVmObject(Nutzeinheit nutzeinheit, Appointment appointment)
        {
            Nutzer nutzer = nutzerService.GetLoadedNutzerForDate(nutzeinheit, appointment.From);
            Guid orderGuid = appointment.Order.Guid;
            NutzeinheitOrder nutzeinheitOrder = new NutzeinheitOrder(orderGuid, nutzeinheit.Guid);
            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheit.OrderStates.Where(x => x.OrderGuid == orderGuid).Single();
            ProcessState nutzeinheitProcessState = nutzeinheitOrderState.ProcessState;

            string nutzerSalutationAndName = nutzerService.GetNutzerTitleAndSalutation(nutzer) + nutzerService.GetNutzerName(nutzer);
            string nutzeinheitLocation = nutzeinheit.Location + " " + nutzeinheit.WalkSequence;
            string nutzerAddress = nutzerService.GetNutzerAddress(nutzeinheit);

            bool isNeLocked = false;
            if (nutzeinheitProcessState == ProcessState.Completed)
            {
                isNeLocked = true;
            }

            NutzeinheitVM nutzeinheitVM = new NutzeinheitVM
            {
                NutzeinheitGuid = nutzeinheit.Guid,
                NutzerGuid = nutzer.Guid,
                AppointmentDate = appointment.From,
                NutzeinheitOrder = nutzeinheitOrder,
                NutzerNameLocation = $"{nutzerSalutationAndName},{nutzeinheitLocation}",
                NutzeinheitNote = nutzeinheit.Note,
                NutzeinheitNumber = nutzeinheit.Number,
                NutzeinheitAddress = nutzerAddress,
                NutzeinheitState = nutzeinheitProcessState,
                NutzerKind = nutzer.Kind,
                IsNeLocked = isNeLocked,
                HasNewAppointment = nutzer.NextAppointmentDate.HasValue,
                NutzeinheitUiState = new NutzeinheitUiState()
                {
                    OrderStates = nutzeinheitService.GetNutzeinheitDevicesOrderStates(nutzeinheit, orderGuid),
                    HasSignature = nutzeinheitService.IsNutzeinheitSigned(nutzeinheit, orderGuid),
                    NutzeinheitProcessState = nutzeinheitProcessState,
                    AmwInfoKeyGuid = nutzeinheitOrderState.AmwInfoKeyGuid
                }
            };

            return nutzeinheitVM;
        }

        #endregion

        #region private methods

        private void DisappearingExecute()
        {
            try
            {
                UnregisterWebserviceEventHandlers();
            }
            catch (Exception exception)
            {
                logger.Error(exception, "DisappearingExecute failed");
                throw;
            }
        }

        private void RegisterWebserviceEventHandlers()
        {
            webservice.Info += FileInfoLogging;
            webservice.Error += FileErrorLogging;
            webservice.Warning += FileWarningLogging;
        }

        private void FileErrorLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Error(arg.Label);
        }

        private void FileWarningLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Warning(arg.Label);
        }

        private void FileInfoLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Information(arg.Label);
        }

        private void UnregisterWebserviceEventHandlers()
        {
            webservice.Info -= FileInfoLogging;
            webservice.Error -= FileErrorLogging;
            webservice.Warning -= FileWarningLogging;
        }

        private void ReadDbOrder()
        {
            try
            {
                GetAppointmentOrderInformation();
                GetOrderNutzeinheitInformation();
            }
            catch(Exception e)
            {
                logger.Error(e, "Error while loading appointment data in the OrderPage!");
                throw;
            }
        }

        private void GetAppointmentOrderInformation()
        {
            Order order = SelectedAppointment.Order;
            OrderState orderState = order.OrderState;
            Address aeAddress = order.Abrechnungseinheit.Address;
            bool hasNote = false;

            if (!string.IsNullOrEmpty(order.Note))
            {
                hasNote = true;
            }

            OrderState = orderState;
            StartTime = SelectedAppointment.From;
            EndTime = SelectedAppointment.To;
            HasOrderNote = hasNote;
            OrderNumber = order.Number;
            if (order.Abrechnungseinheit.BilligPeriodEnd.HasValue)
            {
                BillingPeriodEnd = order.Abrechnungseinheit.BilligPeriodEnd;
            }
            Address = string.Format("{0} {1}\n{2} {3}", aeAddress.Street, aeAddress.StreetNumber, aeAddress.Zipcode, aeAddress.City);
            OrderLabel = SelectedAppointment.Order.Label;
        }

        private void GetOrderNutzeinheitInformation()
        {
            List<AppointmentNutzeinheit> appointmentNutzeinheits = SelectedAppointment.AppointmentNutzeinheiten.ToList(); 
            NutzeinheitenHeaderNote = string.Format("Es sind: {0} Nutzeinheiten in dieser AE vorhanden", appointmentNutzeinheits.Count);

            List <Nutzeinheit> appointmentNutzeinheiten = GetListOfNutzeinheitenForAppointment(appointmentNutzeinheits);

            if (appointmentNutzeinheiten.All(x => decimal.TryParse(x.Number, out decimal neNumber)))
            {
                appointmentNutzeinheiten = appointmentNutzeinheiten.OrderBy(x => Convert.ToDecimal(x.Number)).ToList();
            }
            else
            {
                appointmentNutzeinheiten = appointmentNutzeinheiten.OrderBy(x => x.Number).ToList();
            }

            foreach (Nutzeinheit nutzeinheit in appointmentNutzeinheiten)
            {
                NutzeinheitVM nutzeinheitVM = CreateNutzeinheitVmObject(nutzeinheit, SelectedAppointment);

                AppointmentNutzeinheiten.Add(nutzeinheitVM);
            }
            SetDeviceNutzeinheitState();
        }

        private void SetDeviceNutzeinheitState()
        {
            List<NutzeinheitVM> completedNe = AppointmentNutzeinheiten.Where(x => x.NutzeinheitUiState.NutzeinheitProcessState == ProcessState.Completed).ToList();
            List<NutzeinheitVM> startedProgressNe = AppointmentNutzeinheiten.Where(x => x.NutzeinheitUiState.NutzeinheitProcessState == ProcessState.Updating).ToList();
            DeviceNutzeinheitenState = ProcessState.InProgress;
            int sumOfCompletedAndUpdatedNe = completedNe.Count() + startedProgressNe.Count();


            if (AppointmentNutzeinheiten.Count() == 0)
            {
                DeviceNutzeinheitenState = ProcessState.InProgress;
            }
            else if (sumOfCompletedAndUpdatedNe == AppointmentNutzeinheiten.Count())
            {
                DeviceNutzeinheitenState = ProcessState.Completed;
            }
            else
            {
                DeviceNutzeinheitenState = startedProgressNe.Any() || completedNe.Any() ? ProcessState.Updating : ProcessState.InProgress;
            }
        }

        private List<Nutzeinheit> GetListOfNutzeinheitenForAppointment(List<AppointmentNutzeinheit> appointmentNutzeinheits)
        {
            return appointmentNutzeinheits.Select(x => new Nutzeinheit
            {
                Guid = x.Nutzeinheit.Guid,
                Number = x.Nutzeinheit.Number,
                Location = x.Nutzeinheit.Location,
                WalkSequence = x.Nutzeinheit.WalkSequence,
                Note = x.Nutzeinheit.Note,
                IsCreatedByApp = x.Nutzeinheit.IsCreatedByApp,
                Abrechnungseinheit = x.Nutzeinheit.Abrechnungseinheit,
                AbrechnungseinheitGuid = x.Nutzeinheit.AbrechnungseinheitGuid,
                Address = x.Nutzeinheit.Address,
                AddressGuid = x.Nutzeinheit.AddressGuid,
                Nutzer = x.Nutzeinheit.Nutzer,
                NutzeinheitOrderPositions = x.Nutzeinheit.NutzeinheitOrderPositions,
                Devices = x.Nutzeinheit.Devices,
                Photos = x.Nutzeinheit.Photos,
                Signatures = x.Nutzeinheit.Signatures,
                OrderStates = x.Nutzeinheit.OrderStates
            }).ToList();
        }
        private async void OrderEditExecute(Appointment appointment)
        {
            try
            {
                await navigationService.NavigateToAsync<OrderEditPageViewModel>(appointment);
            }
            catch(Exception e)
            {
                logger.Error(e, "Problem occured while trying to navigate to Edit Appointment!");
                throw;
            }
        }

        private void NutzeinheitStateChangeExecute(NutzeinheitOrder nutzeinheitOrder)
        {
            try
            {
                NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitOrder);

                if(nutzeinheitOrderState.ProcessState != ProcessState.Completed)
                {
                    return;
                }

                nutzeinheitOrderState = nutzeinheitService.SetNutzeinheitOrderStateToUpdating(nutzeinheitOrderState);

                NutzeinheitVM nutzeinheit = AppointmentNutzeinheiten.Single(x => x.NutzeinheitGuid == nutzeinheitOrder.NutzeinheitGuid);
                ChangeNutzeinheitState(nutzeinheit, nutzeinheitOrderState);
                ChangeOrderState();
                
                int index = AppointmentNutzeinheiten.IndexOf(nutzeinheit);
                AppointmentNutzeinheiten[index] = nutzeinheit;

                RefreshView();
            }
            catch(Exception e)
            {
                logger.Error(e, "Error occured while attempting to change nutzeinheit status !");
                throw;
            }
        }

        private void ChangeNutzeinheitState(NutzeinheitVM nutzeinheit, NutzeinheitOrderState nutzeinheitOrderState)
        {
            nutzeinheit.NutzeinheitState = nutzeinheitOrderState.ProcessState;
            nutzeinheit.NutzeinheitUiState = new NutzeinheitUiState()
            {
                HasSignature = nutzeinheit.NutzeinheitUiState.HasSignature,
                OrderStates = nutzeinheit.NutzeinheitUiState.OrderStates,
                NutzeinheitProcessState = nutzeinheitOrderState.ProcessState
            };
        }

        private void ChangeOrderState()
        {
            Appointment appointment = appointmentService.GetAppointment(SelectedAppointment.Guid);
            OrderState currentOrderState = appointment.Order.OrderState;

            if (currentOrderState.ProcessState != ProcessState.Completed)
            {
                return;
            }

            currentOrderState.ProcessState = ProcessState.Updating;
            orderService.UpdateOrderState(currentOrderState);
            OrderState = new OrderState()
            {
                ProcessState = ProcessState.Updating
            };
        }

        private async void AddNutzeinheitExecute(Appointment appointment)
        {
            try
            {
                await navigationService.NavigateToAsync<CreateNutzeinheitPageViewModel>(this);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while navigating to DevicePage!");
                throw;
            }
        }

        private void OrderIconButtonExecute(Appointment appointment)
        {
            try
            {
                string orderNote = appointment.Order.Note;
                dialogService.AcceptAsync(orderNote, "OK", "Auftrag Information");
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to execute the command assign to the orderInfo Icon!");
                throw;
            }
        }

        private void NutzeinheitIconButtonExecute(NutzeinheitVM nutzeinheitVM)
        {
            try
            {
                string nutzeinheitNote = nutzeinheitVM.NutzeinheitNote;
                dialogService.AcceptAsync(nutzeinheitNote, "OK", "Nutzeinheit Information");
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to execute the command assign to the nutzeinheitInfo Icon!");
                throw;
            }
        }

        private async void NutzeinheitAmwInfoExecute(NutzeinheitVM nutzeinheitVM)
        {
            try
            {
                var nutzeinheitAmwInfoDialog = new NutzeinheitAmwInfoDialog(stammdatenService, serviceLocator.LocalisationService, nutzeinheitService);
                NutzeinheitOrderState nutzeinheitOrderState = await nutzeinheitAmwInfoDialog.ShowNutzeinheitAmwInfoDialog(nutzeinheitVM);

                Guid? currentAmwInfoGuid = nutzeinheitVM.NutzeinheitUiState.AmwInfoKeyGuid;
                Guid? changedAmwInfoGuid = nutzeinheitOrderState.AmwInfoKeyGuid;

                if (currentAmwInfoGuid == changedAmwInfoGuid )
                {
                    return;
                }

                if (changedAmwInfoGuid == Guid.Empty)
                {
                    nutzeinheitOrderState.AmwInfoKeyGuid = null;
                }

                OrderState.ProcessState = ProcessState.Updating;
                orderService.UpdateOrderState(OrderState);

                if (nutzeinheitOrderState.ProcessState != ProcessState.Creating)
                {
                    if(nutzeinheitOrderState.ProcessState == ProcessState.InProgress && nutzeinheitOrderState.AmwInfoKeyGuid == null)
                    {
                        return;
                    }
                    nutzeinheitOrderState.ProcessState = ProcessState.Updating;
                }

                nutzeinheitOrderState.CompletedDate = DateTime.Now;
                nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);

                List<DeviceOrderState> deviceOrderStatesList = nutzeinheitVM.NutzeinheitUiState.OrderStates;
                foreach (DeviceOrderState deviceOrder in deviceOrderStatesList)
                {
                    if (deviceOrder.ProcessState != ProcessState.InProgress)
                    {
                        continue;
                    }
                    deviceOrder.ProcessState = ProcessState.Updating;
                    deviceService.UpdateDeviceOrderState(deviceOrder);
                }

                if (nutzeinheitOrderState.AmwInfoKeyGuid != null)
                { 
                    await GetNextAppointmentDateAndUpdate(nutzeinheitVM);
                    nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitVM.NutzeinheitOrder);
                    if (nutzeinheitOrderState.ProcessState != ProcessState.Completed)
                    {
                        await DoWebserviceSync(nutzeinheitOrderState);
                    }
                }
                else
                {
                    Nutzer nutzer = nutzerService.GetNutzer(nutzeinheitVM.NutzerGuid);
                    nutzer.NextAppointmentDate = null;
                    nutzerService.UpdateNutzer(nutzer);
                }

                RefreshView();
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to execute nutzeinheitAmwInfo method!");
                throw;
            }
        }

        private async Task DoWebserviceSync(NutzeinheitOrderState nutzeinheitOrderState)
        {
            if (nutzeinheitOrderState == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheitOrderState));
            }

            if(!loginService.GetUserLiveSyncState())
            {
                return;
            }

            try
            {
                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    return;
                }

                Xamarin.Forms.Device.BeginInvokeOnMainThread(() => { dialogService.ShowBusyIndicator(AnimationTypes.Globe, "Der Nutzeinheit Status wird übertragen..."); });

                await backupService.DatabaseDackup();

                await webservice.SyncLiveNutzeinheitAsync();
            }
            catch (Exception exp)
            {
                logger.Error(exp, "SyncLiveAsync(): Exception.");
            }
            finally
            {
                dialogService.HideBusyIndicator();
            }
        }

        private void UpdateAllAppointmentNutzeinheitenStatus()
        {
            Order order = SelectedAppointment.Order;
            var appointmentNutzeinheits = SelectedAppointment.AppointmentNutzeinheiten.Select(x => x.Nutzeinheit).SelectMany(x => x.OrderStates).Where(y => y.OrderGuid == order.Guid).ToList();
            var completedNe = appointmentNutzeinheits.Where(x => x.ProcessState == ProcessState.Completed).ToList();
            var startedProgressNe = appointmentNutzeinheits.Where(x => x.ProcessState == ProcessState.Updating).ToList();

            ProcessState nutzeinheitenStates = ProcessState.InProgress;
            int sumOfCompletedAndUpdatedNe = completedNe.Count() + startedProgressNe.Count();

            if (sumOfCompletedAndUpdatedNe == appointmentNutzeinheits.Count())
            {
                nutzeinheitenStates = ProcessState.Completed;
            }
            else if (startedProgressNe.Any() || completedNe.Any())
            {
                nutzeinheitenStates = ProcessState.Updating;
            }
            else
            {
                nutzeinheitenStates = ProcessState.InProgress;
            }

            if(SelectedCalendarAppointmentVm != null)
            {
                SelectedCalendarAppointmentVm.NutzeinheitenState = nutzeinheitenStates;
            }
            if (SelectedListAppointmentVm != null)
            {
                SelectedListAppointmentVm.NutzeinheitenState = nutzeinheitenStates;
            }
        }

        private async void NavigateToSelectedSelectedNutzeinheit(NutzeinheitVM nutzeinheitVM)
        {
            try
            {
                await navigationService.NavigateToAsync<NutzeinheitPageViewModel>(nutzeinheitVM);
            }
            catch (Exception e)
            {
                logger.Error(e, "Error while processing selected appointment");
                throw;
            }
        }

        private async void NewAppointmentExecute(NutzeinheitVM nutzeinheitVM)
        {
            try
            {
                await GetNextAppointmentDateAndUpdate(nutzeinheitVM);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to create new appointment");
                throw;
            }
        }

        private async Task GetNextAppointmentDateAndUpdate(NutzeinheitVM nutzeinheitVM)
        {
            Nutzer nutzer = nutzerService.GetNutzer(nutzeinheitVM.NutzerGuid);
            DateTime? newAppointmentDate = await newAppointmentDialog.ShowNewAppointmentDialog(nutzer);

            if(nutzer.NextAppointmentDate == newAppointmentDate)
            {
                return;
            }

            UpdateNextAppoitnmentDateInParentVM(nutzer, newAppointmentDate);
            nutzer.NextAppointmentDate = newAppointmentDate;
            nutzeinheitVM.HasNewAppointment = newAppointmentDate.HasValue;
            nutzerService.UpdateNutzer(nutzer);

            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitVM.NutzeinheitOrder);
            if(nutzeinheitOrderState.ProcessState != ProcessState.Completed)
            {
                await DoWebserviceSync(nutzeinheitOrderState);
            }
          
            RefreshView();
        }

        private void AppearingExecute()
        {
            try
            {
                RegisterWebserviceEventHandlers();
                RefreshView();
            }
            catch (Exception exception)
            {
                logger.Error(exception, "Exception occured while executing Appearing assignment method!");
                throw;
            }
        }

        private void RefreshView()
        {
            if (AppointmentNutzeinheiten.Count > 0)
            {
                Guid appointmentGuid = SelectedAppointment.Guid;
                Appointment appointment = appointmentService.GetAppointment(appointmentGuid);
                SelectedAppointment = appointment;

                //updating auftrag info
                GetAppointmentOrderInformation();

                //update previous page statuses
                if (SelectedCalendarAppointmentVm != null)
                {
                    SelectedCalendarAppointmentVm.OrderState = appointment.Order.OrderState;
                    List<Order> orderList = PrevModelAppAppointments.Select(x => x.Appointment.Order).Where(x => x.Guid == appointment.Order.Guid).ToList();
                    UpdateAllAppointmentsForThisOrder(orderList, appointment);
                }
                if (SelectedListAppointmentVm != null)
                {
                    SelectedListAppointmentVm.OrderState = appointment.Order.OrderState;
                    List<Order> orderList = PrevModelAppAppointments.Select(x => x.Appointment.Order).Where(x => x.Guid == appointment.Order.Guid).ToList();
                    UpdateAllAppointmentsForThisOrder(orderList, appointment);
                }

                UpdateAllAppointmentNutzeinheitenStatus();

                if (SelectedCalendarAppointmentVm != null)
                {
                    AppointmentTechnician selectedAppointmentInPreviousVM = PrevModelAppAppointments.Where(x => x.Appointment.Guid == SelectedCalendarAppointmentVm.Guid).SingleOrDefault();
                    selectedAppointmentInPreviousVM.Appointment = appointment;
                }
                else
                {
                    AppointmentTechnician selectedAppointmentInPreviousVM = PrevModelAppAppointments.Where(x => x.Appointment.Guid == SelectedListAppointmentVm.Guid).SingleOrDefault();
                    selectedAppointmentInPreviousVM.Appointment = appointment;
                }

                //updating list of its nutzeinheit

                List<AppointmentNutzeinheit> appointmentNutzeinheits = appointment.AppointmentNutzeinheiten.ToList();
                List<Nutzeinheit> appointmentNutzeinheiten = GetListOfNutzeinheitenForAppointment(appointmentNutzeinheits);

                if (appointmentNutzeinheiten.All(x => decimal.TryParse(x.Number, out decimal neNumber)))
                {
                    appointmentNutzeinheiten = appointmentNutzeinheiten.OrderBy(x => Convert.ToDecimal(x.Number)).ToList();
                }
                else
                {
                    appointmentNutzeinheiten = appointmentNutzeinheiten.OrderBy(x => x.Number).ToList();
                }

                NutzeinheitenHeaderNote = string.Format("Es sind: {0} Nutzeinheiten für diese AE vorhanden", appointmentNutzeinheits.Count);

                AppointmentNutzeinheiten.Clear();
                foreach (Nutzeinheit nutzeinheit in appointmentNutzeinheiten)
                {
                    NutzeinheitVM nutzeinheitVM = CreateNutzeinheitVmObject(nutzeinheit, appointment);
                    AppointmentNutzeinheiten.Add(nutzeinheitVM);
                }
                SetDeviceNutzeinheitState();
            }
        }

        private void UpdateAllAppointmentsForThisOrder(List<Order> orderList, Appointment appointment)
        {
            foreach(Order order in orderList)
            {
                order.OrderState = appointment.Order.OrderState;
            }
        }

        private void UpdateNextAppoitnmentDateInParentVM(Nutzer nutzer, DateTime? newAppointmentDate)
        {
            if(SelectedCalendarAppointmentVm != null)
            {
                if (SelectedCalendarAppointmentVm.ParentViewModel is AppointmentPageViewModel viewModel)
                {
                    Nutzer nutzerInParentVmAppointments = viewModel.AppAppointments.SelectMany(x => x.Appointment.AppointmentNutzeinheiten)
                                                                                   .SelectMany(x => x.Nutzeinheit.Nutzer)
                                                                                   .Where(x => x.Guid == nutzer.Guid).FirstOrDefault();
                    nutzerInParentVmAppointments.NextAppointmentDate = newAppointmentDate;
                }
            }
            else
            {
                if (SelectedListAppointmentVm.ParentViewModel is AppointmentPageViewModel viewModel)
                {
                    Nutzer nutzerInParentVmAppointments = viewModel.AppAppointments.SelectMany(x => x.Appointment.AppointmentNutzeinheiten)
                                                                                   .SelectMany(x => x.Nutzeinheit.Nutzer)
                                                                                   .Where(x => x.Guid == nutzer.Guid).FirstOrDefault();
                    nutzerInParentVmAppointments.NextAppointmentDate = newAppointmentDate;
                }
            }
        }

        #endregion
    }
}
