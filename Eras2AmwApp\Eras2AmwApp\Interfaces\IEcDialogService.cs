﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IEcDialogService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Interfaces
{
    using System.Threading.Tasks;
    using Services;

    using Syncfusion.SfBusyIndicator.XForms;

    public interface IEcDialogService
    {
        void Info(string message, string title = null);

        Task<DialogResponse> AcceptAsync(string message, string acceptButtonText = null, string title = null);

        Task<DialogResponse> AcceptDeclineAsync(string message, string acceptButtonText, string declineButtonText, string title = null);
        
        void ShowBusyIndicator(AnimationTypes animationType = AnimationTypes.Gear, string title = "");

        void SetBusyIndicatorText(string text);

        void HideBusyIndicator();
    }
}