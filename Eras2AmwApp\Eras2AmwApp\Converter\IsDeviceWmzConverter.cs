﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IsDeviceWmzConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.Domain.Eras2Amw.Enums;
using System;
using System.Globalization;
using Xamarin.Forms;

namespace Eras2AmwApp.Converter
{
    public class IsDeviceWmzConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            DeviceClass orderKind = (DeviceClass)value;
            return orderKind == DeviceClass.WMZ;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new InvalidOperationException("IsDeviceHkvvConverter can only be used OneWay.");
        }
    }
}
