﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DatabaseCreator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Bootstrap
{
    using System;
    using Common.Ioc;
    using Database.Contexts;
    using Database.Interfaces;
    using Microsoft.EntityFrameworkCore;
    using Serilog;
    
    public class DatabasesCreator
    {
        private readonly ILogger logger;

        private readonly IDbContextFactory contextFactory;

        public DatabasesCreator(ILogger logger)
        {
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));

            contextFactory = NinjectKernel.Get<IDbContextFactory>();
        }

        public void Start()
        {
            try
            {
                Migrate();
            }
            catch (Exception e)
            { 
                logger.Error(e, "Database Setup failed");
                throw;
            }
        }

        private void Migrate()
        {
            using (Eras2AmwContext amwContext = contextFactory.CreateAmw())
            {
                amwContext.Database.Migrate();
            }

            using (Eras2AppContext appContext = contextFactory.CreateApp())
            {
                appContext.Database.Migrate();
            }
        }
    }
}