﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:behaviors="clr-namespace:Eras2AmwApp.Behaviors"
             xmlns:syncfusion="clr-namespace:Syncfusion.SfCalendar.XForms;assembly=Syncfusion.SfCalendar.XForms"
             xmlns:tabView="clr-namespace:Syncfusion.XForms.TabView;assembly=Syncfusion.SfTabView.XForms"
             xmlns:converter="clr-namespace:Eras2AmwApp.Converter"
             xmlns:buttons="clr-namespace:Syncfusion.XForms.Buttons;assembly=Syncfusion.Buttons.XForms"
             xmlns:inputLayout="clr-namespace:Syncfusion.XForms.TextInputLayout;assembly=Syncfusion.Core.XForms"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:syncfusionGrid="clr-namespace:Syncfusion.SfDataGrid.XForms;assembly=Syncfusion.SfDataGrid.XForms" 
             xmlns:syncfusionTree="clr-namespace:Syncfusion.XForms.TreeView;assembly=Syncfusion.SfTreeView.XForms"
             xmlns:sfPager="clr-namespace:Syncfusion.SfDataGrid.XForms.DataPager;assembly=Syncfusion.SfDataGrid.XForms"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.AppointmentPage">

    <ContentPage.Resources>
        <converter:OrderStateConverter x:Key="OrderStateConverter"></converter:OrderStateConverter>
        <converter:ShortTimeConverter x:Key="ShortTimeConverter"></converter:ShortTimeConverter>
        <converter:DataGridTimeFormatConverter x:Key="DataGridTimeFormatConverter"></converter:DataGridTimeFormatConverter>
        <converter:DeviceNutzeinheitOrderConverter x:Key="DeviceNutzeinheitOrderConverter"></converter:DeviceNutzeinheitOrderConverter>
    </ContentPage.Resources>

    <ContentPage.Behaviors>
        <behaviors:EventToCommandBehavior EventName="Appearing" Command="{Binding AppearingCommand}" />
        <behaviors:EventToCommandBehavior EventName="Disappearing" Command="{Binding DisappearingCommand}" />
    </ContentPage.Behaviors>

    <tabView:SfTabView VisibleHeaderCount="4" TabHeaderPosition="Bottom" EnableSwiping="False">

        <tabView:SfTabView.Behaviors>
            <behaviors:EventToCommandBehavior EventName="SelectionChanged" Command="{Binding TabChangedCommand}" />
        </tabView:SfTabView.Behaviors>

        <tabView:SfTabItem Title="Kalender">

            <tabView:SfTabItem.Content>

                <Grid>

                    <Grid.RowDefinitions>
                        <RowDefinition Height="35*"></RowDefinition>
                        <RowDefinition Height="65*"></RowDefinition>
                    </Grid.RowDefinitions>

                    <syncfusion:SfCalendar  Grid.Row="0"
                                            IsVisible="{Binding ShowCalendar}"
                                            ShowInlineEvents="False" 
                                            SelectionMode="SingleSelection"
                                            ShowLeadingAndTrailingDays="True"
                                            NavigationDirection="Horizontal"
                                            FirstDayofWeek="1"
                                            SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                            DataSource="{Binding CalendarInlineEvents}"
                                            ViewMode="MonthView"
                                            ShowYearView="False"
                                            MaximumEventIndicatorCount="1">

                        <syncfusion:SfCalendar.Behaviors>
                            <behaviors:EventToCommandBehavior   EventName="OnCalendarTapped"
                                                                Command="{Binding SelectedCalendarDateCommand}">
                            </behaviors:EventToCommandBehavior>
                        </syncfusion:SfCalendar.Behaviors>

                        <syncfusion:SfCalendar.MonthViewSettings>
                            <syncfusion:MonthViewSettings   DateTextAlignment="Center"
                                                        HeaderBackgroundColor="#3498db"
                                                        HeaderTextColor="White"
                                                        HeaderFont="14" 
                                                        DayHeaderBackgroundColor="#3498db"
                                                        DayHeaderFontSize="12"
                                                        DayCellFont="12"
                                                        DayHeaderTextColor="White"
                                                        WeekEndBackgroundColor="LightGray"
                                                        TodaySelectionTextColor="Black"
                                                        TodaySelectionBackgroundColor="#538EEC"
                                                        BorderColor="#538EEC"
                                                        CellGridOptions="Both"
                                                        PreviousMonthTextColor="LightGray"
                                                        CurrentMonthBackgroundColor="White"
                                                        DateSelectionColor="#3498db">

                            </syncfusion:MonthViewSettings>


                        </syncfusion:SfCalendar.MonthViewSettings>

                    </syncfusion:SfCalendar>

                    <ListView   x:Name="AppointmentList"
                                Margin="10,2,10,2"
                                Grid.Row="1"
                                ItemsSource="{Binding AppointmentsList}"
                                SelectedItem="{Binding SelectedListAppointment}"
                                SelectionMode="Single"
                                HasUnevenRows="True"
                                CachingStrategy="RecycleElement">

                        <ListView.Header>
                            <StackLayout Orientation="Horizontal"
                                        Spacing="0"
                                        HorizontalOptions="StartAndExpand">

                                <Label  Text="{Binding NavigationHeader}"
                                    Margin="5,0,0,0"
                                    FontSize="Large" />

                                <Image  x:Name="TerminInfo"
                                    Margin="5,0,5,0"
                                    Source="infoIcon.png"
                                    HeightRequest="30"
                                    WidthRequest="30"
                                    VerticalOptions="Center"
                                    HorizontalOptions="End">

                                    <Image.GestureRecognizers>
                                        <TapGestureRecognizer   Command="{Binding ShowTerminInfoCommand}"
                                                            NumberOfTapsRequired="1" />
                                    </Image.GestureRecognizers>
                                </Image>

                            </StackLayout>
                        </ListView.Header>

                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <ViewCell>
                                    <Frame  Margin="2"
                                        Padding="0"
                                        BorderColor="LightGray"
                                        CornerRadius="0"
                                        HasShadow="True">

                                        <Grid RowSpacing="0" ColumnSpacing="0">

                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                            </Grid.RowDefinitions>

                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="6"></ColumnDefinition>
                                                <ColumnDefinition Width="9"></ColumnDefinition>
                                                <ColumnDefinition Width="20*"></ColumnDefinition>
                                                <ColumnDefinition Width="55*"></ColumnDefinition>
                                                <ColumnDefinition Width="45"></ColumnDefinition>
                                                <ColumnDefinition Width="50"></ColumnDefinition>
                                            </Grid.ColumnDefinitions>

                                            <Label  x:Name="StatusColor"
                                                Grid.Row="0"
                                                Grid.RowSpan="3"
                                                Grid.Column="0"
                                                BackgroundColor="{Binding OrderState, Converter={StaticResource OrderStateConverter}}">

                                            </Label>

                                            <Label  x:Name="DeviceColor"
                                                Grid.Row="0"
                                                Grid.RowSpan="3"
                                                Grid.Column="1"
                                                Margin="3,0,0,0"
                                                BackgroundColor="{Binding NutzeinheitenState, Converter={StaticResource DeviceNutzeinheitOrderConverter}}">

                                            </Label>

                                            <Label x:Name="StartTime"
                                                Grid.Row="0"
                                                Grid.Column="2"
                                                Margin="5,5,5,5"
                                                Text="{Binding StartTime, Converter={StaticResource ShortTimeConverter}}"
                                                TextColor="Black"
                                                FontSize="Small"
                                                FontAttributes="Bold">

                                            </Label>

                                            <Label x:Name="EndTime"
                                                Grid.Row="1"
                                                Grid.Column="2"
                                                Margin="5,5,5,5"
                                                Text="{Binding EndTime, Converter={StaticResource ShortTimeConverter}}"
                                                TextColor="Black"
                                                FontSize="Small"
                                                FontAttributes="Bold">

                                            </Label>

                                            <Label x:Name="MainText"
                                                Grid.Row="0"
                                                Grid.Column="3"
                                                Margin="5,5,30,0"
                                                Text="{Binding Address}"
                                                TextColor="Black"
                                                FontSize="Small"
                                                FontAttributes="Bold">

                                            </Label>

                                            <Label x:Name="MinorText"
                                                Grid.Row="1"
                                                Grid.Column="3"
                                                Margin="5,0,30,5"
                                                Text="{Binding OrderLabel}"
                                                FontSize="12">

                                            </Label>

                                            <Label  x:Name="OrderNumber"
                                                Grid.Row="0"
                                                Grid.Column="4"
                                                Grid.ColumnSpan="2"
                                                HorizontalTextAlignment="End"
                                                Margin="0,5,5,0"
                                                Text="{Binding OrderNumber}"
                                                FontSize="Small"
                                                FontAttributes="Bold">

                                            </Label>

                                            <Image  x:Name="Icon1"
                                                Grid.Row="1"
                                                Grid.Column="4"
                                                Margin="0"
                                                Source="infoIcon.png"
                                                WidthRequest="30"
                                                HeightRequest="30"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                IsEnabled="{Binding HasNote}"
                                                IsVisible="{Binding HasNote}" >

                                                <Image.GestureRecognizers>
                                                    <TapGestureRecognizer   Command="{Binding Path=BindingContext.IconButtonCommand, Source={x:Reference AppointmentList}}"
                                                                        CommandParameter="{Binding Guid}"
                                                                        NumberOfTapsRequired="1" />
                                                </Image.GestureRecognizers>
                                            </Image>

                                            <Image  Grid.Row="1"
                                                Grid.Column="5"
                                                Margin="0"
                                                Source="nelist.png"
                                                WidthRequest="40"
                                                HeightRequest="40"
                                                VerticalOptions="Start"
                                                HorizontalOptions="End" >

                                                <Image.GestureRecognizers>
                                                    <TapGestureRecognizer   Command="{Binding Path=BindingContext.NeListCommand, Source={x:Reference AppointmentList}}"
                                                                        CommandParameter="{Binding Guid}"
                                                                        NumberOfTapsRequired="1" />
                                                </Image.GestureRecognizers>
                                            </Image>

                                        </Grid>
                                    </Frame>
                                </ViewCell>

                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>

                </Grid>

            </tabView:SfTabItem.Content>

        </tabView:SfTabItem>

        <tabView:SfTabItem Title="Aufträge Liste">

            <tabView:SfTabItem.Content>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="50*"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="50*"></ColumnDefinition>
                        <ColumnDefinition Width="50*"></ColumnDefinition>
                    </Grid.ColumnDefinitions>

                    <SearchBar  x:Name="filterText"
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        IsVisible="true"
                                        Placeholder="Suche"
                                        Text="{Binding SearchbarText}">

                    </SearchBar>

                    <StackLayout Grid.Row="0"
                                         Grid.Column="1"
                                         Orientation="Horizontal">

                        <Label  Text="Gruppieren:"
                                        VerticalOptions="Center"
                                        FontSize="Medium">

                        </Label>

                        <Picker Title="Wähle eine Option:"
                                        HorizontalOptions="FillAndExpand"
                                        ItemsSource="{Binding GroupOptionList}"
                                        SelectedItem="{Binding SelectedGroupOption}"
                                        FontSize="Medium">

                        </Picker>

                    </StackLayout>

                    <Label  x:Name="TermineCount"
                                    Grid.Row="1"
                                    Grid.Column="0"
                                    Grid.ColumnSpan="2"
                                    Text="{Binding TerminCount}"
                                    Margin="5,0,5,0"
                                    FontSize="Medium"/>

                    <syncfusionGrid:SfDataGrid  x:Name="appointmentDataGrid"
                                                Grid.Row="2"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="2"
                                                ColumnSizer="Star"
                                                AutoGenerateColumns="False"
                                                AllowResizingColumn="True"
                                                ItemsSource="{Binding PagedSource, Source={x:Reference dataPager}}" 
                                                AllowSorting="True"
                                                SelectionMode="Single"
                                                NavigationMode="Row" 
                                                CanUseViewFilter="True"
                                                AllowPullToRefresh="False"
                                                SelectedItem="{Binding SelectedGridAppointment}"
                                                GroupCaptionTextFormat="{}{ColumnName}: {Key}">

                        <syncfusionGrid:SfDataGrid.Columns x:TypeArguments="syncfusion:Columns">

                            <syncfusionGrid:GridTemplateColumn MappingName="OrderState" HeaderText="S" Width="5" AllowSorting="False">
                                <syncfusionGrid:GridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Label BackgroundColor="{Binding OrderState, Converter={StaticResource OrderStateConverter}}"/>
                                    </DataTemplate>
                                </syncfusionGrid:GridTemplateColumn.CellTemplate>
                            </syncfusionGrid:GridTemplateColumn>

                            <syncfusionGrid:GridTextColumn  HeaderText="Nummer" 
                                                                    HeaderCellTextSize="10"
                                                                    CellTextSize="10"
                                                                    HeaderFontAttribute="Bold"
                                                                    Width="60"
                                                                    MappingName="OrderNumber" />

                            <syncfusionGrid:GridTextColumn  HeaderText="Startzeit" 
                                                                    HeaderCellTextSize="10"
                                                                    CellTextSize="10"
                                                                    HeaderFontAttribute="Bold"
                                                                    Width="100"
                                                                    MappingName="StartTime" 
                                                                    DisplayBinding="{Binding StartTime, Converter={StaticResource DataGridTimeFormatConverter}}"/>

                            <syncfusionGrid:GridTextColumn  HeaderText="Endzeit" 
                                                                    HeaderCellTextSize="10"
                                                                    CellTextSize="10"
                                                                    HeaderFontAttribute="Bold"
                                                                    Width="100"
                                                                    MappingName="EndTime" 
                                                                    DisplayBinding="{Binding EndTime, Converter={StaticResource DataGridTimeFormatConverter}}"/>

                            <syncfusionGrid:GridTextColumn  HeaderText="Datum" 
                                                                    HeaderCellTextSize="10"
                                                                    CellTextSize="10"
                                                                    IsHidden="True"
                                                                    HeaderFontAttribute="Bold"
                                                                    MappingName="DateTime">

                            </syncfusionGrid:GridTextColumn>

                            <syncfusionGrid:GridTextColumn  HeaderText="Adresse" 
                                                                    HeaderCellTextSize="10"
                                                                    CellTextSize="10"
                                                                    HeaderFontAttribute="Bold"
                                                                    MappingName="Address" />

                            <syncfusionGrid:GridTemplateColumn  HeaderText="Auftrag Notiz" 
                                                                        HeaderFontAttribute="Bold"
                                                                        HeaderCellTextSize="10"
                                                                        Width="50"
                                                                        MappingName="HasNote">
                                <syncfusionGrid:GridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Image  x:Name="Icon1"
                                                        Margin="15"
                                                        Source="baseline_info_black_18dp.png"
                                                        WidthRequest="20"
                                                        HeightRequest="20"
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Center"
                                                        IsEnabled="{Binding HasNote}"
                                                        IsVisible="{Binding HasNote}" >

                                            <Image.GestureRecognizers>
                                                <TapGestureRecognizer   Command="{Binding Path=BindingContext.IconButtonCommand, Source={x:Reference AppointmentList}}"
                                                                                CommandParameter="{Binding Guid}"
                                                                                NumberOfTapsRequired="1" />
                                            </Image.GestureRecognizers>
                                        </Image>
                                    </DataTemplate>
                                </syncfusionGrid:GridTemplateColumn.CellTemplate>

                            </syncfusionGrid:GridTemplateColumn>

                        </syncfusionGrid:SfDataGrid.Columns>

                    </syncfusionGrid:SfDataGrid>

                    <sfPager:SfDataPager    x:Name ="dataPager"
                                            Grid.Row="3"
                                            Grid.Column="0"
                                            Grid.ColumnSpan="2"          
                                            PageCount="1"
                                            PageSize="20"
                                            HeightRequest ="50"
                                            NumericButtonCount="20"
                                            Source="{Binding DataGridAppointmentList}">

                    </sfPager:SfDataPager>

                </Grid>
            </tabView:SfTabItem.Content>

        </tabView:SfTabItem>

        <tabView:SfTabItem Title="Nachtermine">
            <tabView:SfTabItem.Content>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Label  Text="Nachtermine"
                                Grid.Row="0"
                                HorizontalOptions="Center"
                                Margin="5,5,5,10"
                                FontSize="Large" />

                        <syncfusionTree:SfTreeView  Grid.Row="1"
                                                    x:Name="treeView"
                                                    ItemsSource="{Binding TechnicianAppointmentVMs}"
                                                    ChildPropertyName="AppointmentTermine"
                                                    NotificationSubscriptionMode="CollectionChange"
                                                    NodePopulationMode="Instant"
                                                    TapCommand="{Binding TreeViewItemTappedCommand}">

                            <syncfusionTree:SfTreeView.ItemTemplate>
                                <DataTemplate>
                                    <Grid Padding="5,5,5,0">

                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"></RowDefinition>
                                        </Grid.RowDefinitions>

                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="70*"></ColumnDefinition>
                                            <ColumnDefinition Width="30*"></ColumnDefinition>
                                        </Grid.ColumnDefinitions>

                                        <Label  Grid.Column="0"
                                                Text="{Binding FileName}"
                                                VerticalTextAlignment="Center"/>

                                        <Label  Grid.Column="1"
                                                Text="{Binding NutzerList.Count, StringFormat='({0} Nutzer)'}"
                                                HorizontalTextAlignment="End"/>

                                    </Grid>
                                </DataTemplate>
                            </syncfusionTree:SfTreeView.ItemTemplate>
                        </syncfusionTree:SfTreeView>
                    </Grid>

            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>

        <tabView:SfTabItem Title="Einstellungen">
            <tabView:SfTabItem.Content>
                <ScrollView>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="20"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="20"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="20"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="5*"></ColumnDefinition>
                            <ColumnDefinition Width="45*"></ColumnDefinition>
                            <ColumnDefinition Width="45*"></ColumnDefinition>
                            <ColumnDefinition Width="5*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <Label Grid.Row="0"
                               Grid.Column="1"
                               Grid.ColumnSpan="2"
                               Text="{markupExtensions:Localisation DeviceInfo}"
                               FontSize="Large"
                               FontAttributes="Bold"
                               HorizontalOptions="Center">
                        </Label>

                        <Label  Grid.Row="2"
                                Grid.Column="1"
                                Text="{markupExtensions:Localisation Manufacturer}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="2"
                                Grid.Column="2"
                                Text="{Binding DeviceManufacturer}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="3"
                                Grid.Column="1"
                                Text="{markupExtensions:Localisation DeviceModel}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="3"
                                Grid.Column="2"
                                Text="{Binding DeviceModel}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="4"
                                Grid.Column="1"
                                Text="OS"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="4"
                                Grid.Column="2"
                                Text="{Binding DevicePlatform}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="5"
                                Grid.Column="1"
                                Text="OS Version"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="5"
                                Grid.Column="2"
                                Text="{Binding DevicePlatformVersion}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="6"
                                Grid.Column="1"
                                Text="App Version"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="6"
                                Grid.Column="2"
                                Text="{Binding AppVersion}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="7"
                                Grid.Column="1"
                                Text="{markupExtensions:Localisation DbVersion}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="7"
                                Grid.Column="2"
                                Text="{Binding DatabaseVersion}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="8"
                                Grid.Column="1"
                                Text="Internet"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="8"
                                Grid.Column="2"
                                Text="{Binding InternetAvailableInfo}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label Grid.Row="10"
                               Grid.Column="1"
                               Grid.ColumnSpan="2"
                               Text="{markupExtensions:Localisation UserInfo}"
                               FontSize="Large"
                               FontAttributes="Bold"
                               HorizontalOptions="Center">
                        </Label>

                        <Label  Grid.Row="12"
                                Grid.Column="1"
                                Text="Live-Synchronisierung"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <CheckBox   x:Name="liveSyncCheckbox"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    Color="Black"
                                    IsChecked="{Binding IsLiveSyncOn}"
                                    IsEnabled="{Binding IsAdminTestUser}"
                                    IsVisible="{Binding IsAdminTestUser}"
                                    Grid.Row="12"
                                    Grid.Column="2">

                        </CheckBox>

                        <Label  Grid.Row="13"
                                Grid.Column="1"
                                Text="{markupExtensions:Localisation Username}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="13"
                                Grid.Column="2"
                                Text="{Binding Username}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="14"
                                Grid.Column="1"
                                Text="{markupExtensions:Localisation CustomerName}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="14"
                                Grid.Column="2"
                                Text="{Binding Customername}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="15"
                                Grid.Column="1"
                                Text="Webservice URL"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="15"
                                Grid.Column="2"
                                Text="{Binding WebserviceUrl}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <!--<Label  Grid.Row="15"
                                Grid.Column="1"
                                Text="{markupExtensions:Localisation UserEmail}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                        Margin="5"
                                                        ContainerBackgroundColor="White"
                                                        ContainerType="Outlined"  
                                                        Grid.Row="15"
                                                        Grid.Column="2"
                                                        VerticalOptions="Center"
                                                        ErrorText="{Binding UserEmailErrorText}" 
                                                        HasError="{Binding UserEmailHasError}">

                            <Entry  Text="{Binding UserEmail}"
                                    FontSize="Medium"
                                    VerticalOptions="StartAndExpand">

                                <Entry.Behaviors>
                                    <behaviors:EventToCommandBehavior   EventName="Unfocused" 
                                                                        Command="{Binding UpdateUserEntryCommand}">
                                    </behaviors:EventToCommandBehavior>
                                </Entry.Behaviors>
                            </Entry>

                        </inputLayout:SfTextInputLayout>-->

                        <buttons:SfButton   Margin="5,5,5,0" 
                                            Text="{markupExtensions:Localisation LokalBackup}"
                                            Command="{Binding LocalBackupCommand}" 
                                            Grid.Row="16" Grid.Column="1"
                                            VerticalOptions="Center"
                                            IsEnabled="{Binding IsAdminTestUser}"
                                            IsVisible="{Binding IsAdminTestUser}"
                                            Style="{StaticResource SyncfusionButtonBlue}">
                        </buttons:SfButton>

                        <buttons:SfButton   Margin="5,5,5,0" 
                                            Text="{markupExtensions:Localisation RemoteBackup}"
                                            Command="{Binding RemoteBackupCommand}" 
                                            Grid.Row="16" Grid.Column="2"
                                            VerticalOptions="Center"
                                            IsEnabled="{Binding IsAdminTestUser}"
                                            IsVisible="{Binding IsAdminTestUser}"
                                            Style="{StaticResource SyncfusionButtonBlue}"/>

                        <buttons:SfButton   Margin="5,5,5,0" 
                                            Text="{markupExtensions:Localisation Synchronize}"
                                            Command="{Binding SyncOrdersCommand}" 
                                            Grid.Row="18" Grid.Column="1"
                                            VerticalOptions="Center"
                                            IsEnabled="{Binding IsAdminTestUser}"
                                            IsVisible="{Binding IsAdminTestUser}"
                                            Style="{StaticResource SyncfusionButtonBlue}"/>

                        <buttons:SfButton   Margin="5,5,5,0" 
                                            Text="{markupExtensions:Localisation DeleteDb}"
                                            Command="{Binding DeleteAmwDatabaseCommand}" 
                                            Grid.Row="17" Grid.Column="1"
                                            VerticalOptions="Center"
                                            IsEnabled="{Binding IsAdminTestUser}"
                                            IsVisible="{Binding IsAdminTestUser}"
                                            Style="{StaticResource SyncfusionButtonBlue}"/>

                        <buttons:SfButton   Margin="5,5,5,0" 
                                            Text="{markupExtensions:Localisation FactoryReset}"
                                            Command="{Binding FactoryResetCommand}" 
                                            Grid.Row="17" Grid.Column="2"
                                            VerticalOptions="Center"
                                            IsEnabled="{Binding IsAdminTestUser}"
                                            IsVisible="{Binding IsAdminTestUser}"
                                            Style="{StaticResource SyncfusionButtonBlue}"/>

                    </Grid>
                </ScrollView>
            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>

    </tabView:SfTabView>

</ContentPage>