﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="WatermeterEditPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using FluentValidation;
    using ViewModels;

    public class WatermeterEditPageViewModelValidator : AbstractValidator<WatermeterEditPageViewModel>
    {
        public WatermeterEditPageViewModelValidator()
        {
            RuleFor(x => x.SelectedRoom)
                .NotEmpty().WithMessage("Der Raum darf nicht leer sein.");

            RuleFor(x => x.DeviceNumber)
                .NotEmpty().WithMessage("Die Gerätenummer darf nicht leer sein.");
        }
    }
}
