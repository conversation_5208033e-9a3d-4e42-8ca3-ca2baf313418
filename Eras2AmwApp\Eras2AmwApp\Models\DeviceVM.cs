﻿//  <copyright file="DeviceVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.BusinessLogic.Interfaces;
using Eras2AmwApp.BusinessLogic.Models;
using Eras2AmwApp.Domain.Eras2Amw.Enums;
using Eras2AmwApp.Domain.Eras2Amw.Models;
using Eras2AmwApp.Interfaces;
using Eras2AmwApp.ViewModels;
using GalaSoft.MvvmLight;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Eras2AmwApp.Models
{
    public class DeviceVM : ViewModelBase, ISupportParentViewModel
    {
        public NutzeinheitPageViewModel GetParentProperty()
        {
            return ParentViewModel as NutzeinheitPageViewModel;
        }

        private EcViewModelBase _parentViewModel;
        public EcViewModelBase ParentViewModel 
        { 
            get { return _parentViewModel; } 
            set { _parentViewModel = value; } 
        }

        private DeviceClass deviceClass;
        public DeviceClass DeviceClass
        {
            get { return deviceClass; }
            set { Set(ref deviceClass, value); }
        }

        private bool _isDeviceMaintained;
        public bool IsDeviceMaintained
        {
            get { return _isDeviceMaintained; }
            set
            {
                if (value != _isDeviceMaintained)
                {
                    Set(ref _isDeviceMaintained, value);

                    if (!ParentViewModel.IsSetupDone)
                    {
                        return;
                    }

                    if (IsDeviceListEditedOrCreated)
                    {
                        IsDeviceListEditedOrCreated = !IsDeviceListEditedOrCreated;
                        return;
                    }

                    Xamarin.Forms.Device.BeginInvokeOnMainThread(async () =>
                    {
                        if (await CheckIfNutzeinheitAmwIsSet () && await CheckIfSignatureNeedsToBeOverwritten())
                        {
                            MaintainDevice();
                        }
                        else
                        {
                            Set(ref _isDeviceMaintained, !_isDeviceMaintained);
                        }
                    });
                }
            }
        }

        private bool _isDeviceListEditedOrCreated;
        public bool IsDeviceListEditedOrCreated
        {
            get { return _isDeviceListEditedOrCreated; }
            set { Set(ref _isDeviceListEditedOrCreated, value); }
        }

        private string _deviceNumber;
        public string DeviceNumber
        {
            get { return _deviceNumber; }
            set { Set(ref _deviceNumber, value); }
        }

        private string _deviceLabel;
        public string DeviceLabel
        {
            get { return _deviceLabel; }
            set { Set(ref _deviceLabel, value); }
        }

        private string _deviceRoom;
        public string DeviceRoom
        {
            get { return _deviceRoom; }
            set { Set(ref _deviceRoom, value); }
        }

        private List<Photo> _photos;
        public List<Photo> Photos
        {
            get { return _photos; }
            set { Set(ref _photos, value); }
        }

        private string _deviceOngoingNumber;
        public string DeviceOngoingNumber
        {
            get { return _deviceOngoingNumber; }
            set { Set(ref _deviceOngoingNumber, value); }
        }

        public IDevice Device { get; set; }

        private DeviceUiState _deviceUiState;
        public DeviceUiState DeviceUiState
        {
            get { return _deviceUiState; }
            set { Set(ref _deviceUiState, value); }
        }

        private async Task<bool> CheckIfNutzeinheitAmwIsSet()
        {
            NutzeinheitPageViewModel partent = GetParentProperty();
            return await partent.CheckIfNeAmwInfoKeyExist();
        }

        private async Task<bool> CheckIfSignatureNeedsToBeOverwritten()
        {
            NutzeinheitPageViewModel partent = GetParentProperty();
            return await partent.CheckIfOverwriteSignature();
        }

        private void MaintainDevice()
        {
            NutzeinheitPageViewModel partent = GetParentProperty();
            DeviceUiState deviceUiState = partent.MaintainExecute(Device);
            Device.AmwInfoKeyGuid = deviceUiState.DeviceOrderState.AmwInfoKeyGuid;
            Device.DeviceOrderState = deviceUiState.DeviceOrderState;
        }
    }
}
