﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using Interfaces;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Database.Contexts;
    using Microsoft.EntityFrameworkCore;

    public class NutzerService : INutzerService
    {
        private readonly IDbContextFactory contextFactory;

        public NutzerService(IDbContextFactory contextFactory)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public Nutzer GetCurrentNutzer(Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Nutzer
                    .Include(x => x.Salutation)
                    .Include(x => x.Title)
                    .Include(x => x.NutzerPersonen)
                    .Include(x => x.NutzerCoOwnership)
                    .Include(x => x.NutzerQuadratmeter)
                    .Include(x => x.NutzerCommunications)
                    .ThenInclude(NutzerCommunications => NutzerCommunications.CommunicationFeature)
                    .Where(x =>x.MoveOutDate == null && x.NutzeinheitGuid == nutzeinheit.Guid).SingleOrDefault();
            }
        }

        public Nutzer GetNutzer(Guid nutzerGuid)
        {
            if (nutzerGuid == null)
            {
                throw new ArgumentNullException(nameof(nutzerGuid));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Nutzer
                    .Include(x => x.Salutation)
                    .Include(x => x.Title)
                    .Include(x => x.NutzerPersonen)
                    .Include(x => x.NutzerCoOwnership)
                    .Include(x => x.NutzerQuadratmeter)
                    .Include(x => x.NutzerCommunications)
                    .ThenInclude(x => x.CommunicationFeature)
                    .Include(x => x.Nutzeinheit)
                    .ThenInclude(x => x.Address)
                    .Include(x => x.Nutzeinheit)
                    .ThenInclude(x => x.OrderStates)
                    .Include(x => x.Nutzeinheit)
                    .ThenInclude(x => x.Devices)
                    .ThenInclude(x => x.OrderStates)
                    .Include(x => x.Nutzeinheit)
                    .ThenInclude(x => x.Signatures)
                    .Where(x => x.Guid == nutzerGuid).SingleOrDefault();
            }
        }

        public Nutzer GetNutzerForDate(Nutzeinheit nutzeinheit, DateTime date)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            if (date == null)
            {
                throw new ArgumentNullException(nameof(date));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                IEnumerable<Nutzer> Nutzers = context.Nutzer
                                            .Include(x => x.Salutation)
                                            .Include(x => x.Title)
                                            .Include(x => x.NutzerPersonen)
                                            .Include(x => x.NutzerCoOwnership)
                                            .Include(x => x.NutzerQuadratmeter)
                                            .Include(x => x.NutzerCommunications)
                                            .ThenInclude(NutzerCommunications => NutzerCommunications.CommunicationFeature)
                                            .Where(x => x.MoveInDate <= date && (x.MoveOutDate >= date || x.MoveOutDate == null) && x.NutzeinheitGuid == nutzeinheit.Guid);

                Nutzer nutzer = Nutzers.Where(x => x.MoveOutDate != null).SingleOrDefault();

                if (nutzer == null)
                {
                    nutzer = Nutzers.FirstOrDefault();
                }
                return nutzer;
            }
        }

        public Nutzer GetLoadedNutzerForDate(Nutzeinheit nutzeinheit, DateTime date)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            if (date == null)
            {
                throw new ArgumentNullException(nameof(date));
            }

            Nutzer nutzer = nutzeinheit.Nutzer.Where(x => x.MoveInDate <= date && (x.MoveOutDate >= date || x.MoveOutDate == null) && x.NutzeinheitGuid == nutzeinheit.Guid)
                                     .Where(x => x.MoveOutDate != null).SingleOrDefault();
            if (nutzer == null)
            {
                nutzer = nutzeinheit.Nutzer.FirstOrDefault();
            }

            if (nutzer == null)
            {
                nutzer = nutzeinheit.Nutzer.FirstOrDefault();
            }
            return nutzer;
        }

        public int GetNutzerCountForNextAppointmentDate(DateTime nextAppointmentDate)
        {
            if (nextAppointmentDate == null)
            {
                throw new ArgumentNullException(nameof(nextAppointmentDate));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Nutzer.Where(x => x.NextAppointmentDate.Value == nextAppointmentDate).Count();
            }
        }

        public string GetNutzerName(Nutzer nutzer)
        {
            if (nutzer == null)
            {
                throw new ArgumentNullException(nameof(nutzer));
            }

            string name1 = string.IsNullOrEmpty(nutzer.Name1) ? string.Empty : nutzer.Name1 + " ";
            string name2 = string.IsNullOrEmpty(nutzer.Name2) ? string.Empty : nutzer.Name2 + " ";
            string name3 = string.IsNullOrEmpty(nutzer.Name3) ? string.Empty : nutzer.Name3 + " ";
            string name4 = string.IsNullOrEmpty(nutzer.Name4) ? string.Empty : nutzer.Name4;

            return name1 + name2 + name3 + name4;
        }

        public string GetNutzerTitleAndSalutation(Nutzer nutzer)
        {
            if (nutzer == null)
            {
                throw new ArgumentNullException(nameof(nutzer));
            }

            string title = nutzer.Title == null ? string.Empty : nutzer.Title.Label + " ";
            string salutation = nutzer.Salutation == null ? string.Empty : nutzer.Salutation.Label + " ";

            return title + salutation;
        }

        public string GetNutzerContactValue(Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            Nutzer nutzer = GetCurrentNutzer(nutzeinheit);
            IList<NutzerCommunication> communicationMethods = nutzer.NutzerCommunications;

            NutzerCommunication contact = communicationMethods.Where(x => x.CommunicationFeature.Kind == CommunicationKind.Mobil).FirstOrDefault();

            if (contact == null)
            {
                contact = communicationMethods.Where(x => x.CommunicationFeature.Kind == CommunicationKind.Telefon).FirstOrDefault();
                if (contact == null)
                {
                    contact = communicationMethods.FirstOrDefault();
                    if(contact == null)
                    {
                        return string.Empty;
                    }
                }
            }
            string nutzerContact = contact.Address;


            return nutzerContact;
        }

        public string GetNutzerAddress(Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            string street1 = string.IsNullOrEmpty(nutzeinheit.Address.Street) ? string.Empty : nutzeinheit.Address.Street + " ";
            string street2 = string.IsNullOrEmpty(nutzeinheit.Address.Street2) ? string.Empty : nutzeinheit.Address.Street2 + " ";
            string streetNumber = string.IsNullOrEmpty(nutzeinheit.Address.StreetNumber) ? string.Empty : nutzeinheit.Address.StreetNumber + " ";
            string zipcode = string.IsNullOrEmpty(nutzeinheit.Address.Zipcode) ? string.Empty : nutzeinheit.Address.Zipcode + " ";
            string city = string.IsNullOrEmpty(nutzeinheit.Address.City) ? string.Empty : nutzeinheit.Address.City + " ";

            return street1 + street2 + streetNumber + zipcode + city;
        }

        public void UpdateNutzer(Nutzer nutzer)
        {
            if (nutzer == null)
            {
                throw new ArgumentNullException(nameof(nutzer));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(nutzer);
                context.Entry(nutzer).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void AddNutzerPersonen(NutzerPersonen nutzerPersonen)
        {
            if (nutzerPersonen == null)
            {
                throw new ArgumentNullException(nameof(nutzerPersonen));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.NutzerPersonen.Add(nutzerPersonen);
                context.SaveChanges();
            }
        }

        public void UpdateNutzerPersonen(NutzerPersonen nutzerPersonen)
        {
            if (nutzerPersonen == null)
            {
                throw new ArgumentNullException(nameof(nutzerPersonen));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(nutzerPersonen);
                context.Entry(nutzerPersonen).State = EntityState.Modified;
                nutzerPersonen.LastModified = DateTime.Now;
                context.SaveChanges();
            }
        }

        public void DeleteNutzerPersonen(NutzerPersonen nutzerPersonen)
        {
            if (nutzerPersonen == null)
            {
                throw new ArgumentNullException(nameof(nutzerPersonen));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                NutzerPersonen dalnutzerPersonen = context.NutzerPersonen.Where(x => x.Guid == nutzerPersonen.Guid).SingleOrDefault();
                context.NutzerPersonen.Remove(dalnutzerPersonen);
                context.SaveChanges();
            }
        }

        public void UpdateNutzerCommunication(NutzerCommunication nutzerCommunication)
        {
            if (nutzerCommunication == null)
            {
                throw new ArgumentNullException(nameof(nutzerCommunication));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(nutzerCommunication);
                context.Entry(nutzerCommunication).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void AddNutzerQuadratmeter(NutzerQuadratmeter nutzerQuadratmeter)
        {
            if (nutzerQuadratmeter == null)
            {
                throw new ArgumentNullException(nameof(nutzerQuadratmeter));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.NutzerQuadratmeter.Add(nutzerQuadratmeter);
                context.SaveChanges();
            }
        }

        public void UpdateNutzerQuadratmeter(NutzerQuadratmeter nutzerQuadratmeter)
        {
            if (nutzerQuadratmeter == null)
            {
                throw new ArgumentNullException(nameof(nutzerQuadratmeter));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(nutzerQuadratmeter);
                context.Entry(nutzerQuadratmeter).State = EntityState.Modified;
                nutzerQuadratmeter.LastModified = DateTime.Now;
                context.SaveChanges();
            }
        }

        public void DeleteNutzerQuadratmeter(NutzerQuadratmeter nutzerQuadratmeter)
        {
            if (nutzerQuadratmeter == null)
            {
                throw new ArgumentNullException(nameof(nutzerQuadratmeter));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                NutzerQuadratmeter dalnutzerQuadratmeter = context.NutzerQuadratmeter.Where(x => x.Guid == nutzerQuadratmeter.Guid).SingleOrDefault();
                context.NutzerQuadratmeter.Remove(dalnutzerQuadratmeter);
                context.SaveChanges();
            }
        }

        public void SaveNutzerCommunication(NutzerCommunication nutzerCommunication)
        {
            if (nutzerCommunication == null)
            {
                throw new ArgumentNullException(nameof(nutzerCommunication));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.NutzerCommunication.Add(nutzerCommunication);
                context.SaveChanges();
            }
        }

        public void AddNutzer(Nutzer nutzer)
        {
            if (nutzer == null)
            {
                throw new ArgumentNullException(nameof(nutzer));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Nutzer.Add(nutzer);
                context.SaveChanges();
            }
        }
    }
}
