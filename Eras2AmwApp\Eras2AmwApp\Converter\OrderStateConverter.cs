﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderStateConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class OrderStateConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if(value == null)
            {
                return null;
            }

            OrderState orderState = (OrderState)value;
            ProcessState processState = orderState.ProcessState;
            Color deviceOrderColor;

            if (processState == ProcessState.InProgress)
            {
                deviceOrderColor = Color.LightGray;
            }
            else if (processState == ProcessState.Updating)
            {
                deviceOrderColor = Color.Orange;
            }
            else if (processState == ProcessState.Completed)
            {
                deviceOrderColor = Color.Green;
            }
            else
            {
                deviceOrderColor = Color.LightGray;
            }

            return deviceOrderColor;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
