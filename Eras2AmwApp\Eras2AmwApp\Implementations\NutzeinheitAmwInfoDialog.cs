﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitAmwInfoDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Implementations
{
    using Syncfusion.XForms.PopupLayout;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using Models;
    using System;
    using System.Collections.Generic;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System.Linq;

    public class NutzeinheitAmwInfoDialog
    {
        #region fields

        private readonly IStammdatenService stammdatenService;
        private readonly ILocalisationService localisationService;
        private readonly INutzeinheitService nutzeinheitService;

        private Command acceptButtonCommand;
        private Command declineButtonCommand;

        private TaskCompletionSource<NutzeinheitOrderState> source;

        private List<AmwInfoKey> infoKeyList;
        private NutzeinheitOrderState nutzeinheitOrderState;
        private SfPopupLayout sfPopupLayout;
        private ListView listView;

        #endregion

        #region ctor

        public NutzeinheitAmwInfoDialog(IStammdatenService stammdatenService, ILocalisationService localisationService, 
            INutzeinheitService nutzeinheitService)
        {
            this.stammdatenService = stammdatenService ?? throw new ArgumentNullException(nameof(stammdatenService));
            this.localisationService = localisationService ?? throw new ArgumentNullException(nameof(localisationService));
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
        }

        #endregion

        #region Commands
        public Command AcceptButtonCommand => acceptButtonCommand ?? (acceptButtonCommand = new Command(AcceptButtonExecuted));

        public Command DeclineButtonCommand => declineButtonCommand ?? (declineButtonCommand = new Command(DeclineButtonExecuted));

        #endregion

        #region public methods

        public Task<NutzeinheitOrderState> ShowNutzeinheitAmwInfoDialog(NutzeinheitVM nutzeinheitVM)
        {
            source = new TaskCompletionSource<NutzeinheitOrderState>();

            sfPopupLayout = CreateDialogWindow(nutzeinheitVM);
            sfPopupLayout.Show();

            return source.Task;
        }

        #endregion

        #region private methods

        private SfPopupLayout CreateDialogWindow(NutzeinheitVM nutzeinheitVM)
        {
            nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitVM.NutzeinheitOrder);

            string dialogWarningMessage = localisationService.Get("NutzeinheitAmwInfoWarning");
            Label dialogLabel = CreateLabel(dialogWarningMessage);

            listView = CreateListView();

            Grid grid = CreateGrid();

            grid.Children.Add(dialogLabel, 0, 0);
            grid.Children.Add(listView, 0, 1);

            DataTemplate headerTempleteView = new DataTemplate(() =>
            {
                return CreateHeader();
            });

            SfPopupLayout popupLayout = new SfPopupLayout()
            {
                PopupView =
                {
                    ShowCloseButton = false,
                    HeaderTemplate = headerTempleteView,
                    ContentTemplate = new DataTemplate(() => grid),
                    MinimumWidthRequest = 300,
                    AppearanceMode = AppearanceMode.TwoButton,
                    ShowFooter = true,
                    AcceptButtonText="OK",
                    DeclineButtonText="Abbrechen",
                    AcceptCommand = AcceptButtonCommand,
                    DeclineCommand = DeclineButtonCommand
                }
            };

            return popupLayout;
        }

        private ListView CreateListView()
        {
            infoKeyList = stammdatenService.GetNutzeinheitInfoKeys();
            AmwInfoKey emptyKey = new AmwInfoKey()
            {
                Key = -1,
                Info = "<Infoschlüssel löschen>"
            };
            infoKeyList.Insert(0, emptyKey);

            List<string> stringList = new List<string>();
            foreach(AmwInfoKey amwInfoKey in infoKeyList)
            {
                stringList.Add(amwInfoKey.Info);
            };

            string selectedKeyInfo = infoKeyList[0].Info;
            AmwInfoKey selectedKey = infoKeyList.FirstOrDefault(x => x.Guid == nutzeinheitOrderState.AmwInfoKeyGuid);
            if(selectedKey != null)
            {
                selectedKeyInfo = selectedKey.Info;
            }

            listView = new ListView()
            {
                ItemsSource = stringList,
                SelectionMode = ListViewSelectionMode.Single,
                SelectedItem = selectedKeyInfo,
                HasUnevenRows = true,
                IsPullToRefreshEnabled = false
            };

            return listView;
        }

        private Label CreateLabel(string dialogWarningMessage)
        {
            var label = new Label
            {
                Margin = 5,
                BackgroundColor = Color.White,
                HorizontalOptions = LayoutOptions.CenterAndExpand,
                VerticalOptions = LayoutOptions.CenterAndExpand,
                FontSize = 20,
                TextColor = Color.Black,
                Text = dialogWarningMessage
            };

            return label;
        }

        private Grid CreateGrid()
        {
            Grid grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(280) });
            return grid;
        }

        private Label CreateHeader()
        {
            Label headerContent = new Label
            {
                Padding = 0,
                Text = "Infoschlüssel Nutzeinheit",
                FontAttributes = FontAttributes.Bold,
                TextColor = Color.White,
                BackgroundColor = Color.FromHex("#538EEC"),
                FontSize = 20,
                HorizontalTextAlignment = TextAlignment.Center,
                VerticalTextAlignment = TextAlignment.Center
            };
            return headerContent;
        }

        private void AcceptButtonExecuted()
        {
            if (listView.SelectedItem is string infoKey)
            {
                AmwInfoKey amwInfoKey = infoKeyList.FirstOrDefault(x => x.Info == infoKey);

                if (amwInfoKey == null)
                {
                    nutzeinheitOrderState.AmwInfoKeyGuid = null;
                }
                else
                {
                    nutzeinheitOrderState.AmwInfoKeyGuid = amwInfoKey.Guid;
                }
            }
            else
            {
                nutzeinheitOrderState.AmwInfoKeyGuid = null;
            }

            source.SetResult(nutzeinheitOrderState);
        }

        private void DeclineButtonExecuted()
        {
            source.SetResult(nutzeinheitOrderState);
        }

        #endregion
    }
}
