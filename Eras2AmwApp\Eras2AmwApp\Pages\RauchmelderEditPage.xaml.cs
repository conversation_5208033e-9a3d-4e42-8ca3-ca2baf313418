﻿namespace Eras2AmwApp.Pages
{
    using Eras2AmwApp.ViewModels;
    using System;
    using Xamarin.Forms;
    using Xamarin.Forms.Xaml;

    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class RauchmelderEditPage : ContentPage
    {
        public static readonly BindableProperty EnableBackButtonOverrideProperty =
           BindableProperty.Create(
               nameof(EnableBackButtonOverride),
               typeof(bool),
               typeof(RauchmelderEditPage),
               true);

        private readonly RauchmelderEditPageViewModel rauchmelderVM;

        public bool EnableBackButtonOverride
        {
            get => (bool)GetValue(EnableBackButtonOverrideProperty);
            set => SetValue(EnableBackButtonOverrideProperty, value);
        }

        public RauchmelderEditPage(RauchmelderEditPageViewModel viewModel)
        {
            InitializeComponent();
            BindingContext = rauchmelderVM = viewModel;
        }

        public Action CustomBackButtonAction => async () => await rauchmelderVM.DeselectListViewItemOnBackButton();

        protected override bool OnBackButtonPressed()
        {
            DeselectListViewItem();

            return true;
        }

        private void DeselectListViewItem()
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                await rauchmelderVM.DeselectListViewItemOnBackButton();
            });
        }
    }
}