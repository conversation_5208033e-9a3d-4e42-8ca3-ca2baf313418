﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IDeviceMaintenanceDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Interfaces
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System.Threading.Tasks;

    public interface IDeviceMaintenanceDialog
    {
        Task<DeviceOrderState> ShowMaintenanceDialog(IDevice device);
    }
}
