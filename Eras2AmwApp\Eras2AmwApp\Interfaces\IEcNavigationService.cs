﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IEcNavigationService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Interfaces
{
    using System.Threading.Tasks;
    using GalaSoft.MvvmLight;

    public interface IEcNavigationService
    {
        bool ShowInitPageOnResume { get; set; }

        ViewModelBase PreviousPageViewModel { get; } 
        
        Task InitializeAsync<TViewModel>() where TViewModel : ViewModelBase; 

        Task NavigateToAsync<TViewModel>() where TViewModel : ViewModelBase;  

        Task NavigateToAsync<TViewModel>(object parameter) where TViewModel : ViewModelBase;  

        Task PopToRootAsync();

        Task RemoveLastFromBackStackAsync();  

        Task RemoveBackStackAsync();

        Task GoBackAsync();
    }
}