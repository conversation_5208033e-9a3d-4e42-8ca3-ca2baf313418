﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="WatermeterExchangeOldDeviceValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using FluentValidation;
    using System;
    using ViewModels;

    public class WatermeterExchangeOldDeviceValidator : AbstractValidator<WatermeterExchangePageViewModel>
    {
        public WatermeterExchangeOldDeviceValidator()
        {
            RuleFor(x => x.OldDeviceNumber)
                .NotEmpty().WithMessage("Die Gerätenummer darf nicht leer sein.");

            RuleFor(x => x.OldDeviceMeasureUnit)
                .Must(ValidateUnit)
                .WithMessage("Die Einheit darf nicht leer sein.");
        }

        private bool ValidateUnit(WatermeterExchangePageViewModel device, UnitKind? unit)
        {
            if (device.OldDeviceClass != DeviceClass.WMZ)
            {
                return true;
            }

            if (unit == UnitKind.KWH || unit == UnitKind.MWH)
            {
                return true;
            }

            return false;
        }
    }
}
