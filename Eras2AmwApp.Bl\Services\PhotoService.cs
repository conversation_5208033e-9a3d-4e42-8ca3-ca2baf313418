﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PhotoService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.IO;
    using Microsoft.EntityFrameworkCore;

    public class PhotoService : IPhotoService
    {
        private readonly IDbContextFactory contextFactory;
        private readonly IResourceService resoureService;

        public PhotoService(IDbContextFactory contextFactory, IResourceService resoureService)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
            this.resoureService = resoureService ?? throw new ArgumentNullException(nameof(resoureService));
        }

        public void SavePhotoToDb(Photo photo)
        {
            if (photo == null)
            {
                throw new ArgumentNullException(nameof(photo));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Photos.Add(photo);
                context.SaveChanges();
            }
        }

        public void UpdatePhoto(Photo photo)
        {
            if (photo == null)
            {
                throw new ArgumentNullException(nameof(photo));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(photo);
                context.Entry(photo).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public string SaveNutzeinheitPhoto(string picturePath, Guid nutzeinheitGuid)
        {
            if (string.IsNullOrEmpty(picturePath))
            {
                throw new ArgumentException("message", nameof(picturePath));
            }

            if (nutzeinheitGuid == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheitGuid));
            }

            DirectoryInfo pictureDirectory = resoureService.GetNutzeinheitPhotoDirectory(nutzeinheitGuid);

            return SavePhotoToDirectory(picturePath, pictureDirectory);
        }

        public string SaveDevicePhoto(string picturePath, Guid deviceGuid)
        {
            if (string.IsNullOrEmpty(picturePath))
            {
                throw new ArgumentException("message", nameof(picturePath));
            }

            if (deviceGuid == null)
            {
                throw new ArgumentNullException(nameof(deviceGuid));
            }

            DirectoryInfo pictureDirectory = resoureService.GetDeviceFotoDirectory(deviceGuid);

            return SavePhotoToDirectory(picturePath, pictureDirectory);
        }

        private string SavePhotoToDirectory(string picturePath, DirectoryInfo pictureDirectory)
        {
            string timeNow = DateTime.Now.ToString("yyyyMMdd_HHmmssffff");
            string newPicturePath = Path.Combine(pictureDirectory.FullName, "Img_" + timeNow + ".jpeg");

            if (!File.Exists(pictureDirectory.FullName))
            {
                Directory.CreateDirectory(pictureDirectory.FullName);
            }

            File.Move(picturePath, newPicturePath);

            return newPicturePath;
        }

        public void DeletePhoto(Photo photo)
        {
            if (photo == null)
            {
                throw new ArgumentNullException(nameof(photo));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Photos.Remove(photo);
                context.SaveChanges();
            }
        }
    }
}
