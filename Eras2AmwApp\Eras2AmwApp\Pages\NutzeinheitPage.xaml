﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:local="clr-namespace:Eras2AmwApp.DataTemplates"
             xmlns:converter="clr-namespace:Eras2AmwApp.Converter"
             xmlns:behaviors="clr-namespace:Eras2AmwApp.Behaviors"
             xmlns:badge ="clr-namespace:Syncfusion.XForms.BadgeView;assembly=Syncfusion.SfBadgeView.XForms"
             xmlns:tabView="clr-namespace:Syncfusion.XForms.TabView;assembly=Syncfusion.SfTabView.XForms"
             mc:Ignorable="d"
             x:Name="NutzeinheitPageName"
             x:Class="Eras2AmwApp.Pages.NutzeinheitPage">

    <NavigationPage.TitleView>

        <Grid>
            
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="50*"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <ImageButton    Grid.Column="1"
                            BackgroundColor="Transparent"
                            HorizontalOptions="End"
                            IsEnabled="False"
                            IsVisible="True"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"> 

                <ImageButton.Triggers>
                    <DataTrigger    TargetType="ImageButton"
                                    Binding="{Binding IsLiveSyncOn}"
                                    Value="True">   
                        <Setter     Property="Source" Value="liveSyncOnIcon.png" />
                    </DataTrigger>

                    <DataTrigger    TargetType="ImageButton"
                                    Binding="{Binding IsLiveSyncOn}"
                                    Value="False">
                        <Setter     Property="Source" Value="liveSyncOffIcon.png" />
                    </DataTrigger>
                </ImageButton.Triggers>
            </ImageButton>

            <ImageButton    Source="newDateIcon.png"
                            Grid.Column="2"
                            BackgroundColor="Transparent"
                            Margin="10,0,0,0"
                            HeightRequest="35"
                            WidthRequest="35"
                            Command="{Binding NewAppointmentCommand}">

            </ImageButton>

            <ImageButton    Source="signatureIcon.png"
                            Grid.Column="3"
                            BackgroundColor="Transparent"
                            Margin="10,0,0,0"
                            HeightRequest="50"
                            WidthRequest="50"
                            Command="{Binding SignatureDialogCommand}">

            </ImageButton>

            <ImageButton    Source="saveIcon.png"
                            Grid.Column="4"
                            BackgroundColor="Transparent"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            HorizontalOptions="End"
                            Command="{Binding ConfirmNutzeinheitCommand}">
            </ImageButton>

        </Grid>

    </NavigationPage.TitleView>

    <ContentPage.Resources>

        <converter:DateTimeConverter x:Key="DateTimeConverter"></converter:DateTimeConverter>
        <converter:WatermeterOrderColorState x:Key="WatermeterOrderColorState"></converter:WatermeterOrderColorState>
        <converter:NutzeinheitOrderStateConverter x:Key="NutzeinheitOrderStateConverter"></converter:NutzeinheitOrderStateConverter>
        <converter:NegateBooleanConverter x:Key="NegateBooleanConverter"></converter:NegateBooleanConverter>
        <converter:DeviceClassTypeConverter x:Key="DeviceClassTypeConverter"></converter:DeviceClassTypeConverter>
        <converter:DeviceOrderKindConverter x:Key="DeviceOrderKindConverter"></converter:DeviceOrderKindConverter>
        <converter:RauchmelderOrderColorState x:Key="RauchmelderOrderColorState"></converter:RauchmelderOrderColorState>
       
        <ResourceDictionary>
            
            <DataTemplate x:Key="RauchmelderTemplate">
                <ViewCell>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="8*"></ColumnDefinition>
                            <ColumnDefinition Width="92*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <Image  Margin="5,2,2,5"
                                Grid.Row="0"
                                Grid.Column="0"
                                WidthRequest="35"
                                HeightRequest="35"
                                Source="{Binding DeviceClass, Converter={StaticResource DeviceClassTypeConverter}}">
                            
                        </Image>

                        <Frame  Margin="5,2,2,2"
                                Grid.Row="0"
                                Grid.Column="1"
                                Padding="0"
                                BorderColor="LightGray"
                                CornerRadius="0"
                                HasShadow="True">

                            <Grid RowSpacing="0">

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="6"></ColumnDefinition>
                                    <ColumnDefinition Width="110"></ColumnDefinition>
                                    <ColumnDefinition Width="30"></ColumnDefinition>
                                    <ColumnDefinition Width="20*"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                </Grid.ColumnDefinitions>

                                <Label  x:Name="StatusColor"
                                        Grid.Row="0"
                                        Grid.RowSpan="2"
                                        Grid.Column="0"
                                        BackgroundColor="{Binding DeviceUiState, Converter={StaticResource RauchmelderOrderColorState}}">

                                </Label>

                                <Label  x:Name="DeviceNumber"
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        Margin="5,3,0,0"
                                        Text="{Binding DeviceNumber}"
                                        FontSize="Small"
                                        TextColor="Black"
                                        FontAttributes="Bold">

                                </Label>

                                <Label  x:Name="DeviceOrderKind"
                                        Grid.Row="0"
                                        Grid.RowSpan="2"
                                        Grid.Column="2"
                                        Margin="0,0,0,3"
                                        Text="{Binding DeviceUiState.DeviceOrderState.OrderKind, Converter={StaticResource DeviceOrderKindConverter}}"
                                        FontSize="Micro"
                                        HorizontalOptions="Center"
                                        VerticalOptions="End"
                                        TextColor="Gray"
                                        BackgroundColor="LightGray">

                                </Label>

                                <Label  x:Name="DeviceType"
                                        Grid.Row="0"
                                        Grid.Column="3"
                                        Margin="5,3,10,0"
                                        Text="{Binding DeviceLabel}"
                                        FontSize="Small"
                                        FontAttributes="Bold">

                                </Label>
                                
                                <Label  x:Name="Room"
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        VerticalOptions="Center"
                                        Margin="5,0,5,3"
                                        Text="{Binding DeviceRoom,StringFormat={markupExtensions:Localisation DeviceRoom}}"
                                        FontSize="Micro">
                                </Label>

                                <Label  x:Name="OngoingNumber"
                                        Grid.Row="1"
                                        Grid.Column="3"
                                        Margin="5,0,5,3"
                                        Text="{Binding DeviceOngoingNumber,StringFormat={markupExtensions:Localisation DeviceOngoingNumber}}"
                                        FontSize="Micro">

                                </Label>

                                <ImageButton    Source="maintainIcon.png"
                                                BackgroundColor="Transparent"
                                                Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="5"
                                                Margin="0,5,10,0"
                                                HeightRequest="35"
                                                WidthRequest="35"
                                                IsVisible="{Binding IsDeviceMaintained, Converter={StaticResource NegateBooleanConverter}}"
                                                VerticalOptions="Start"
                                                HorizontalOptions="End"
                                                Command="{Binding Source={x:Reference NutzeinheitPageName},Path = BindingContext.InfoKeyCommand}"
                                                CommandParameter="{Binding .}">

                                    <ImageButton.Triggers>
                                        <DataTrigger TargetType="ImageButton"
                                                     Binding="{Binding DeviceUiState.DeviceOrderState.OrderKind}"
                                                     Value="Exchange">
                                            <Setter Property="IsVisible"
                                                    Value="True"/>
                                        </DataTrigger>
                                        <DataTrigger TargetType="ImageButton"
                                                     Binding="{Binding DeviceUiState.DeviceOrderState.OrderKind}"
                                                     Value="Assembly">
                                            <Setter Property="IsVisible"
                                                    Value="True"/>
                                        </DataTrigger>
                                    </ImageButton.Triggers>
                                </ImageButton>

                                <CheckBox   Grid.Row="0"
                                            Grid.RowSpan="2"
                                            Grid.Column="4"
                                            WidthRequest="35"
                                            HeightRequest="35"
                                            Margin="0,0,5,0"
                                            Color="Black" 
                                            IsChecked="{Binding IsDeviceMaintained}">

                                    <CheckBox.Triggers>
                                        <DataTrigger TargetType="CheckBox"
                                                     Binding="{Binding DeviceUiState.DeviceOrderState.OrderKind}"
                                                     Value="Exchange">
                                            <Setter Property="IsVisible"
                                                    Value="False"/>
                                        </DataTrigger>
                                        <DataTrigger TargetType="CheckBox"
                                                     Binding="{Binding DeviceUiState.DeviceOrderState.OrderKind}"
                                                     Value="Assembly">
                                            <Setter Property="IsVisible"
                                                    Value="False"/>
                                        </DataTrigger>
                                    </CheckBox.Triggers>
                                </CheckBox>

                            </Grid>

                        </Frame>
                        
                    </Grid>
                </ViewCell>
            </DataTemplate >

            <DataTemplate x:Key="WatermeterTemplate">
                <ViewCell>
                    <Grid>

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="8*"></ColumnDefinition>
                            <ColumnDefinition Width="92*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <Image  Margin="5,2,2,5"
                                Grid.Row="0"
                                Grid.Column="0"
                                WidthRequest="35"
                                HeightRequest="35"
                                Source="{Binding DeviceClass, Converter={StaticResource DeviceClassTypeConverter}}">

                        </Image>

                        <Frame  Margin="5,2,2,2"
                                Grid.Row="0"
                                Grid.Column="1"
                                Padding="0"
                                BorderColor="LightGray"
                                CornerRadius="0"
                                HasShadow="True">

                            <Grid RowSpacing="0">

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="6"></ColumnDefinition>
                                    <ColumnDefinition Width="110"></ColumnDefinition>
                                    <ColumnDefinition Width="30"></ColumnDefinition>
                                    <ColumnDefinition Width="20*"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                </Grid.ColumnDefinitions>

                                <Label  x:Name="StatusColor"
                                        Margin="0"
                                        Grid.Row="0"
                                        Grid.RowSpan="2"
                                        Grid.Column="0"
                                        BackgroundColor="{Binding DeviceUiState, Converter={StaticResource WatermeterOrderColorState}}">

                                </Label>

                                <Label  x:Name="DeviceNumber"
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        Margin="5,3,0,0"
                                        Text="{Binding DeviceNumber}"
                                        FontSize="Small"
                                        TextColor="Black"
                                        FontAttributes="Bold">

                                </Label>

                                <Label  x:Name="DeviceOrderKind"
                                        Grid.Row="0"
                                        Grid.RowSpan="2"
                                        Grid.Column="2"
                                        Margin="0,0,0,3"
                                        Text="{Binding DeviceUiState.DeviceOrderState.OrderKind, Converter={StaticResource DeviceOrderKindConverter}}"
                                        FontSize="Micro"
                                        HorizontalOptions="Center"
                                        VerticalOptions="End"
                                        TextColor="Gray"
                                        BackgroundColor="LightGray">

                                </Label>

                                <Label  x:Name="DeviceType"
                                        Grid.Row="0"
                                        Grid.Column="3"
                                        Margin="5,3,10,0"
                                        Text="{Binding DeviceLabel}"
                                        FontSize="Small"
                                        FontAttributes="Bold">

                                </Label>

                                <Label  x:Name="Room"
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        VerticalOptions="Center"
                                        Margin="5,0,5,3"
                                        Text="{Binding DeviceRoom,StringFormat={markupExtensions:Localisation DeviceRoom}}"
                                        FontSize="Micro">
                                </Label>

                                <Label  x:Name="OngoingNumber"
                                        Grid.Row="1"
                                        Grid.Column="3"
                                        Margin="5,0,5,3"
                                        Text="{Binding DeviceOngoingNumber,StringFormat={markupExtensions:Localisation DeviceOngoingNumber}}"
                                        FontSize="Micro">

                                </Label>

                                <ImageButton    Source="maintainIcon.png"
                                                BackgroundColor="Transparent"
                                                Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="5"
                                                Margin="0,5,10,0"
                                                HeightRequest="35"
                                                WidthRequest="35"
                                                VerticalOptions="Start"
                                                HorizontalOptions="End"
                                                IsVisible="{Binding IsDeviceMaintained, Converter={StaticResource NegateBooleanConverter}}"
                                                Command="{Binding Source={x:Reference NutzeinheitPageName},Path = BindingContext.InfoKeyCommand}"
                                                CommandParameter="{Binding .}">

                                </ImageButton>

                                <CheckBox   Grid.Row="0"
                                            Grid.RowSpan="2"
                                            Grid.Column="4"
                                            WidthRequest="35"
                                            HeightRequest="35"
                                            Margin="0,0,5,0"
                                            Color="Black" 
                                            IsChecked="{Binding IsDeviceMaintained}"
                                            IsVisible="False">

                                    <CheckBox.Triggers>
                                        <DataTrigger TargetType="CheckBox"
                                                     Binding="{Binding DeviceUiState.DeviceOrderState.OrderKind}"
                                                     Value="Inspection">
                                            <Setter Property="IsVisible"
                                                    Value="True"/>
                                        </DataTrigger>
                                    </CheckBox.Triggers>
                                </CheckBox>

                            </Grid>

                        </Frame>

                    </Grid>
                </ViewCell>
            </DataTemplate>

            <local:DeviceDataTemplateSelector x:Key="deviceDataTemplateSelector"
                RauchmelderTemplate="{StaticResource RauchmelderTemplate}"
                WatermeterTemplate="{StaticResource WatermeterTemplate}" />

        </ResourceDictionary>
        
    </ContentPage.Resources>

    <ContentPage.Behaviors>
        <behaviors:EventToCommandBehavior EventName="Appearing"
                                          Command="{Binding AppearingCommand}">
        </behaviors:EventToCommandBehavior>
        <behaviors:EventToCommandBehavior EventName="Disappearing"
                                          Command="{Binding DisappearingCommand}">
        </behaviors:EventToCommandBehavior>

    </ContentPage.Behaviors>

    <ContentPage.Content>
        
        <Grid>

            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="90*"></ColumnDefinition>
                <ColumnDefinition Width="10*"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <Label  x:Name="SignatureErrorText"
                    Margin="5,0,0,0"
                    Grid.Row="0"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    Text="Bitte geben Sie eine Unterschrift ein!"
                    FontAttributes="Bold"
                    FontSize="Medium"
                    HorizontalTextAlignment="Center"
                    TextColor="Red"
                    IsVisible="{Binding IsSignatureWarningShown}"
                    IsEnabled="{Binding IsSignatureWarningShown}">

            </Label>

            <Frame  Margin="10,3,10,3"
                    Grid.Row="1"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    Padding="0"
                    BorderColor="LightGray"
                    CornerRadius="0"
                    HasShadow="True">

                <Frame.GestureRecognizers>
                    <TapGestureRecognizer   Command="{Binding NutzerEditCommand}"
                                            NumberOfTapsRequired="1">
                    </TapGestureRecognizer>
                </Frame.GestureRecognizers>

                <Grid RowSpacing="0">

                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="6"></ColumnDefinition>
                        <ColumnDefinition Width="50*"></ColumnDefinition>
                        <ColumnDefinition Width="25*"></ColumnDefinition>
                        <ColumnDefinition Width="Auto"></ColumnDefinition>
                    </Grid.ColumnDefinitions>

                    <Label  x:Name="StatusColor"
                            Grid.Row="0"
                            Grid.RowSpan="3"
                            Grid.Column="0"
                            BackgroundColor="{Binding NutzeinheitUiState, Converter={StaticResource NutzeinheitOrderStateConverter}}">

                    </Label>

                    <Label  x:Name="Nutzer"
                            Grid.Row="0"
                            Grid.Column="1"
                            Margin="5,5,0,5"
                            Text="{Binding NutzerNameLocation}"
                            TextColor="Black"
                            FontSize="Small"
                            FontAttributes="Bold">

                    </Label>

                    <Label  x:Name="NutzeinheitAddress"
                            Grid.Row="1"
                            Grid.Column="1"
                            Margin="5,0,30,5"
                            Text="{Binding NutzerAddressContact}"
                            FontSize="12">

                    </Label>

                    <Image  x:Name="infoIcon"
                            Grid.Row="0"
                            Grid.Column="2"
                            Margin="0,5,10,0"
                            HeightRequest="30"
                            WidthRequest="30"
                            VerticalOptions="Start"
                            HorizontalOptions="End"
                            Source="infoIcon.png"
                            IsEnabled="{Binding HasNutzeinheitNote}"
                            IsVisible="{Binding HasNutzeinheitNote}">

                        <Image.GestureRecognizers>
                            <TapGestureRecognizer  NumberOfTapsRequired="1"
                                                    Command="{Binding NutzeinheitIconButtonCommand}" />
                        </Image.GestureRecognizers>

                    </Image>

                    <Label  x:Name="NutzeinheitNumber"
                            Grid.Row="0"
                            Grid.Column="3"
                            HorizontalTextAlignment="End"
                            Margin="0,10,10,0"
                            Text="{Binding NutzeinheitNumber}"
                            FontSize="Small"
                            TextColor="Black"
                            FontAttributes="Bold">

                    </Label>
                
                </Grid>

            </Frame>

            <Grid   Grid.Row="2"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    Margin="10,0,25,0"
                    RowSpacing="0">

                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="50*"></ColumnDefinition>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                </Grid.ColumnDefinitions>

                <Label  x:Name="MainLabel"
                        Grid.Row="0"
                        Grid.Column="0"
                        Text="{markupExtensions:Localisation DeviceList}"
                        Margin="5,0,0,0"
                        FontSize="Large" />

                <Label  x:Name="SecondaryLabel"
                        Grid.Row="1"
                        Grid.Column="0"
                        Text="{Binding DeviceCountInfo}"
                        Margin="5,0,5,0"
                        FontSize="Small"
                        TextColor="LightGray">
                </Label>

                <badge:SfBadgeView HorizontalOptions="Center" VerticalOptions="Center" Grid.Row="0" BadgeText="{Binding PhotoCount}" Grid.RowSpan="2" Grid.Column="1">
                    <badge:SfBadgeView.Content>
                        <ImageButton    x:Name="PhotoGallery"
                                        Source="photoGalleryIcon.png"
                                        BackgroundColor="Transparent"
                                        Margin="5,0,0,0"
                                        HeightRequest="45"
                                        WidthRequest="45"
                                        Command="{Binding PhotoPreviewCommand}">
                        </ImageButton>
                    </badge:SfBadgeView.Content>
                    <badge:SfBadgeView.BadgeSettings>
                        <badge:BadgeSetting BadgeType="{Binding BadgeSetting}"
                                            BadgePosition="TopLeft"
                                            Offset="5,10"/>
                    </badge:SfBadgeView.BadgeSettings>
                </badge:SfBadgeView>

                <ImageButton    x:Name="AddDeviceButton"
                                Source="newIcon.png"
                                BackgroundColor="Transparent"
                                Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="2"
                                Margin="5,0,0,0"
                                HeightRequest="40"
                                WidthRequest="40"
                                Command="{Binding AddDeviceCommand}">

                </ImageButton>

                <ImageButton    x:Name="SummaryButton"
                                Source="overviewIcon.png"
                                BackgroundColor="Transparent"
                                Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="3"
                                Margin="5,0,0,0"
                                HeightRequest="50"
                                WidthRequest="50"
                                Command="{Binding SummaryCommand}">

                </ImageButton>

                <CheckBox   Grid.Row="0"
                            Grid.RowSpan="2"
                            Grid.Column="4"
                            Margin="5,0,0,0"
                            Color="Black" 
                            IsVisible="{Binding IsMaintainAllVisible}"
                            IsEnabled="{Binding IsMaintainAllVisible}"
                            IsChecked="{Binding AreAllDevicesMaintained}">
                </CheckBox>

            </Grid>

            <tabView:SfTabView  VisibleHeaderCount="2" 
                                TabHeaderPosition="Bottom" 
                                EnableSwiping="False"
                                Grid.Row="3"
                                Grid.Column="0"
                                Grid.ColumnSpan="2">

                <tabView:SfTabView.Behaviors>
                    <behaviors:EventToCommandBehavior EventName="SelectionChanged" Command="{Binding TabChangedCommand}" />
                </tabView:SfTabView.Behaviors>

                <tabView:SfTabItem Title="Auftrag">

                    <tabView:SfTabItem.Content>

                        <StackLayout    HorizontalOptions="Start"
                                        Margin="10,10,0,0"
                                        Orientation="Vertical">

                            <ListView   x:Name="DeviceList"
                                        Margin="10,0,10,0"
                                        ItemsSource="{Binding ListOfCurrentDevices}"
                                        ItemTemplate="{StaticResource deviceDataTemplateSelector}"
                                        SelectionMode="Single"
                                        SelectedItem="{Binding SelectedDevice, Mode=TwoWay}"
                                        IsPullToRefreshEnabled="False"
                                        HasUnevenRows="True"
                                        CachingStrategy="RecycleElement">

                                <ListView.Triggers>
                                    <DataTrigger    TargetType="ListView"
                                                    Binding="{Binding Source={x:Reference ListOptionSwitch},Path=IsToggled}"
                                                    Value="true">
                                        <Setter Property="ItemsSource"
                                                Value="{Binding ListOfAllDevices}"/>

                                    </DataTrigger>
                                </ListView.Triggers>

                            </ListView>

                            <StackLayout    HorizontalOptions="Start"
                                            Margin="10,10,0,0"
                                            Orientation="Horizontal">

                                <Switch x:Name="ListOptionSwitch"
                                        IsToggled="{Binding ToggleState}"
                                        HorizontalOptions="Start"
                                        OnColor="Black">

                                    <Switch.Behaviors>
                                        <behaviors:EventToCommandBehavior   EventName="Toggled"
                                                                            Command="{Binding ToggleSwitchCommand}">
                                        </behaviors:EventToCommandBehavior>
                                    </Switch.Behaviors>

                                </Switch>

                                <Label  x:Name="switchInfo"
                                        Margin="0"
                                        Text="Nur aktive Geräte anzeigen"
                                        TextColor="LightGray"
                                        FontSize="12"
                                        VerticalTextAlignment="Center">

                                    <Label.Triggers>
                                        <DataTrigger TargetType="Label"
                                                Binding="{Binding Source={x:Reference ListOptionSwitch},Path=IsToggled}"
                                                Value="true">
                                            <Setter Property="Text"
                                                    Value="Alle Geräte dieser Nutzeinheit anzeigen"/>
                                        </DataTrigger>
                                    </Label.Triggers>
                                </Label>

                            </StackLayout>

                        </StackLayout>
                        
                    </tabView:SfTabItem.Content>

                </tabView:SfTabItem>

                <tabView:SfTabItem Title="Weitere Geräte">

                    <tabView:SfTabItem.Content>
                        <StackLayout>
                            <ListView   x:Name="RemainingDeviceList"
                                        Margin="10,20,10,0"
                                        ItemsSource="{Binding ListOfRemainingDevices}"
                                        ItemTemplate="{StaticResource deviceDataTemplateSelector}"
                                        SelectionMode="Single"
                                        SelectedItem="{Binding SelectedDevice, Mode=TwoWay}"
                                        IsPullToRefreshEnabled="False"
                                        HasUnevenRows="True"
                                        CachingStrategy="RecycleElement"
                                        IsVisible="True">
                                <ListView.Triggers>
                                    <DataTrigger    TargetType="ListView"
                                                    Binding="{Binding ListOfRemainingDevices.Count}"
                                                    Value="0">
                                        <Setter Property="IsVisible"
                                            Value="False"/>

                                    </DataTrigger>
                                </ListView.Triggers>
                            </ListView>

                            <Label  x:Name="EmptyListViewMessageLabel"
                                    Margin="5,0,0,0"
                                    Text="Keine weiteren Geräte vorhanden"
                                    FontAttributes="Bold"
                                    FontSize="Large"
                                    HorizontalTextAlignment="Center"
                                    TextColor="LightGray"
                                    IsVisible="False">
                                <Label.Triggers>
                                    <DataTrigger    TargetType="Label"
                                                    Binding="{Binding ListOfRemainingDevices.Count}"
                                                    Value="0">
                                        <Setter Property="IsVisible"
                                                Value="True"/>

                                    </DataTrigger>
                                </Label.Triggers>
                            </Label>
                        </StackLayout>
                        
                    </tabView:SfTabItem.Content>

                </tabView:SfTabItem>

            </tabView:SfTabView>

        </Grid>
        
    </ContentPage.Content>
    
</ContentPage>