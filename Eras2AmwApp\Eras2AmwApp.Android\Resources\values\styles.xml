<?xml version="1.0" encoding="utf-8" ?>
<resources>
  
  <style name="MainTheme" parent="MainTheme.Base">
  </style>
  
  <style name="MainTheme.Base" parent="Theme.AppCompat.Light.DarkActionBar">
    <item name="windowNoTitle">true</item>
    <item name="windowActionBar">false</item>
    <item name="colorPrimary">#2196F3</item>
    <item name="colorPrimaryDark">#1976D2</item>
    <item name="colorAccent">#FF4081</item>
    <item name="windowActionModeOverlay">true</item>
    <item name="android:datePickerDialogTheme">@style/AppCompatDialogStyle</item>
    <item name="android:colorPressedHighlight">@color/ListViewSelected</item>
    <item name="android:colorLongPressedHighlight">@color/ListViewHighlighted</item>
    <item name="android:colorFocusedHighlight">@color/ListViewSelected</item>
    <item name="android:colorActivatedHighlight">@color/ListViewSelected</item>
    <item name="android:activatedBackgroundIndicator">@color/ListViewSelected</item>
  </style>

  <style name="AppCompatDialogStyle" parent="Theme.AppCompat.Light.Dialog">
    <item name="colorAccent">#FF4081</item>
  </style>

  <style name="MyTheme.Base" parent="Theme.AppCompat.Light">
  </style>

  <style name="MyTheme" parent="MyTheme.Base">
  </style>

  <style name="MyTheme.Splash" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:windowBackground">@drawable/splashscreen</item>
    <item name="android:windowNoTitle">true</item>
    <item name="android:windowFullscreen">true</item>
    <item name="android:windowContentOverlay">@null</item>
    <item name="android:windowActionBar">true</item>
  </style>

 </resources>
