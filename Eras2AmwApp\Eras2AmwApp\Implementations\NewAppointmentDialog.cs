﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NewAppointmentDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Implementations
{
    using Eras2AmwApp.Interfaces;
    using Syncfusion.XForms.PopupLayout;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using System;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.CustomControls;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using System.ComponentModel;

    public class NewAppointmentDialog : INewAppointmentDialog
    {
        #region fields

        private readonly IGlobalCacheTrash globalCacheTrash;
        private readonly INutzerService nutzerService;

        private Grid grid;
        private DataTemplate headerTempleteView;
        private SfPopupLayout popupLayout;
        private TimePicker timePicker;
        private NullableDatePicker datePicker;
        private Button clearButton;
        private Label terminCounter;

        private TaskCompletionSource<DateTime?> source;

        private DateTime? newAppointmentDate;
        private DateTime? updatedNewAppointmentDate;

        private Command acceptButtonCommand;
        private Command declineButtonCommand;
        private Command clearTimeDateCommand;

        #endregion

        public NewAppointmentDialog(IGlobalCacheTrash globalCacheTrash, INutzerService nutzerService)
        {
            this.globalCacheTrash = globalCacheTrash ?? throw new ArgumentNullException(nameof(globalCacheTrash));
            this.nutzerService = nutzerService ?? throw new ArgumentNullException(nameof(nutzerService));
        }

        #region Commands

        public Command AcceptButtonCommand => acceptButtonCommand ?? (acceptButtonCommand = new Command(AcceptButtonExecuted));

        public Command DeclineButtonCommand => declineButtonCommand ?? (declineButtonCommand = new Command(DeclineButtonExecuted));

        public Command ClearTimeDateCommand => clearTimeDateCommand ?? (clearTimeDateCommand = new Command(ClearTimeDateExecute));

        #endregion

        #region public methods

        public Task<DateTime?> ShowNewAppointmentDialog(Nutzer nutzer)
        {
            return AssignNewAppointmentDialog(nutzer);
        }

        #endregion

        #region private methods

        private Task<DateTime?> AssignNewAppointmentDialog(Nutzer nutzer)
        {
            source = new TaskCompletionSource<DateTime?>();

            newAppointmentDate = nutzer.NextAppointmentDate;

            timePicker = CreateTimePicker(newAppointmentDate);
            timePicker.PropertyChanged += TimePickerValueChanged;
            
            CreateNullableDatePicker(newAppointmentDate);
            datePicker.PropertyChanged += DatePickerValueChanged;

            clearButton = CreateClearDateButton();
            terminCounter = CreateNewCounterLabel(newAppointmentDate);

            CreateGrid();
            grid.Children.Add(timePicker, 0, 0);
            grid.Children.Add(datePicker, 1, 0);
            grid.Children.Add(clearButton, 0, 1);
            Grid.SetColumnSpan(clearButton, 2);
            grid.Children.Add(terminCounter, 0, 2);
            Grid.SetColumnSpan(terminCounter, 2);
            
            headerTempleteView = new DataTemplate(() =>
            {
                return CreateHeader();
            });

            popupLayout = new SfPopupLayout
            {
                PopupView =
                {
                    ShowCloseButton = false,
                    HeaderTemplate = headerTempleteView,
                    ContentTemplate = new DataTemplate(() => grid),
                    WidthRequest = 200,
                    HeightRequest = 240,
                    AppearanceMode = AppearanceMode.TwoButton,
                    ShowFooter = true,
                    AcceptButtonText="OK",
                    DeclineButtonText="Abbrechen",
                    AcceptCommand = AcceptButtonCommand,
                    DeclineCommand = DeclineButtonCommand
                }
            };

            popupLayout.Show();

            return source.Task;
        }

        private void TimePickerValueChanged(object sender, PropertyChangedEventArgs e)
        {
            if(e.PropertyName == "Time")
            {
                if(datePicker.NullableDate.HasValue)
                {
                    TimeSpan time = timePicker.Time;
                    DateTime? dateTime = datePicker.NullableDate.Value.Date;

                    updatedNewAppointmentDate = dateTime + time;

                    int appointmentCountForDate = 0;

                    if (updatedNewAppointmentDate.HasValue)
                    {
                        appointmentCountForDate = nutzerService.GetNutzerCountForNextAppointmentDate(updatedNewAppointmentDate.Value);
                    }

                    terminCounter.Text = $"Anzahl der Nachtermine: {appointmentCountForDate}.";
                }
            }
        }

        private void DatePickerValueChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == "Date")
            {
                TimeSpan time = timePicker.Time;
                DateTime dateTime = datePicker.Date;
                updatedNewAppointmentDate = dateTime + time;

                int appointmentCountForDate = 0;

                if (updatedNewAppointmentDate.HasValue)
                {
                    appointmentCountForDate = nutzerService.GetNutzerCountForNextAppointmentDate(updatedNewAppointmentDate.Value);
                }

                terminCounter.Text = $"Anzahl der Nachtermine: {appointmentCountForDate}.";
            }
        }


        private TimePicker CreateTimePicker(DateTime? newAppointmentDate)
        {
            TimePicker timePick = new TimePicker()
            {
                Margin = 1,
            };

            if (newAppointmentDate.HasValue)
            {
                timePick.Time = newAppointmentDate.Value.TimeOfDay;
            }
            else if(globalCacheTrash.LastSecondAppointment.HasValue)
            {
                timePick.Time = globalCacheTrash.LastSecondAppointment.Value.TimeOfDay;
            }
 
            return timePick;
        }

        private void CreateNullableDatePicker(DateTime? newAppointmentDate)
        {
            if(newAppointmentDate.HasValue)
            {
                datePicker = new NullableDatePicker
                {
                    NullableDate = newAppointmentDate
                };
            }
            else if(globalCacheTrash.LastSecondAppointment.HasValue)
            {
                datePicker = new NullableDatePicker
                {
                    NullableDate = globalCacheTrash.LastSecondAppointment
                };
            }
            else 
            {
                datePicker = new NullableDatePicker()
                {
                    NullableDate = newAppointmentDate,
                };
            }
        }

        private Button CreateClearDateButton()
        {
            Button button = new Button()
            {
                Margin = 3,
                Text = "Löschen",
                Command = ClearTimeDateCommand
            };

            return button;
        }

        private Label CreateNewCounterLabel(DateTime? newAppointmentDate)
        {
            int appointmentCountForDate = 0;

            if (newAppointmentDate.HasValue)
            {
                appointmentCountForDate = nutzerService.GetNutzerCountForNextAppointmentDate(newAppointmentDate.Value);
            }
            else if(globalCacheTrash.LastSecondAppointment.HasValue)
            {
                appointmentCountForDate = nutzerService.GetNutzerCountForNextAppointmentDate(globalCacheTrash.LastSecondAppointment.Value);
            }

            Label label = new Label
            {
                Text = $"Anzahl der Nachtermine: {appointmentCountForDate}.",
                FontAttributes = FontAttributes.Bold,
                FontSize = 12,
                Margin = 3
            };

            return label;
        }

        private void CreateGrid()
        {
            grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(60) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });
        }

        private Label CreateHeader()
        {
            Color headercolor;

            if(newAppointmentDate.HasValue)
            {
                headercolor = Color.Green;
            }
            else
            {
                headercolor = Color.LightGray;
            }

            Label headerContent = new Label
            {
                Padding = 0,
                Text = "Neuer Termin",
                FontAttributes = FontAttributes.Bold,
                TextColor = Color.White,
                BackgroundColor = headercolor,
                FontSize = 20,
                HorizontalTextAlignment = TextAlignment.Center,
                VerticalTextAlignment = TextAlignment.Center
            };
            return headerContent;
        }

        private void AcceptButtonExecuted()
        {
            if((timePicker.Time != null) && (datePicker.NullableDate != null))
            {
                TimeSpan time = timePicker.Time;
                DateTime? dateTime = datePicker.NullableDate.Value.Date;
                newAppointmentDate = dateTime + time;
                globalCacheTrash.LastSecondAppointment = newAppointmentDate;

                source.SetResult(newAppointmentDate);
            }
            else
            {
                newAppointmentDate = null;
                source.SetResult(newAppointmentDate);
            }
        }

        private void DeclineButtonExecuted()
        {
            timePicker.PropertyChanged -= TimePickerValueChanged;
            source.SetResult(newAppointmentDate);
        }

        private void ClearTimeDateExecute()
        {
            if(timePicker.Time != TimeSpan.Zero)
            {
                timePicker.Time = new TimeSpan();
            }

            if(datePicker.NullableDate != null)
            {
                datePicker.CleanDate();
                datePicker.Format = "tt.mm.jjjj";
            }

            newAppointmentDate = null;
            source.SetResult(newAppointmentDate);
            timePicker.PropertyChanged -= TimePickerValueChanged;
            popupLayout.Dismiss();
        }

        #endregion

    }
}
