﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderEditPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using Endiancode.Utilities.Extensions;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.Models;
    using Eras2AmwApp.Services;
    using Eras2AmwApp.Validators;
    using FluentValidation.Results;
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq;
    using System.Threading.Tasks;
    using Xamarin.Forms;

    public class OrderEditPageViewModel : EcViewModelBase
    {
        #region fields

        private readonly IPersonService personService;
        private readonly IAbrechnungseinheitService abrechnungseinheitService;
        private readonly IOrderService orderService;

        private Command appearingCommand;
        private Command updateAuftrageNoticeCommand;
        private Command addSegmentCommand;
        private Command saveSegmentCommand;
        private Command updateAbrechnungseinheitNoticeCommand;

        private string _address;
        private string _orderNumber;
        private string _orderNote;
        private string _abrechnungseinheitNote;
        private int _selectedSegment;
        private int _segmentNumber;

        private Title _personTitle;
        private Salutation _personSalutation;
        private string _personFirstName;
        private string _personLastName;
        private CommunicationDetailsVM _personCommunicationType;
        private string _personCommunicationValue;
        private string _personNote;
        private string _personPosition;

        private string _personSalutationErrorText;
        private bool _personSalutationHasError;
        private string _personFirstNameErrorText;
        private bool _personFirstNameHasError;
        private string _personLastNameErrorText;
        private bool _personLastNameHasError;
        private string _personPositionErrorText;
        private bool _personPositionHasError;
        private string _personCommunicationValueErrorText;
        private bool _personCommunicationValueHasError;
        private string _personCommunicationTypeErrorText;
        private bool _personCommunicationTypeHasError;

        private Abrechnungseinheit selectedAbrechnungseinheit;
        private Dictionary<CommunicationKind, string> selectedPersonCommunicationDetails;

        private bool isNewPerson;
        private Appointment selectedAppointment;
        #endregion

        #region ctor

        public OrderEditPageViewModel(IServiceLocator serviceLocator, IEcNavigationService navigationService, IPersonService personService,
            IAbrechnungseinheitService abrechnungseinheitService, IOrderService orderService)
           : base(serviceLocator, navigationService)
        {
            this.personService = personService ?? throw new ArgumentNullException(nameof(personService));
            this.abrechnungseinheitService = abrechnungseinheitService ?? throw new ArgumentNullException(nameof(abrechnungseinheitService));
            this.orderService = orderService ?? throw new ArgumentNullException(nameof(orderService));

            AbrechnungsPeopleNames = new ObservableCollection<string>();
            AbrechnungsPeopleList = new ObservableCollection<AbrechnungsPersonVM>();

            InitilizePickersItemLists();
        }

        #endregion

        #region commands

        public Command UpdateAuftrageNoticeCommand => updateAuftrageNoticeCommand ?? (updateAuftrageNoticeCommand = new Command(UpdateAuftrageNoticeExecute));

        public Command UpdateAbrechnungseinheitNoticeCommand => updateAbrechnungseinheitNoticeCommand ?? (updateAbrechnungseinheitNoticeCommand = new Command(UpdateAbrechnungseinheitNoticeExecute));

        public Command AppearingCommand => appearingCommand ?? (appearingCommand = new Command(AppearingExecute));

        public Command AddSegmentCommand => addSegmentCommand ?? (addSegmentCommand = new Command(AddSegmentExecute));

        public Command SaveSegmentCommand => saveSegmentCommand ?? (saveSegmentCommand = new Command<int>(SaveSegmentExecute));

        #endregion

        #region properties

        public string Address
        {
            get { return _address; }
            set { Set(ref _address, value); }
        }

        public string OrderNumber
        {
            get { return _orderNumber; }
            set { Set(ref _orderNumber, value); }
        }

        public string OrderNote
        {
            get { return _orderNote; }
            set { Set(ref _orderNote, value); }
        }

        public string AbrechnungseinheitNote
        {
            get { return _abrechnungseinheitNote; }
            set { Set(ref _abrechnungseinheitNote, value); }
        }

        public ObservableCollection<AbrechnungsPersonVM> AbrechnungsPeopleList { get; set; }

        public ObservableCollection<string> AbrechnungsPeopleNames { get; set; }

        public int SelectedSegment
        {
            get { return _selectedSegment; }
            set
            {
                if (value != _selectedSegment)
                {
                    Set(ref _selectedSegment, value);
                    GetSelectedContactPerson(value);
                }
            }
        }

        public int SegmentNumber
        {
            get { return _segmentNumber; }
            set { Set(ref _segmentNumber, value); }
        }

        public Title PersonTitle
        {
            get { return _personTitle; }
            set { Set(ref _personTitle, value); }
        }

        public Salutation PersonSalutation
        {
            get { return _personSalutation; }
            set { Set(ref _personSalutation, value); }
        }

        public string PersonSalutationErrorText
        {
            get { return _personSalutationErrorText; }
            set { Set(ref _personSalutationErrorText, value); }
        }

        public bool PersonSalutationHasError
        {
            get { return _personSalutationHasError; }
            set { Set(ref _personSalutationHasError, value); }
        }

        public string PersonFirstName
        {
            get { return _personFirstName; }
            set { Set(ref _personFirstName, value);}
        }

        public string PersonFirstNameErrorText
        {
            get { return _personFirstNameErrorText; }
            set { Set(ref _personFirstNameErrorText, value); }
        }

        public bool PersonFirstNameHasError
        {
            get { return _personFirstNameHasError; }
            set { Set(ref _personFirstNameHasError, value); }
        }

        public string PersonLastName
        {
            get { return _personLastName; }
            set { Set(ref _personLastName, value); }
        }

        public string PersonLastNameErrorText
        {
            get { return _personLastNameErrorText; }
            set { Set(ref _personLastNameErrorText, value); }
        }

        public bool PersonLastNameHasError
        {
            get { return _personLastNameHasError; }
            set { Set(ref _personLastNameHasError, value); }
        }

        public string PersonPosition
        {
            get { return _personPosition; }
            set { Set(ref _personPosition, value); }
        }

        public string PersonPositionErrorText
        {
            get { return _personPositionErrorText; }
            set { Set(ref _personPositionErrorText, value); }
        }

        public bool PersonPositionHasError
        {
            get { return _personPositionHasError; }
            set { Set(ref _personPositionHasError, value); }
        }

        public CommunicationDetailsVM PersonCommunicationType
        {
            get { return _personCommunicationType; }
            set
            {
                if (value != _personCommunicationType)
                {
                    Set(ref _personCommunicationType, value);
                    if(selectedPersonCommunicationDetails != null)
                    {
                        UpdatePersonCommunicationValue(value);
                    } 
                }
            }
        }

        public string PersonCommunicationTypeErrorText
        {
            get { return _personCommunicationTypeErrorText; }
            set { Set(ref _personCommunicationTypeErrorText, value); }
        }

        public bool PersonCommunicationTypeHasError
        {
            get { return _personCommunicationTypeHasError; }
            set { Set(ref _personCommunicationTypeHasError, value); }
        }

        public string PersonCommunicationValue
        {
            get { return _personCommunicationValue; }
            set { Set(ref _personCommunicationValue, value); }
        }

        public string PersonCommunicationValueErrorText
        {
            get { return _personCommunicationValueErrorText; }
            set { Set(ref _personCommunicationValueErrorText, value); }
        }

        public bool PersonCommunicationValueHasError
        {
            get { return _personCommunicationValueHasError; }
            set { Set(ref _personCommunicationValueHasError, value); }
        }

        public string PersonNote
        {
            get { return _personNote; }
            set { Set(ref _personNote, value); }
        }

        public List<Salutation> PersonSalutationList { get; set; }

        public List<Title> PersonTitleList { get; set; }

        public List<CommunicationDetailsVM> PersonCommunicationTypeList { get; set; }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is Appointment appointment)
            {
                selectedAppointment = appointment;
                InitilizeProperties(appointment);
            }
            return base.SetupAsync(navigationData);
        }

        #endregion

        #region private methods

        private void InitilizePickersItemLists()
        {
            try
            {
                List<Salutation> listOfSalutations = personService.GetSalutations();
                PersonSalutationList = new List<Salutation>();
                foreach (Salutation salutation in listOfSalutations)
                {
                    PersonSalutationList.Add(salutation);
                }

                List<Title> listOfTitles = personService.GetTitles();
                PersonTitleList = new List<Title>();
                foreach (Title title in listOfTitles)
                {
                    PersonTitleList.Add(title);
                }

                PersonCommunicationTypeList = new List<CommunicationDetailsVM>();
                List<CommunicationKind> listOfCommunicationTypes = Enum.GetValues(typeof(CommunicationKind)).Cast<CommunicationKind>().ToList();

                foreach (CommunicationKind type in listOfCommunicationTypes)
                {
                    CommunicationDetailsVM communicationDetail = new CommunicationDetailsVM()
                    {
                        CommunicationType = type.GetDescription()
                    };
                    PersonCommunicationTypeList.Add(communicationDetail);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while initializing pickers values in the AbrechnungsEinheitPageVm!");
                throw;
            }

        }

        private void InitilizeProperties(Appointment appointment)
        {
            try
            {
                Order order = appointment.Order;
                Address aeAddress = order.Abrechnungseinheit.Address;

                OrderNumber = order.Number;
                OrderNote = order.Note;
                Address = $"{aeAddress.Street} {aeAddress.StreetNumber}, {aeAddress.Zipcode} {aeAddress.City}";

                Abrechnungseinheit abrechnungseinheit = order.Abrechnungseinheit;
                selectedAbrechnungseinheit = abrechnungseinheit;

                AbrechnungseinheitNote = abrechnungseinheit.Note;

                List<PersonAbrechnungseinheit> contactPeople = abrechnungseinheit.PersonAbrechnungseinheiten.ToList();

                if(!contactPeople.Any())
                {
                    AbrechnungsPeopleNames.Add("Neu AE Kontakt Person");
                    SegmentNumber = 1;
                    isNewPerson = true;
                    return;
                }

                foreach (PersonAbrechnungseinheit contactPerson in contactPeople)
                {
                    Person personDetails = contactPerson.Person;

                    AbrechnungsPersonVM abrechnungsPerson = AssignPersonDetails(personDetails, contactPerson);

                    List<PersonCommunication> personContactInfo = personDetails.PersonCommunications.ToList();

                    AssignPersonContactInfo(personContactInfo, abrechnungsPerson);

                    AbrechnungsPeopleNames.Add(abrechnungsPerson.PersonLastname);
                    AbrechnungsPeopleList.Add(abrechnungsPerson);
                }

                if(AbrechnungsPeopleNames.Count > 4)
                {
                    SegmentNumber = 4;
                }
                else
                {
                    SegmentNumber = AbrechnungsPeopleNames.Count;
                }
                
                GetSelectedContactPerson(SelectedSegment);
            }
            catch(Exception exception)
            {
                logger.Error(exception, "Exception occoured while initializing properties of OrderEditPage!");
                throw;
            }
        }

        private AbrechnungsPersonVM AssignPersonDetails(Person personDetails, PersonAbrechnungseinheit contactPerson)
        {
            AbrechnungsPersonVM abrechnungsPerson = new AbrechnungsPersonVM
            {
                PersonAEGuid = contactPerson.Guid,
                PersonGuid = personDetails.Guid,
                PersonFirstname = personDetails.Firstname1,
                PersonLastname = personDetails.Lastname1,
                PersonSalutation = personDetails.Salutation,
                PersonTitle = personDetails.Title,
                PersonNote = contactPerson.Note,
                PersonPosition = contactPerson.BusinessPosition
            };

            return abrechnungsPerson;
        }

        private void AssignPersonContactInfo(List<PersonCommunication> personContactInfo, AbrechnungsPersonVM abrechnungsPerson)
        {
            abrechnungsPerson.PersonCommunicationDetails = personService.GetPersonContacts(personContactInfo);
            abrechnungsPerson.PersonCommunicationValue = personService.GetPersonMainCommunicationValue(personContactInfo);

            if (personContactInfo.Count != 0)
            {
                CommunicationKind communicationTyp = abrechnungsPerson.PersonCommunicationDetails.FirstOrDefault(x => x.Value == abrechnungsPerson.PersonCommunicationValue).Key;
                abrechnungsPerson.PersonCommunicationTyp = communicationTyp.GetDescription();
            }
        }

        private void GetSelectedContactPerson(int selectedPersonNr)
        {
            try
            {
                if((selectedPersonNr == AbrechnungsPeopleList.Count) && selectedPersonNr != 0) //selected is outside of range
                {
                    return;
                }

                isNewPerson = false;
                AbrechnungsPersonVM abrechnungsPerson = AbrechnungsPeopleList[selectedPersonNr];
                InitSelectedPerson(abrechnungsPerson);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to navigate to Selected Contact Person!");
                throw;
            }
        }

        private void InitSelectedPerson(AbrechnungsPersonVM abrechnungsPerson)
        {
            ClearPersonFields();
            InitOldNutzerTitleAndSalutation(abrechnungsPerson);
            InitOldNutzerName(abrechnungsPerson);
            InitOldNutzerCommunication(abrechnungsPerson);
            InitOldNutzerRemainingInfo(abrechnungsPerson);
        }

        private void ClearPersonFields()
        {
            PersonSalutation = new Salutation();
            PersonTitle = null;
            PersonFirstName = string.Empty;
            PersonLastName = string.Empty;
            PersonCommunicationType = new CommunicationDetailsVM();
            PersonCommunicationValue = string.Empty;
            PersonPosition = string.Empty;
            PersonNote = string.Empty;
            selectedPersonCommunicationDetails = new Dictionary<CommunicationKind, string>();
        }

        private void InitOldNutzerTitleAndSalutation(AbrechnungsPersonVM abrechnungsPerson)
        {
            Salutation salutation = abrechnungsPerson.PersonSalutation;
            PersonSalutation = PersonSalutationList.First(x => x.Label == salutation.Label);

            Title title = abrechnungsPerson.PersonTitle;

            if (title != null)
            {
                PersonTitle = PersonTitleList.First(x => x.Label == title.Label);
            }
        }

        private void InitOldNutzerName(AbrechnungsPersonVM abrechnungsPerson)
        {
            PersonFirstName = abrechnungsPerson.PersonFirstname;
            PersonLastName = abrechnungsPerson.PersonLastname;
        }

        private void InitOldNutzerCommunication(AbrechnungsPersonVM abrechnungsPerson)
        {
            selectedPersonCommunicationDetails = abrechnungsPerson.PersonCommunicationDetails;
            Person person = personService.GetPerson(abrechnungsPerson.PersonGuid);

            List<PersonCommunication> personCommunications = person.PersonCommunications.ToList();
            PersonCommunicationValue = personService.GetPersonMainCommunicationValue(personCommunications);
            if (personCommunications.Count != 0)
            {
                CommunicationDetailsVM communication = new CommunicationDetailsVM
                {
                    CommunicationType = abrechnungsPerson.PersonCommunicationDetails.FirstOrDefault(x => x.Value == abrechnungsPerson.PersonCommunicationValue).Key.GetDescription()
                };
                PersonCommunicationType = PersonCommunicationTypeList.First(x => x.CommunicationType == communication.CommunicationType);
            }
        }

        private void InitOldNutzerRemainingInfo(AbrechnungsPersonVM abrechnungsPerson)
        {
            PersonNote = abrechnungsPerson.PersonNote;
            PersonPosition = abrechnungsPerson.PersonPosition;
        }

        private void UpdateAuftrageNoticeExecute()
        {
            try
            {
                Order order = selectedAppointment.Order;
                UpdateOrder(order);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to update Order and AE note!");
                throw;
            }
        }

        private void UpdateAbrechnungseinheitNoticeExecute()
        {
            try
            {
                Order order = selectedAppointment.Order;

                Abrechnungseinheit abrechnungseinheit = order.Abrechnungseinheit;
                UpdateAbrechnungseinheit(abrechnungseinheit);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to update Order and AE note!");
                throw;
            }
        }

        private void UpdateOrder(Order order)
        {
            order.Note = OrderNote;
            orderService.UpdateOrder(order);
        }

        private void UpdateAbrechnungseinheit(Abrechnungseinheit abrechnungseinheit)
        {
            abrechnungseinheit.Note = AbrechnungseinheitNote;
            abrechnungseinheitService.UpdateAbrechnungseinheit(abrechnungseinheit);
        }

        private void AddSegmentExecute()
        {
            try
            {
                ClearPersonFields();
                isNewPerson = true;
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to add new item to segmented control list!");
                throw;
            }
        }

        private void UpdatePersonCommunicationValue(CommunicationDetailsVM selectedCommunicationType)
        {
            try
            {
                if (selectedCommunicationType == null)
                {
                    return;
                }

                Enum.TryParse(selectedCommunicationType.CommunicationType, out CommunicationKind commKind);
                selectedPersonCommunicationDetails.TryGetValue(commKind, out string selectedCommunicationValue);

                if (selectedCommunicationValue == PersonCommunicationValue)
                {
                    return;
                }

                PersonCommunicationValue = selectedCommunicationValue;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to retreive value for selected communication type!");
                throw;
            }
        }

        private void SaveSegmentExecute(int segmentNumber)
        {
            try
            {
                if (!Validate())
                {
                    return;
                }

                if (isNewPerson)
                {
                    Person person = CreateNewPerson();
                    List<PersonCommunication> personCommunications = CreateNewPersonCommunication();
                    PersonAbrechnungseinheit personAbrechnungseinheit = CreateNewPersonenAbrechnungseinheit(person, personCommunications);
                    abrechnungseinheitService.AddPersonAbrechnungseinheit(personAbrechnungseinheit);
                }
                else
                {
                    AbrechnungsPersonVM abrechnungsPerson = AbrechnungsPeopleList[segmentNumber];
                    PersonAbrechnungseinheit personAbrechnungseinheit = UpdatePersonAeDetails(abrechnungsPerson);
                    UpdatePersonDetails(personAbrechnungseinheit);
                    UpdatePersonAeContactDetails(personAbrechnungseinheit);
                }
                RefreshUI();
                GetSelectedContactPerson(SelectedSegment);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to save new person contact!");
                throw;
            }
        }

        private Person CreateNewPerson()
        {
            Person person = new Person
            {
                Firstname1 = PersonFirstName,
                Lastname1 = PersonLastName,
                SalutationId = PersonSalutation.Id
            };

            if (PersonTitle != null)
            {
                person.TitleId = PersonTitle.Id;
            }


            return person;
        }

        private List<PersonCommunication> CreateNewPersonCommunication()
        {
            List<PersonCommunication> personCommunications = new List<PersonCommunication>();

            Enum.TryParse(PersonCommunicationType.CommunicationType, out CommunicationKind kind);
            CommunicationFeature communicationFeature = personService.GetCommunicationFeature(kind);

            PersonCommunication newPersonCommunication = new PersonCommunication()
            {
                Address = PersonCommunicationValue,
                CommunicationFeatureGuid = communicationFeature.Guid
            };
            personCommunications.Add(newPersonCommunication);

            return personCommunications;
        }

        private PersonAbrechnungseinheit CreateNewPersonenAbrechnungseinheit(Person person, List<PersonCommunication> personCommunications)
        {
            person.PersonCommunications = personCommunications;

            PersonAbrechnungseinheit personAbrechnungseinheit = new PersonAbrechnungseinheit
            {
                Note = PersonNote,
                BusinessPosition = PersonPosition,
                IsCreatedByApp = true,
                Person = person,
                AbrechnungseinheitGuid = selectedAbrechnungseinheit.Guid
            };

            return personAbrechnungseinheit;
        }

        private PersonAbrechnungseinheit UpdatePersonAeDetails(AbrechnungsPersonVM abrechnungsPerson)
        {
            PersonAbrechnungseinheit personAbrechnungseinheit = abrechnungseinheitService.GetPersonAbrechnungseinheit(abrechnungsPerson.PersonAEGuid);
            personAbrechnungseinheit.Note = PersonNote;
            personAbrechnungseinheit.BusinessPosition = PersonPosition;

            PersonAbrechnungseinheit unmodifiedPersonAe = abrechnungseinheitService.GetPersonAbrechnungseinheit(abrechnungsPerson.PersonAEGuid);

            if (WasPersonAeModified(unmodifiedPersonAe, personAbrechnungseinheit))
            {
                abrechnungseinheitService.UpdatePersonAbrechnungseinheit(personAbrechnungseinheit);
            }

            return personAbrechnungseinheit;
        }

        private bool WasPersonAeModified(PersonAbrechnungseinheit unmodifiedPersonAe, PersonAbrechnungseinheit personAbrechnungseinheit)
        {
            if ((unmodifiedPersonAe.Note != personAbrechnungseinheit.Note) ||
                unmodifiedPersonAe.BusinessPosition != personAbrechnungseinheit.BusinessPosition)
            {
                return true;
            }

            return false;
        }

        private void UpdatePersonDetails(PersonAbrechnungseinheit personAbrechnungseinheit)
        {
            Person person = personAbrechnungseinheit.Person;
            person.Lastname1 = PersonLastName;
            person.Firstname1 = PersonFirstName;

            if (PersonTitle != null)
            {
                person.TitleId = PersonTitle.Id;
                person.Title = personService.GetTitle(PersonTitle.Id);
            }

            person.SalutationId = PersonSalutation.Id;
            person.Salutation = personService.GetSalutation(PersonSalutation.Id);

            if (WasPersonModified(person, personAbrechnungseinheit))
            {
                personService.UpdatePerson(person);
            }
        }

        private bool WasPersonModified(Person person, PersonAbrechnungseinheit personAbrechnungseinheit)
        {
            Person unmodifiedPerson = personService.GetPerson(personAbrechnungseinheit.Person.Guid);

            if ((unmodifiedPerson.Firstname1 != person.Firstname1) ||
                (unmodifiedPerson.Lastname1 != person.Lastname1) ||
                (unmodifiedPerson.SalutationId != person.SalutationId) ||
                (unmodifiedPerson.TitleId != person.TitleId))
            {
                return true;
            }
            return false;
        }

        private void UpdatePersonAeContactDetails(PersonAbrechnungseinheit personAbrechnungseinheit)
        {
            Person person = personService.GetPerson(personAbrechnungseinheit.Person.Guid);
            List<PersonCommunication> personCommunications = person.PersonCommunications.ToList();

            Enum.TryParse(PersonCommunicationType.CommunicationType, out CommunicationKind kind);
            PersonCommunication personCommunication = personCommunications.FirstOrDefault(x => x.CommunicationFeature.Kind == kind);

            if (personCommunication == null)
            {
                personCommunication = GetNewPersonCommunication(person, kind);
                personService.AddPersonCommunication(personCommunication);
            }
            else
            {
                PersonCommunication modifiedPersonCommunication = GetPersonCommunication(person, kind);
                if (WasCommunicationModified(personCommunication, modifiedPersonCommunication, kind))
                {
                    personService.UpdatePersonCommunication(personCommunication);
                }
            }
        }

        private bool WasCommunicationModified(PersonCommunication personCommunication, PersonCommunication modifiedPersonCommunication, CommunicationKind kind)
        {
            if ((personCommunication.Address != modifiedPersonCommunication.Address) ||
                personCommunication.CommunicationFeature.Kind != kind)
            {
                CommunicationFeature communicationFeature = personService.GetCommunicationFeature(kind);
                personCommunication.Address = PersonCommunicationValue;
                personCommunication.CommunicationFeature.Guid = communicationFeature.Guid;
                return true;
            }

            return false;
        }

        private PersonCommunication GetPersonCommunication(Person person, CommunicationKind kind)
        {
            CommunicationFeature communicationFeature = personService.GetCommunicationFeature(kind);
            PersonCommunication newPersonCommunication = new PersonCommunication()
            {
                Address = PersonCommunicationValue,
                PersonGuid = person.Guid,
                CommunicationFeatureGuid = communicationFeature.Guid
            };
            return newPersonCommunication;
        }

        private PersonCommunication GetNewPersonCommunication(Person person, CommunicationKind kind)
        {
            CommunicationFeature communicationFeature = personService.GetCommunicationFeature(kind);

            PersonCommunication newPersonCommunication = new PersonCommunication()
            {
                PersonGuid = person.Guid,
                Address = PersonCommunicationValue,
                CommunicationFeatureGuid = communicationFeature.Guid
            };

            return newPersonCommunication;
        }

        private bool Validate()
        {
            var validator = new OrderEditPageViewModelValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }

        private void RefreshUI()
        {
            Abrechnungseinheit updatedAbrechnungseinheit = abrechnungseinheitService.GetAbrechnungseinheit(selectedAbrechnungseinheit.Guid);
            List<PersonAbrechnungseinheit> contactPeople = updatedAbrechnungseinheit.PersonAbrechnungseinheiten.ToList();
            AbrechnungsPeopleList.Clear();
            AbrechnungsPeopleNames.Clear();
            foreach (PersonAbrechnungseinheit person in contactPeople)
            {
                Person personDetails = person.Person;

                AbrechnungsPersonVM abrechnungsPerson = AssignPersonDetails(personDetails, person);

                List<PersonCommunication> personContactInfo = personDetails.PersonCommunications.ToList();

                AssignPersonContactInfo(personContactInfo, abrechnungsPerson);

                AbrechnungsPeopleNames.Add(abrechnungsPerson.PersonLastname);
                AbrechnungsPeopleList.Add(abrechnungsPerson);
            }

            if (AbrechnungsPeopleNames.Count > 4)
            {
                SegmentNumber = 4;
            }
            else
            {
                SegmentNumber = AbrechnungsPeopleNames.Count;
            }
        }

        private void AppearingExecute()
        {
            try
            {
                if(AbrechnungsPeopleList.Count > 0)
                {
                    RefreshUI();
                }
            }
            catch (Exception exception)
            {
                logger.Error(exception, "Exception occured while executing Appearing OrderEditPage!");
                throw;
            }
        }

        #endregion
    }
}



