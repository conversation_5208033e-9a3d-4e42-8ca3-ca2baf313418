﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="LocalisationExtension.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MarkupExtensions
{
    using System;

    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Common.Ioc;

    using Xamarin.Forms;
    using Xamarin.Forms.Xaml;

    [ContentProperty("Text")]
    public class LocalisationExtension : IMarkupExtension
    {
        private readonly ILocalisationService localisationService;

        public LocalisationExtension()
        {
            localisationService = NinjectKernel.Get<ILocalisationService>();
        }

        public string Text { get; set; }

        public object ProvideValue(IServiceProvider serviceProvider) => localisationService.Get(Text);
    }
}