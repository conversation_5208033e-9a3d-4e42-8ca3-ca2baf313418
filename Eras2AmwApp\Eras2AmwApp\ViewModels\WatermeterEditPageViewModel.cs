﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="WatermeterEditPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using Device = Domain.Eras2Amw.Models.Device;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Interfaces;
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using Eras2AmwApp.Models;
    using Eras2AmwApp.Services;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.BusinessLogic.Factory;
    using Plugin.Media;
    using Plugin.Media.Abstractions;
    using System.IO;
    using FluentValidation.Results;
    using System.Globalization;
    using Eras2AmwApp.Validators;
    using Xamarin.Essentials;

    public class WatermeterEditPageViewModel : EcViewModelBase, ISupportParentViewModel
    {
        #region fields

        private readonly IDeviceService deviceService;
        private readonly ISignatureService signatureService;
        private readonly IStammdatenService stammdatenService;
        private readonly INutzeinheitService nutzeinheitService;
        private readonly IEcDialogService dialogService;
        private readonly IPhotoService photoService;
        private readonly IScannerService scannerService;
        private readonly IOrderService orderService;
        private readonly IDeviceOrderKindChangeDialog deviceOrderKindChangeDialog;

        private Command saveDeviceCommand;
        private Command photoCommand;
        private Command readBarcodCommand;
        private Command addArticleCommand;
        private Command removeArticleCommand;
        private Command deleteDeviceCommand;
        private Command setInstallationDateCommand;
        private Command setDeinstallationDateCommand;
        private Command setReadingDateCommand;
        private Command changeDeviceOrderKindCommand;

        private EcViewModelBase _parentViewModel;
        private bool _isDeviceVisible;
        private string _deviceNumber;
        private string _deviceKind;
        private DeviceClass _deviceClass;
        private Room _selectedRoom;
        private string _ongoingNumber;
        private string _articleDescription;
        private string _articleNumber;
        private string _deviceCalibrationDate;
        private UnitKind? _deviceMeasureUnit;
        private DateTime _deviceInstallationDate;
        private DateTime? _deviceDeinstallationDate;
        private double? _deviceReading;
        private string _previousDeviceReading;
        private AmwInfoKey _selectedAmwKey;
        private string _deviceNote;
        private bool _isMaintained;
        private DeviceOrderKind _deviceOrderKind;
        private bool _showMaintainOrErrorWarning;
        private DateTime? _deviceReadingDate;

        private List<string> _unitKindList;
        private List<AmwInfoKey> _amwInfoKeyList;
        private AdditionalArticle _selectedAdditionalArticle;
        private AdditionalArticleVM _selectedToRemoveAdditionalAricle;
        private int _selectedAdditionalArticleListCount;
        private string _additionalArticlesNumber;

        private string _deviceNumberErrorText;
        private string _selectedRoomErrorText;

        private bool _deviceNumberHasError;
        private bool _selectedRoomHasError;

        private IWatermeter selectedDevice;
        private Device currentDevice;
        private DeviceOrderState currentDeviceOrderState;
        private NutzeinheitOrder nutzeinheitOrder;
        private bool wasDeleteDeclined;

        #endregion

        #region ctor

        public WatermeterEditPageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            IDeviceService deviceService,
            ISignatureService signatureService,
            IStammdatenService stammdatenService,
            INutzeinheitService nutzeinheitService,
            IEcDialogService dialogService,
            IPhotoService photoService,
            IScannerService scannerService,
            IOrderService orderService,
            IDeviceOrderKindChangeDialog deviceOrderKindChangeDialog)
         : base(serviceLocator, navigationService)
        {
            this.deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
            this.signatureService = signatureService ?? throw new ArgumentNullException(nameof(signatureService));
            this.stammdatenService = stammdatenService ?? throw new ArgumentNullException(nameof(stammdatenService));
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.photoService = photoService ?? throw new ArgumentNullException(nameof(photoService));
            this.scannerService = scannerService ?? throw new ArgumentNullException(nameof(scannerService));
            this.orderService = orderService ?? throw new ArgumentNullException(nameof(orderService));
            this.deviceOrderKindChangeDialog = deviceOrderKindChangeDialog ?? throw new ArgumentNullException(nameof(deviceOrderKindChangeDialog));

            PickerYearList = new ObservableCollection<string>
            {
                " "
            };
            SelectedAdditionalArticleList = new ObservableCollection<AdditionalArticleVM>();
            SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;
            AmwInfoKeyList = new List<AmwInfoKey>();
            UnitKindList = new List<string>();
            wasDeleteDeclined = false;
            ShowMaintainOrErrorWarning = false;
            InitPickerItemList();
        }

        #endregion

        #region commands

        public Command SaveDeviceCommand => saveDeviceCommand ?? (saveDeviceCommand = new Command(SaveDeviceExecute));

        public Command PhotoCommand => photoCommand ?? (photoCommand = new Command(PhotoExecute));

        public Command ReadBarcodCommand => readBarcodCommand ?? (readBarcodCommand = new Command(ReadBarcodExecute));

        public Command AddArticleCommand => addArticleCommand ?? (addArticleCommand = new Command<AdditionalArticle>(AddArticleExecute));

        public Command RemoveArticleCommand => removeArticleCommand ?? (removeArticleCommand = new Command<AdditionalArticleVM>(RemoveArticleExecute));

        public Command DeleteDeviceCommand => deleteDeviceCommand ?? (deleteDeviceCommand = new Command(DeleteDeviceExecute, () => IsDeviceVisible));

        public Command SetInstallationDateCommand => setInstallationDateCommand ?? (setInstallationDateCommand = new Command(SetInstallationDateExecute));

        public Command SetDeinstallationDateCommand => setDeinstallationDateCommand ?? (setDeinstallationDateCommand = new Command(SetDeinstallationDateExecute));

        public Command SetReadingDateCommand => setReadingDateCommand ?? (setReadingDateCommand = new Command(SetReadingDateExecute));

        public Command ChangeDeviceOrderKindCommand => changeDeviceOrderKindCommand ?? (changeDeviceOrderKindCommand = new Command(ChangeDeviceOrderKindExecute));

        #endregion

        #region properties

        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { Set(ref _parentViewModel, value); }
        }

        public string DeviceNumber
        {
            get { return _deviceNumber; }
            set { Set(ref _deviceNumber, value); }
        }

        public bool IsDeviceVisible
        {
            get { return _isDeviceVisible; }
            set { Set(ref _isDeviceVisible, value); }
        }

        public string DeviceNumberErrorText
        {
            get { return _deviceNumberErrorText; }
            set { Set(ref _deviceNumberErrorText, value); }
        }

        public bool DeviceNumberHasError
        {
            get { return _deviceNumberHasError; }
            set { Set(ref _deviceNumberHasError, value); }
        }

        public string DeviceKind
        {
            get { return _deviceKind; }
            set { Set(ref _deviceKind, value); }
        }

        public DeviceClass DeviceClass
        {
            get { return _deviceClass; }
            set 
            { 
                Set(ref _deviceClass, value);
                GetDeviceMeasureUnit();
                GetAmwInfoKeyList();
                InitCalibrationListValues();
            }
        }

        public Room SelectedRoom
        {
            get { return _selectedRoom; }
            set { Set(ref _selectedRoom, value); }
        }

        public string SelectedRoomErrorText
        {
            get { return _selectedRoomErrorText; }
            set { Set(ref _selectedRoomErrorText, value); }
        }

        public bool SelectedRoomHasError
        {
            get { return _selectedRoomHasError; }
            set { Set(ref _selectedRoomHasError, value); }
        }

        public string OngoingNumber
        {
            get { return _ongoingNumber; }
            set { Set(ref _ongoingNumber, value); }
        }

        public string ArticleDescription
        {
            get { return _articleDescription; }
            set { Set(ref _articleDescription, value); }
        }

        public string ArticleNumber
        {
            get { return _articleNumber; }
            set { Set(ref _articleNumber, value); }
        }

        public UnitKind? DeviceMeasureUnit
        {
            get { return _deviceMeasureUnit; }
            set { Set(ref _deviceMeasureUnit, value); }
        }

        public string DeviceCalibrationDate
        {
            get { return _deviceCalibrationDate; }
            set
            {
                if (value == " ")
                {
                    value = null;
                }
                Set(ref _deviceCalibrationDate, value);
            }
        }

        public DateTime DeviceInstallationDate
        {
            get { return _deviceInstallationDate; }
            set { Set(ref _deviceInstallationDate, value); }
        }

        public DateTime? DeviceReadingDate
        {
            get { return _deviceReadingDate; }
            set { Set(ref _deviceReadingDate, value); }
        }

        public DateTime? DeviceDeinstallationDate
        {
            get { return _deviceDeinstallationDate; }
            set { Set(ref _deviceDeinstallationDate, value); }
        }

        public bool ShowMaintainOrErrorWarning
        {
            get { return _showMaintainOrErrorWarning; }
            set { Set(ref _showMaintainOrErrorWarning, value); }
        }

        public double? DeviceReading
        {
            get { return _deviceReading; }
            set 
            {
                if (value != null)
                {
                    PreviousDeviceReading = "Ablesung";
                }
                Set(ref _deviceReading, value); 
            }
        }

        public string PreviousDeviceReading
        {
            get { return _previousDeviceReading; }
            set { Set(ref _previousDeviceReading, value); }
        }

        public string DeviceNote
        {
            get { return _deviceNote; }
            set
            {
                if (value != null)
                {
                    value = RemoveUnsupportedCharacters(value);
                }
                Set(ref _deviceNote, value);
            }
        }

        public bool IsMaintained
        {
            get { return _isMaintained; }
            set { Set(ref _isMaintained, value); }
        }

        public string AdditionalArticlesNumber
        {
            get { return _additionalArticlesNumber; }
            set { Set(ref _additionalArticlesNumber, value); }
        }

        public DeviceOrderKind DeviceOrderKind
        {
            get { return _deviceOrderKind; }
            set { Set(ref _deviceOrderKind, value); }
        }

        public List<Room> RoomList { get; set; }

        public List<AmwInfoKey> AmwInfoKeyList
        {
            get { return _amwInfoKeyList; }
            set { Set(ref _amwInfoKeyList, value); }
        }

        public List<AdditionalArticle> AdditionalArticleList { get; set; }

        public ObservableCollection<AdditionalArticleVM> SelectedAdditionalArticleList { get; set; }

        public int SelectedAdditionalArticleListCount
        {
            get { return _selectedAdditionalArticleListCount; }
            set { Set(ref _selectedAdditionalArticleListCount, value); }
        }

        public AmwInfoKey SelectedAmwKey
        {
            get { return _selectedAmwKey; }
            set 
            {
                if (value != _selectedAmwKey)
                {
                    if (value.Info == "<Infoschlüssel löschen>")
                    {
                        value = null;
                    }
                    Set(ref _selectedAmwKey, value);
                } 
            }
        }

        public List<string> UnitKindList
        {
            get { return _unitKindList; }
            set { Set(ref _unitKindList, value); }
        }

        public AdditionalArticle SelectedAdditionalArticle
        {
            get { return _selectedAdditionalArticle; }
            set { Set(ref _selectedAdditionalArticle, value); }
        }

        public AdditionalArticleVM SelectedToRemoveAdditionalAricle
        {
            get { return _selectedToRemoveAdditionalAricle; }
            set { Set(ref _selectedToRemoveAdditionalAricle, value); }
        }

        public ObservableCollection<string> PickerYearList { get; set; }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is NutzeinheitPageViewModel parentVM)
            {
                ParentViewModel = parentVM;
                selectedDevice = (IWatermeter)parentVM.SelectedDevice.Device;
                DeviceOrderKind = selectedDevice.DeviceOrderState.OrderKind;
                InitilizeProperties();
            }

            return base.SetupAsync(navigationData);
        }

        public NutzeinheitPageViewModel GetParentProperty()
        {
            return ParentViewModel as NutzeinheitPageViewModel;
        }

        public async Task DeselectListViewItemOnBackButton()
        {
            var previousVm = GetParentProperty();
            previousVm.SelectedDevice = null;
            await navigationService.GoBackAsync();
        }

        #endregion

        #region private methods

        private void InitCalibrationListValues()
        {
            int y = 10;

            if (DeviceClass != DeviceClass.RM)
            {
                y = 6;
            }

            for (int x = 0; x <= y; x++)
            {
                string year = DateTime.Now.AddYears(x).ToString("yyyy");
                PickerYearList.Add(year);
            }
        }

        private void GetDeviceMeasureUnit()
        {
            if (DeviceClass == DeviceClass.WMZ)
            {
                UnitKindList = UnitKindList.Where(x => x == "KWH" || x == "MWH").ToList();
            }
        }

        private void GetAmwInfoKeyList()
        {
            List<AmwInfoKey> infoKeyList = new List<AmwInfoKey>();

            if (DeviceClass == DeviceClass.KWZ || DeviceClass == DeviceClass.WWZ || DeviceClass == DeviceClass.WMZ)
            {
                infoKeyList = stammdatenService.GetWasserzählerInfoKey();
            }
            else if (DeviceClass == DeviceClass.HKV)
            {
                infoKeyList = stammdatenService.GetHkvInfoKey();
            }

            AmwInfoKey emptyKey = new AmwInfoKey()
            {
                Key = -1,
                Info = "<Infoschlüssel löschen>"
            };
            infoKeyList.Insert(0, emptyKey);

            AmwInfoKeyList = infoKeyList;
        }

        private void InitPickerItemList()
        {
            try
            {               
                List<AdditionalArticle> articleList = stammdatenService.GetAdditionalArticles();
                AdditionalArticleList = new List<AdditionalArticle>();
                foreach (AdditionalArticle additionalArticle in articleList)
                {
                    AdditionalArticleList.Add(additionalArticle);
                }

                List<Room> roomList = stammdatenService.GetRooms();
                RoomList = new List<Room>();
                foreach (Room room in roomList)
                {
                    RoomList.Add(room);
                }

                UnitKindList = Enum.GetNames(typeof(UnitKind)).ToList();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while initilizing AmwInfoKey list and AdditionalArticleList!");
                throw;
            }
        }

        private void InitilizeProperties()
        {
            try
            {
                currentDevice = deviceService.GetDevice(selectedDevice.DeviceGuid);
                currentDeviceOrderState = deviceService.GetDeviceOrderState(selectedDevice.DeviceGuid, selectedDevice.DeviceOrderState.OrderGuid);

                DeviceNumber = selectedDevice.DeviceNumber;
                DeviceClass = currentDevice.DeviceCatalog.DeviceKind.Class;

                if (DeviceClass == DeviceClass.WMZ)
                {
                    UnitKindList = UnitKindList.Where(x => x == "KWH" || x == "MWH").ToList();
                }

                DeviceKind = currentDevice.DeviceCatalog.DeviceKind.LabelShort;
                ArticleDescription = currentDevice.DeviceCatalog.ArticleDescription;
                ArticleNumber = currentDevice.DeviceCatalog.ArticleNumber;
                SelectedRoom = RoomList.FirstOrDefault(x => x.Guid == selectedDevice.DeviceRoom.Guid);
                OngoingNumber = currentDevice.OngoingNumber;
                DeviceMeasureUnit = currentDevice.Unit;

                if (selectedDevice.DeviceCalibrationDate.HasValue)
                {
                    DeviceCalibrationDate = PickerYearList.SingleOrDefault(x => x == selectedDevice.DeviceCalibrationDate.Value.Year.ToString());
                }
                DeviceInstallationDate = selectedDevice.DeviceInstallationDate;
                DeviceDeinstallationDate = selectedDevice.DeviceDeinstallationDate;
                
                IsMaintained = selectedDevice.IsDeviceMaintained;

                List<DeviceConsumption> currentDeviceConsumptions = currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).ToList();
                DeviceReading = currentDeviceConsumptions.First().Reading;

                if (currentDeviceConsumptions.Count > 1)
                {
                    PreviousDeviceReading = "Abl: " +  currentDeviceConsumptions[1].ReadingDate.Value.ToString("dd.MM.yyyy") + " " + currentDeviceConsumptions[1].Reading.ToString();
                }

                if (DeviceReading != null)
                {
                    DeviceReadingDate = currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First().ReadingDate;
                    PreviousDeviceReading = "Ablesung";
                }
                else
                {
                    DeviceReadingDate = DateTime.Now;
                }

                SelectedAmwKey = AmwInfoKeyList.FirstOrDefault(x => x.Guid == currentDeviceOrderState.AmwInfoKeyGuid);
                DeviceNote = selectedDevice.DeviceNote;

                if (currentDevice.DeviceAdditionalArticles.Any())
                {
                    IList<DeviceAdditionalArticle> additionalArticles = currentDevice.DeviceAdditionalArticles;
                    foreach (DeviceAdditionalArticle additionalArticle in additionalArticles)
                    {
                        AdditionalArticleVM additionalArticleVM = new AdditionalArticleVM()
                        {
                            AdditionalArticle = additionalArticle.AdditionalArticle,
                            Amount = additionalArticle.Quantity,
                            IsCreatedByApp = additionalArticle.IsCreatedByApp
                        };
                        SelectedAdditionalArticleList.Add(additionalArticleVM);
                        AdditionalArticle existingArticle = AdditionalArticleList.Where(x => x.Guid == additionalArticle.AdditionalArticle.Guid).Single();
                        AdditionalArticleList.Remove(existingArticle);
                    }
                    SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;
                }

                IsDeviceVisible = selectedDevice.IsCreatedByApp;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to initialize properties in WaterEditPageVM!");
                throw;
            }
        }

        private async void SaveDeviceExecute()
        {
            try
            {
                if (!Validate())
                {
                    return;
                }

                if(DeviceOrderKind == DeviceOrderKind.Inspection)
                {
                    if ((IsMaintained == false && SelectedAmwKey == null) || (IsMaintained && SelectedAmwKey != null))
                    {
                        ShowMaintainOrErrorWarning = true;
                        return;
                    }
                    else
                    {
                        ShowMaintainOrErrorWarning = false;
                    }
                }
                
                currentDevice = await UpdateDeviceInformation();

                if (wasDeleteDeclined)
                {
                    return;
                }

                await DeleteDeviceArticlesRemovedFromList();

                DeviceOrderState deviceOrderState = deviceService.GetDeviceOrderState(selectedDevice.DeviceGuid, selectedDevice.DeviceOrderState.OrderGuid);
                Device updatedDevice = deviceService.GetDevice(selectedDevice.DeviceGuid);

                await AddUpdateDeviceAdditionalArticle(updatedDevice);

                UpdatePreviousVmState(updatedDevice, deviceOrderState);

                await navigationService.GoBackAsync();
            }
            catch (Exception e)
            {
                logger.Error(e, "Excepton occured while attempting to save Device information edit!");
                throw;
            }
        }

        private async Task<Device> UpdateDeviceInformation()
        {
            if (!await CheckIfCanBeSave())
            {
                return currentDevice;
            }

            GetUIDeviceState();
            UpdateDeviceOrderState();

            if (currentDeviceOrderState.AmwInfoKeyGuid != null)
            {
                currentDevice.IsMaintained = false;
            }

            deviceService.UpdateDeviceConsumption(currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First());
            deviceService.UpdateDevice(currentDevice);
            
            return currentDevice;
        }

        private void GetUIDeviceState()
        {
            currentDevice.Number = DeviceNumber;
            currentDevice.InstallationDate = DeviceInstallationDate;
            currentDevice.DeinstallationDate = DeviceDeinstallationDate;
            currentDevice.Note = DeviceNote;
            currentDevice.Room = SelectedRoom;
            currentDevice.Unit = DeviceMeasureUnit;
            currentDevice.IsMaintained = IsMaintained;

            if (!string.IsNullOrEmpty(DeviceCalibrationDate))
            {
                var cultureInfo = new CultureInfo("de-DE");
                string stringCalibrationDate = "1 Jan " + DeviceCalibrationDate;
                DateTime dateTime = DateTime.Parse(stringCalibrationDate, cultureInfo);
                currentDevice.CalibrationDate = dateTime;
            }
            else
            {
                currentDevice.CalibrationDate = null;
            }

            if (currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First().Reading != DeviceReading)
            {
                currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First().Reading = DeviceReading;
            }

            if (currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First().ReadingDate != DeviceReadingDate)
            {
                currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First().ReadingDate = DeviceReadingDate;
            }
        }

        private void UpdateDeviceOrderState()
        {
            if (currentDevice.IsMaintained) //if device is maintained now >> remove info key
            {
                SelectedAmwKey = null;
                currentDeviceOrderState.AmwInfoKeyGuid = null;
                currentDeviceOrderState.AmwInfoKey = null;
            }

            if (SelectedAmwKey != null && SelectedAmwKey.Key != -1)
            {
                currentDeviceOrderState.AmwInfoKeyGuid = SelectedAmwKey.Guid;
                currentDeviceOrderState.AmwInfoKey = SelectedAmwKey;
            }
            else
            {
                currentDeviceOrderState.AmwInfoKeyGuid = null;
                currentDeviceOrderState.AmwInfoKey = null;
            }

            if (currentDeviceOrderState.ProcessState != ProcessState.Creating)
            {
                currentDeviceOrderState.ProcessState = ProcessState.Updating;
                currentDeviceOrderState.CompletedDate = DateTime.Now;
            }

            deviceService.UpdateDeviceOrderState(currentDeviceOrderState);
        }

        private Signature GetSignatureForThisOrder()
        {
            return signatureService.GetSignatureForOrder(nutzeinheitOrder.NutzeinheitGuid, nutzeinheitOrder.OrderGuid);
        }

        private async Task<DialogResponse> DisplayDeleteSignatureWarning(Signature signature)
        {
            string warning = localisationService.Get("DeleteSignatureWarning");
            DialogResponse result = await dialogService.AcceptDeclineAsync(warning, "JA", "NEIN");

            if (result == DialogResponse.Decline)
            {
                wasDeleteDeclined = true;
                return result;
            }

            DeleteSignature(signature);

            return result;
        }

        private void DeleteSignature(Signature signature)
        {
            if (signature.Path != null)
            {
                signatureService.RemoveSignatureFromDevice(signature);
            }
            signatureService.DeleteSignature(signature);
            SetNutzeinheitStateInProgress();
        }

        private void SetNutzeinheitStateInProgress()
        {
            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitOrder);

            if (nutzeinheitOrderState.ProcessState == ProcessState.InProgress)
            {
                return;
            }

            nutzeinheitOrderState.ProcessState = ProcessState.InProgress;
            nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);
        }

        private async Task DeleteDeviceArticlesRemovedFromList()
        {
            List<DeviceAdditionalArticle> existingArticlesList = currentDevice.DeviceAdditionalArticles.ToList();
            List<DeviceAdditionalArticle> removedArticles = new List<DeviceAdditionalArticle>();

            foreach (DeviceAdditionalArticle deviceAdditionalArticle in existingArticlesList)
            {
                bool isPresent = SelectedAdditionalArticleList.Any(x => x.AdditionalArticle.Guid == deviceAdditionalArticle.AdditionalArticle.Guid);
                if (!isPresent)
                {
                    removedArticles.Add(deviceAdditionalArticle);
                }
            }

            if (removedArticles.Any())
            {
                NutzeinheitPageViewModel pageViewModel = GetParentProperty();
                bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

                if (!result)
                {
                    return;
                }


                Signature signature = GetSignatureForThisOrder();

                if (signature != null)
                {
                    DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                    if (signatureDeleteWarningResult == DialogResponse.Decline)
                    {
                        return;
                    }
                }

                deviceService.RemoveDeviceAdditionalArticle(removedArticles);
            }
        }

        private async Task AddUpdateDeviceAdditionalArticle(Device updatedDevice)
        {
            
            IList<DeviceAdditionalArticle> updatedArticlesList = updatedDevice.DeviceAdditionalArticles;

            if(SelectedAdditionalArticleList != null)
            {
                foreach (AdditionalArticleVM additionalArticle in SelectedAdditionalArticleList)
                {
                    DeviceAdditionalArticle existingArticle = updatedArticlesList.SingleOrDefault(x => x.AdditionalArticleGuid == additionalArticle.AdditionalArticle.Guid);
                    Signature signature = GetSignatureForThisOrder();

                    if (existingArticle != null)
                    {
                        NutzeinheitPageViewModel pageViewModel = GetParentProperty();
                        bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

                        if (!result)
                        {
                            return;
                        }

                        if (signature != null)
                        {
                            DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                            if (signatureDeleteWarningResult == DialogResponse.Decline)
                            {
                                return;
                            }
                        }

                        UpdateExistingDeviceArticle(additionalArticle, existingArticle);
                    }
                    else
                    {
                        NutzeinheitPageViewModel pageViewModel = GetParentProperty();
                        bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

                        if (!result)
                        {
                            return;
                        }

                        if (signature != null)
                        {
                            DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                            if (signatureDeleteWarningResult == DialogResponse.Decline)
                            {
                                return;
                            }
                        }

                        AddNewDeviceArticles(updatedDevice, additionalArticle);
                    }
                }
            }
        }

        private void UpdatePreviousVmState(Device updatedDevice, DeviceOrderState deviceOrderState)
        {
            //Get the new state of business logic device
            IDevice iDevice = DeviceFactory.Create(updatedDevice, deviceOrderState);

            //Assign new values to selected item from previousViewModel
            NutzeinheitPageViewModel previousViewModel = GetParentProperty();
            previousViewModel.SelectedDevice = null;

            DeviceVM currentDeviceVM = previousViewModel.ListOfCurrentDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);
            DeviceVM currentAllDeviceVM = previousViewModel.ListOfAllDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);
            DeviceVM currentRemainingDeviceVM = previousViewModel.ListOfRemainingDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);

            Order currentOrder = orderService.GetOrder(deviceOrderState.OrderGuid);

            if (currentOrder.Number.Contains("#x#"))
            {
                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(updatedDevice.NutzeinheitGuid);
                NutzeinheitOrderState nutzeinheitOrderState = nutzeinheit.OrderStates.Single(x => x.OrderGuid == deviceOrderState.OrderGuid);
                nutzeinheitOrderState.ProcessState = ProcessState.Updating;

                List<DeviceOrderState> backupOrderDeviceOrderStates = nutzeinheit.Devices.SelectMany(x => x.OrderStates).Where(y => y.OrderGuid == deviceOrderState.OrderGuid).ToList();

                if (!backupOrderDeviceOrderStates.Any(x => x.ProcessState == ProcessState.InProgress))
                {
                    nutzeinheitOrderState.CompletedDate = DateTime.Now;
                }
                nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);

                Order order = orderService.GetOrder(deviceOrderState.OrderGuid);
                OrderState orderState = order.OrderState;
                orderState.ProcessState = ProcessState.Updating;
                orderService.UpdateOrderState(orderState);
            }

            //update UI Model in previousVM
            if (currentDeviceVM != null && iDevice.DeviceDeinstallationDate != null) //remove from currentList & update in allList  
            {
                previousViewModel.ListOfCurrentDevices.Remove(currentDeviceVM);
                UpdatePreviousVM(currentAllDeviceVM, iDevice);
            }
            else if (currentDeviceVM == null && iDevice.DeviceDeinstallationDate == null && currentRemainingDeviceVM == null) //add to currentList & update in allList
            {
                UpdatePreviousVM(currentAllDeviceVM, iDevice);
                previousViewModel.ListOfCurrentDevices.Add(currentAllDeviceVM);
            }
            else if (currentRemainingDeviceVM != null) //update remainingList
            {
                UpdatePreviousVM(currentRemainingDeviceVM, iDevice);
            }
            else
            {
                UpdatePreviousVM(currentAllDeviceVM, iDevice);
                UpdatePreviousVM(currentDeviceVM, iDevice);
            }
        }

        private void UpdateExistingDeviceArticle(AdditionalArticleVM additionalArticle, DeviceAdditionalArticle existingArticle)
        {
            if (existingArticle.Quantity == additionalArticle.Amount)
            {
                return;
            }
            existingArticle.Quantity = additionalArticle.Amount;
            deviceService.UpdateDeviceAdditonalArticle(existingArticle);
        }

        private void AddNewDeviceArticles(Device device, AdditionalArticleVM additionalArticle)
        {
            DeviceAdditionalArticle newArticle = new DeviceAdditionalArticle()
            {
                DeviceGuid = device.Guid,
                AdditionalArticleGuid = additionalArticle.AdditionalArticle.Guid,
                Quantity = additionalArticle.Amount,
                IsCreatedByApp = true
            };
            deviceService.SaveDeviceAdditonalArticle(newArticle);
        }

        private void UpdatePreviousVM(DeviceVM deviceVM, IDevice iDevice)
        {
            deviceVM.DeviceNumber = iDevice.DeviceNumber;
            deviceVM.DeviceLabel = iDevice.DeviceLabel;
            deviceVM.DeviceRoom = iDevice.DeviceRoom.Label;
            deviceVM.DeviceOngoingNumber = iDevice.DeviceOngoingNumber;
            deviceVM.Device = iDevice;

            if((IsMaintained && IsMaintained != deviceVM.IsDeviceMaintained) ||
                (SelectedAmwKey != null && IsMaintained != deviceVM.IsDeviceMaintained))
            {
                deviceVM.IsDeviceListEditedOrCreated = true;
            }

            if (iDevice.DeviceOrderState.AmwInfoKeyGuid != null)
            {
                deviceVM.IsDeviceMaintained = false;
            }

            deviceVM.IsDeviceMaintained = IsMaintained;
            deviceVM.DeviceUiState = new DeviceUiState
            {
                IsMaintained = deviceVM.IsDeviceMaintained,
                DeviceOrderState = iDevice.DeviceOrderState
            };
        }

        private async void PhotoExecute()
        {
            try
            {
                if (!await CheckIfCanBeSave())
                {
                    return;
                }

                await CrossMedia.Current.Initialize();

                navigationService.ShowInitPageOnResume = false;

                PermissionStatus statusCamera = await Permissions.CheckStatusAsync<Permissions.Camera>();
                PermissionStatus status = await Permissions.CheckStatusAsync<Permissions.StorageWrite>();

                if (statusCamera == PermissionStatus.Denied || status == PermissionStatus.Denied)
                {
                    statusCamera = await Permissions.RequestAsync<Permissions.Camera>();
                    status = await Permissions.RequestAsync<Permissions.StorageWrite>();
                }

                if (statusCamera == PermissionStatus.Denied || status == PermissionStatus.Denied) return;

                MediaFile picture = await CrossMedia.Current.TakePhotoAsync(new StoreCameraMediaOptions()
                {
                    CompressionQuality = 20,
                    PhotoSize = PhotoSize.Medium,
                    SaveToAlbum = false,
                    AllowCropping = false
                });

                if (picture != null)
                {
                    string photoPath = photoService.SaveDevicePhoto(picture.Path, selectedDevice.DeviceGuid);
                    Photo photo = new Photo()
                    {
                        Name = Path.GetFileName(photoPath),
                        Path = photoPath,
                        RecordedDate = DateTime.UtcNow,
                        CreatedByApp = true,
                        DeviceGuid = selectedDevice.DeviceGuid
                    };

                    photoService.SavePhotoToDb(photo);

                    //Update listOfPhotos
                    UpdatePreviousModelPhotos(photo);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to capture a picture in OrderPageVm!");
                throw;
            }
            finally
            {
                navigationService.ShowInitPageOnResume = true;
            }
        }

        private async Task<bool> CheckIfCanBeSave()
        {

            Guid orderGuid = selectedDevice.OrderGuid;
            Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheitWithDevice(currentDevice);

            Order order = orderService.GetOrder(orderGuid);

            if (order.Number.Contains("#x#"))
            {
                Order mainOrder = orderService.GetMainOrderFromBackup(order);
                orderGuid = mainOrder.Guid;
            }

            nutzeinheitOrder = new NutzeinheitOrder()
            {
                NutzeinheitGuid = nutzeinheit.Guid,
                OrderGuid = orderGuid
            };

            //Check if there is AmwKey in Ne
            NutzeinheitPageViewModel pageViewModel = GetParentProperty();
            bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

            if (!result)
            {
                return false;
            }

            //Check if user wants to procede and delete signature if exists
            Signature signature = GetSignatureForThisOrder();

            if (signature != null)
            {
                DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                if (signatureDeleteWarningResult == DialogResponse.Decline)
                {
                    return false;
                }
            }

            return true;
        }

        private void UpdatePreviousModelPhotos(Photo photo)
        {
            NutzeinheitPageViewModel parent = GetParentProperty();
            ObservableCollection<DeviceVM> listOfAllDevices = parent.ListOfAllDevices;
            ObservableCollection<DeviceVM> listCurrentRemainingDeviceVM = parent.ListOfRemainingDevices;

            DeviceVM deviceInAllDeviceListVM = listOfAllDevices.FirstOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);
            DeviceVM deviceInRemainingDeviceListVM = listCurrentRemainingDeviceVM.FirstOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);

            if (deviceInAllDeviceListVM != null)
            {
                deviceInAllDeviceListVM.Photos.Add(photo);
            }

            if (deviceInRemainingDeviceListVM != null)
            {
                deviceInRemainingDeviceListVM.Photos.Add(photo);
            }
        }

        private async void ReadBarcodExecute()
        {
            try
            {
                PermissionStatus statusCamera = await Permissions.CheckStatusAsync<Permissions.Camera>();

                if (statusCamera == PermissionStatus.Denied)
                {
                    statusCamera = await Permissions.RequestAsync<Permissions.Camera>();
                }
                if (statusCamera == PermissionStatus.Denied) return;

                var barcodeScannerResult = await scannerService.ReadBarcode();
                navigationService.ShowInitPageOnResume = false;

                if (barcodeScannerResult != null)
                {
                    AssignBarcodeValues(barcodeScannerResult.Text);
                }
            }
            catch (NotSupportedException exception)
            {
                logger.Error(exception, "Your system does not support this operaton. Please check if you have an available Camera to perform this opertation.");
                string dialogMessage = localisationService.Get("BarcodeFehlar");
                await dialogService.AcceptAsync(dialogMessage, "OK");
            }
            catch (Exception exception)
            {
                logger.Error(exception, "Error attempting to read Barcode!");
                throw;
            }
        }

        private void AssignBarcodeValues(string barcode)
        {
            if (barcode.Contains(";"))
            {
                string[] splittedQrCode;
                splittedQrCode = barcode.Split(new[] { ";" }, StringSplitOptions.None);
                DeviceNumber = splittedQrCode[1];
                DeviceNote = barcode;
            }
            else if (barcode.Contains("\r\n"))
            {
                string[] splittedQrCode;
                splittedQrCode = barcode.Split(new[] { "\r\n" }, StringSplitOptions.None);
                DeviceNumber = splittedQrCode[0];
                DeviceNote = barcode;
            }
            else
            {
                DeviceNumber = barcode;
            }
        }

        private void AddArticleExecute(AdditionalArticle additionalArticle)
        {
            try
            {
                if (additionalArticle == null)
                {
                    return;
                }

                decimal articlesToAdd = Convert.ToDecimal(AdditionalArticlesNumber, CultureInfo.CurrentCulture);

                AdditionalArticleVM additionalArticleVM = new AdditionalArticleVM()
                {
                    AdditionalArticle = additionalArticle,
                    Amount = articlesToAdd,
                    IsCreatedByApp = true
                };
                SelectedAdditionalArticleList.Add(additionalArticleVM);


                AdditionalArticleList.Remove(additionalArticle);
                SelectedAdditionalArticle = null;
                SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to add an article to list!");
                throw;
            }
        }

        private void RemoveArticleExecute(AdditionalArticleVM additionalArticle)
        {
            try
            {
                if (additionalArticle == null)
                {
                    return;
                }

                SelectedAdditionalArticleList.Remove(additionalArticle);
                AdditionalArticleList.Add(additionalArticle.AdditionalArticle);
                SelectedToRemoveAdditionalAricle = null;
                SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to remove an article from list!");
                throw;
            }
        }

        private void SetInstallationDateExecute()
        {
            try
            {
                DeviceInstallationDate = DateTime.Now;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to set installation date to todays date!");
                throw;
            }
        }

        private void SetReadingDateExecute()
        {
            try
            {
                DeviceReadingDate = DateTime.Now;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to set device reading date to todays date!");
                throw;
            }
        }

        private void SetDeinstallationDateExecute()
        {
            try
            {
                DeviceDeinstallationDate = DateTime.Now;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to set deinstallation date to todays date!");
                throw;
            }
        }

        private async void DeleteDeviceExecute()
        {
            try
            {
                Guid orderGuid = selectedDevice.OrderGuid;
                Device device = deviceService.GetDevice(selectedDevice.DeviceGuid);
                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheitWithDevice(device);

                nutzeinheitOrder = new NutzeinheitOrder()
                {
                    NutzeinheitGuid = nutzeinheit.Guid,
                    OrderGuid = orderGuid
                };

                Signature signature = GetSignatureForThisOrder();

                if (signature != null)
                {
                    DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                    if (signatureDeleteWarningResult == DialogResponse.Decline)
                    {
                        return;
                    }
                }

                NutzeinheitPageViewModel previousViewModel = (NutzeinheitPageViewModel)navigationService.PreviousPageViewModel;
                previousViewModel.RemoveDeviceFromDeviceList(selectedDevice);

                deviceService.DeleteDevice(selectedDevice);
                await navigationService.GoBackAsync();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to delete the device!");
                throw;
            }
        }

        private async void ChangeDeviceOrderKindExecute()
        {
            try
            {
                DeviceOrderKind selectedDeviceOrderKind = await deviceOrderKindChangeDialog.ShowChangeDeviceOrderKindDialog(DeviceOrderKind);

                if (selectedDeviceOrderKind == DeviceOrderKind)
                {
                    return;
                }

                if (selectedDeviceOrderKind == DeviceOrderKind.Exchange)
                {
                    DialogResponse dialogResponse = await dialogService.AcceptDeclineAsync("Sie wollen sicher einen Gerätetausch durchführen?", "Ja", "Nein");

                    if (dialogResponse == DialogResponse.Decline)
                    {
                        return;
                    }
                }

                DeviceOrderKind = selectedDeviceOrderKind;
                selectedDevice.DeviceOrderState.OrderKind = selectedDeviceOrderKind;
                currentDeviceOrderState.OrderKind = selectedDeviceOrderKind;
                deviceService.UpdateDeviceOrderState(currentDeviceOrderState);

                UpdatePreviousPageAfterOrderKindChange();

                if (selectedDeviceOrderKind == DeviceOrderKind.Exchange)
                {
                    await navigationService.GoBackAsync();
                    await Task.Delay(200);
                    await navigationService.NavigateToAsync<WatermeterExchangePageViewModel>(ParentViewModel);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while atteption to change deviceOrderKind of the selected Device");
                throw;
            }
        }

        private void UpdatePreviousPageAfterOrderKindChange()
        {
            NutzeinheitPageViewModel previousViewModel = GetParentProperty();

            DeviceVM currentDeviceVM = previousViewModel.ListOfCurrentDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);
            DeviceVM currentAllDeviceVM = previousViewModel.ListOfAllDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);
            DeviceVM currentRemainingDeviceVM = previousViewModel.ListOfRemainingDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);

            if (currentDeviceVM != null)
            {
                currentDeviceVM.DeviceUiState = new DeviceUiState
                {
                    IsMaintained = currentDeviceVM.IsDeviceMaintained,
                    DeviceOrderState = currentDeviceOrderState
                };
            }

            if (currentRemainingDeviceVM != null)
            {
                currentRemainingDeviceVM.DeviceUiState = new DeviceUiState
                {
                    IsMaintained = currentRemainingDeviceVM.IsDeviceMaintained,
                    DeviceOrderState = currentDeviceOrderState
                };
            }

            if (currentAllDeviceVM != null)
            {
                currentAllDeviceVM.DeviceUiState = new DeviceUiState
                {
                    IsMaintained = currentAllDeviceVM.IsDeviceMaintained,
                    DeviceOrderState = currentDeviceOrderState
                };
            }
        }

        private bool Validate()
        {
            var validator = new WatermeterEditPageViewModelValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }

        #endregion
    }
}
