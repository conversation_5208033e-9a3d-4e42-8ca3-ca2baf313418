﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceMaintenanceDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Implementations
{
    using Eras2AmwApp.Interfaces;
    using Syncfusion.XForms.PopupLayout;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Endiancode.Utilities.Extensions;

    public class DeviceOrderKindChangeDialog : IDeviceOrderKindChangeDialog
    {
        #region fields

        private const string headerMessage = "Gerät Aufträge Type:";

        private TaskCompletionSource<DeviceOrderKind> source;

        private Command acceptButtonCommand;
        private Command declineButtonCommand;

        private Grid grid;
        private ListView listView;
        private SfPopupLayout popupLayout;

        List<string> stringList;

        #endregion

        #region Commands

        public Command AcceptButtonCommand => acceptButtonCommand ?? (acceptButtonCommand = new Command(AcceptButtonExecuted));

        public Command DeclineButtonCommand => declineButtonCommand ?? (declineButtonCommand = new Command(DeclineButtonExecuted));

        #endregion

        public DeviceOrderKind DeviceOrderKind { get; set; }

        public DeviceOrderKindChangeDialog()
        { }

        public Task<DeviceOrderKind> ShowChangeDeviceOrderKindDialog(DeviceOrderKind deviceOrderKind)
        {
            stringList = new List<string>();
            source = new TaskCompletionSource<DeviceOrderKind>();
            DeviceOrderKind = deviceOrderKind;

            listView = CreateListView();

            CreateGrid();
            grid.Children.Add(listView, 0, 0);

            popupLayout = new SfPopupLayout
            {
                PopupView =
                {
                    ShowCloseButton = false,
                    ContentTemplate = new DataTemplate(() => grid),
                    HeaderTemplate = new DataTemplate(() => CreateHeader()),
                    WidthRequest = 300,
                    AppearanceMode = AppearanceMode.TwoButton,
                    ShowFooter = true,
                    AcceptButtonText="OK",
                    DeclineButtonText="Abbrechen",
                    AcceptCommand = AcceptButtonCommand,
                    DeclineCommand = DeclineButtonCommand
                }
            };

            popupLayout.Show();

            return source.Task;
        }

        private Label CreateHeader()
        {
            return new Label
            {
                Padding = 0,
                Text = headerMessage,
                FontAttributes = FontAttributes.Bold,
                TextColor = Color.White,
                BackgroundColor = Color.FromHex("#538EEC"),
                FontSize = 20,
                HorizontalTextAlignment = TextAlignment.Center,
                VerticalTextAlignment = TextAlignment.Center
            };
        }

        private ListView CreateListView()
        {
            List<DeviceOrderKind> enumList = Enum.GetValues(typeof(DeviceOrderKind)).OfType<DeviceOrderKind>().ToList();

            foreach(DeviceOrderKind enumVal in enumList)
            {
                stringList.Add(enumVal.GetDescription());
            }

            string selectedDeviceOrderKind = DeviceOrderKind.GetDescription();

            return new ListView()
            {
                ItemsSource = stringList,
                SelectedItem = selectedDeviceOrderKind,
                SelectionMode = ListViewSelectionMode.Single,
                HasUnevenRows = true,
                IsPullToRefreshEnabled = false
            };
        }

        private void CreateGrid()
        {
            grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(280) });
        }

        private void AcceptButtonExecuted()
        {
            if (listView.SelectedItem is string selectedDeviceOrderKind)
            {
                switch (selectedDeviceOrderKind)
                {
                    case "Montage":
                        DeviceOrderKind = DeviceOrderKind.Assembly;
                        break;
                    case "Wartung":
                        DeviceOrderKind = DeviceOrderKind.Maintenance;
                        break;
                    case "Eichaustausch":
                        DeviceOrderKind = DeviceOrderKind.Exchange;
                        break;
                    case "Ablesung":
                        DeviceOrderKind = DeviceOrderKind.Reading;
                        break;
                    case "Prüfung":
                        DeviceOrderKind = DeviceOrderKind.Inspection;
                        break;
                    case "Hauptablesung":
                        DeviceOrderKind = DeviceOrderKind.MainReading;
                        break;
                }
            }

            source.TrySetResult(DeviceOrderKind);
        }

        private void DeclineButtonExecuted()
        {
            source.TrySetResult(DeviceOrderKind);
        }
    }
}
