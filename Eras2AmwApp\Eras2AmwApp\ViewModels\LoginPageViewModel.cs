﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="LoginPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using System;
using System.Threading.Tasks;
using Eras2AmwApp.BusinessLogic.Interfaces;
using Eras2AmwApp.Common.Exceptions;
using Eras2AmwApp.Common.Interfaces;
using Eras2AmwApp.Domain.Eras2App.Database;
using Eras2AmwApp.Services;
using Eras2AmwApp.WebService.EventArgs;
using FluentValidation.Results;
using Eras2AmwApp.Interfaces;
using ServiceStack;
using Syncfusion.SfBusyIndicator.XForms;
using Eras2AmwApp.Validators;
using Eras2AmwApp.WebService.Interfaces;
using Xamarin.Essentials;
using Xamarin.Forms;
using Polly.Timeout;

namespace Eras2AmwApp.ViewModels
{
    public class LoginPageViewModel : EcViewModelBase
    {
        #region fields

        private readonly IEcDialogService dialogService;
        private readonly ILoginService loginService;
        private readonly INetworkServices networkServices;
        private readonly IAmwWebservice webservice;
        private readonly IAppDeviceInformationService appDeviceInformationService;

        private Command loginCommand;
        private Command appearingCommand;
        private Command disappearingCommand;
        private Command resetUserCommand;
        private Command wipeDatabaseCommand;

        private string _appTitle;
        private string _username;
        private string _userNameErrorText;
        private string _password;
        private string _passwordErrorText;
        private string _loginErrorText;
        private string _webserviceUrl;
        private bool _usernameHasError;
        private bool _passwordHasError;

        #endregion

        #region ctor

        public LoginPageViewModel(
            IServiceLocator serviceLocator,
            IEcDialogService dialogService,
            IEcNavigationService navigationService,
            ILoginService loginService,
            INetworkServices networkServices,
            IAmwWebservice webservice,
            IAppDeviceInformationService appDeviceInformationService)
            : base(serviceLocator, navigationService)
        {
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));
            this.networkServices = networkServices ?? throw new ArgumentNullException(nameof(networkServices));
            this.webservice = webservice ?? throw new ArgumentNullException(nameof(webservice));
            this.appDeviceInformationService = appDeviceInformationService ?? throw new ArgumentNullException(nameof(appDeviceInformationService));

            InitilizeProperties();
        }

        #endregion

        #region commands

        public Command LoginCommand => loginCommand ?? (loginCommand = new Command(LoginExecute));

        public Command AppearingCommand => appearingCommand ?? (appearingCommand = new Command(AppearingExecute));

        public Command DisappearingCommand => disappearingCommand ?? (disappearingCommand = new Command(DisappearingExecute));

        public Command ResetUserCommand => resetUserCommand ?? (resetUserCommand = new Command(ResetUserExecute));

        public Command WipeDatabaseCommand => wipeDatabaseCommand ?? (wipeDatabaseCommand = new Command(WipeDatabaseExacute));

        #endregion

        #region properties

        public string AppTitle
        {
            get { return _appTitle; }
            set { Set(ref _appTitle, value); }
        }

        public string Username
        {
            get { return _username; }
            set { Set(ref _username, value); }
        }

        public string UsernameErrorText
        {
            get { return _userNameErrorText; }
            set { Set(ref _userNameErrorText, value); }
        }

        public bool UsernameHasError
        {
            get { return _usernameHasError; }
            set { Set(ref _usernameHasError, value); }
        }

        public string Password
        {
            get { return _password; }
            set { Set(ref _password, value); }
        }

        public string PasswordErrorText
        {
            get { return _passwordErrorText; }
            set { Set(ref _passwordErrorText, value); }
        }

        public bool PasswordHasError
        {
            get { return _passwordHasError; }
            set { Set(ref _passwordHasError, value); }
        }

        public string LoginErrorText
        {
            get { return _loginErrorText; }
            set { Set(ref _loginErrorText, value); }
        }

        public string WebserviceUrl
        {
            get { return _webserviceUrl; }
            set { Set(ref _webserviceUrl, value); }
        }

        public string AppVersion { get; set; }

        #endregion

        #region private methods

        private void InitilizeProperties()
        {
            Username = string.Empty;
            Password = string.Empty;

            AppTitle = loginService.GetLoginAppTitle();
            AppVersion = "Version: " + Xamarin.Essentials.AppInfo.VersionString;
            Webservice appWebService = appDeviceInformationService.GetAppWebservice();


            WebserviceUrl = appWebService.Url;
            if (WebserviceUrl.Contains("servamw.eras-online.de"))
            {
                WebserviceUrl = WebserviceUrl.Substring(8, WebserviceUrl.Length - 31);
            }
            else if(WebserviceUrl.Contains("AdminTest"))
            {
                Username = "AdminTest";
                Password = "AdminTest";
            }
            else
            {
                WebserviceUrl = WebserviceUrl.Substring(7, WebserviceUrl.Length - 7);
            }
        }

        private async void LoginExecute()
        {
            try
            {
                if (!Validate())
                {
                    return;
                }

                if(Username == "AdminTest" && Password == "AdminTest")
                {
                    Xamarin.Forms.Device.BeginInvokeOnMainThread(() => { dialogService.ShowBusyIndicator(AnimationTypes.Globe, localisationService.Get("LoginInProgress")); });
                    Guid adminUserGuid = await loginService.AdminTestLoginAsync(Username, Password);
                    await navigationService.NavigateToAsync<AppointmentPageViewModel>(adminUserGuid);
                    await navigationService.RemoveLastFromBackStackAsync();
                    return;
                }

                if (IsNetworkAvailable && !IsNetworkTimeDeviationValid)
                {
                    await dialogService.AcceptAsync(localisationService.Get("NtpToLocalTimeDeviationFailed"), localisationService.Get("Ok"), localisationService.Get("Hint"));
                    return;
                }

                if (loginService.ShouldSyncOrders)
                {
                    await dialogService.AcceptAsync(localisationService.Get("SyncOrdersHint"), localisationService.Get("Ok"), localisationService.Get("Hint"));
                }

                Xamarin.Forms.Device.BeginInvokeOnMainThread(() => { dialogService.ShowBusyIndicator(AnimationTypes.Globe, localisationService.Get("LoginInProgress")); });
                loginService.NetworkAccess = IsNetworkAvailable;

                Guid userGuid = await loginService.LoginAsync(Username.Trim().ToLower(), Password.Trim());

                await navigationService.NavigateToAsync<AppointmentPageViewModel>(userGuid);
                await navigationService.RemoveLastFromBackStackAsync();
            }
            catch (UnauthorizedAccessException exp)
            {
                logger.Warning(exp, "LoginExecute(): UnauthorizedAccessException.");
                LoginErrorText = exp.Message;
            }
            catch (WebServiceException exp) when (exp.IsUnauthorized())
            {
                logger.Warning(exp, "LoginExecute(): WebServiceException.");
                LoginErrorText = localisationService.Get("UserLoginFailed4");
            }
            catch (TimeoutRejectedException exp)
            {
                logger.Warning(exp, "LoginExecute(): TimeoutRejectedException.");
                LoginErrorText = "Verbindung konnte nicht hergestellt werden...";
            }
            catch (Eras2AmwException exp)
            {
                logger.Warning(exp, "LoginExecute(): Exception.");
                LoginErrorText = exp.Message;
            }
            catch (Exception exp)
            {
                logger.Error(exp, "LoginExecute(): Exception.");
                LoginErrorText = localisationService.Get("UserLoginFailed5");
            }
            finally
            {
                await Task.Delay(750);
                dialogService.HideBusyIndicator();
            }
        }

        private void AppearingExecute()
        {
            try
            {
                RegisterWebserviceEventHandlers();
            }
            catch (Exception exception)
            {
                logger.Error(exception, "AppearingExecute failed");
                throw;
            }
        }

        private void DisappearingExecute()
        {
            try
            {
                UnregisterWebserviceEventHandlers();
            }
            catch (Exception exception)
            {
                logger.Error(exception, "DisappearingExecute failed");
                throw;
            }
        }

        private bool IsNetworkAvailable => Connectivity.NetworkAccess == NetworkAccess.Internet;

        private bool IsNetworkTimeDeviationValid
        {
            get
            {
                DateTime networkTime = networkServices.GetNetworkTime();

                return (networkTime - DateTime.UtcNow).Duration() < networkServices.MaxNetworkTimeDeviation;
            }
        }

        private void RegisterWebserviceEventHandlers()
        {
            webservice.Info += FileInfoLogging;
            webservice.Info += DialogLogging;

            webservice.Error += FileErrorLogging;
            webservice.Error += DialogLogging;

            webservice.Warning += FileWarningLogging;
            webservice.Warning += DialogLogging;
        }

        private void FileErrorLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Error(arg.Label);
        }

        private void FileWarningLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Warning(arg.Label);
        }

        private void FileInfoLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Information(arg.Label);
        }

        private void UnregisterWebserviceEventHandlers()
        {
            webservice.Info -= DialogLogging;
            webservice.Info -= FileInfoLogging;

            webservice.Error -= DialogLogging;
            webservice.Error -= FileErrorLogging;

            webservice.Warning -= DialogLogging;
            webservice.Warning -= FileWarningLogging;
        }

        private void DialogLogging(object sender, WebserviceEventArgs arg)
        {
            Xamarin.Forms.Device.BeginInvokeOnMainThread(() =>
            {
                dialogService.SetBusyIndicatorText(arg.Label);
            });
        }

        private async void ResetUserExecute()
        {
            try
            {
                DialogResponse result = await dialogService.AcceptDeclineAsync("Sind Sie sicher, dass Sie den aktuellen Benutzer löschen möchten ?", "JA", "NEIN");

                if (result == DialogResponse.Decline)
                {
                    return;
                }

                loginService.DeleteUsers();
            }
            catch (Exception exp)
            {
                logger.Error(exp, "ResetUser Failed!");
                throw;
            }

        }

        private async void WipeDatabaseExacute()
        {
            try
            {
                DialogResponse result = await dialogService.AcceptDeclineAsync("Sind Sie sicher, dass Sie die aktuelle Datenbank löschen möchten ?", "JA", "NEIN");

                if (result == DialogResponse.Decline)
                {
                    return;
                }

                loginService.WipeDatabase();
                await dialogService.AcceptAsync("Die Datenbank wurde erfolgreich gelöscht", "OK");
            }
            catch (Exception exp)
            {
                logger.Error(exp, "ResetUser Failed!");
                throw;
            }
        }

        private bool Validate()
        {
            var validator = new LoginPageViewModelValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }

        #endregion
    }
}
