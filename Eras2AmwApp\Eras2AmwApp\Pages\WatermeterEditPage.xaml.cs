﻿namespace Eras2AmwApp.Pages
{
    using Eras2AmwApp.ViewModels;
    using System;
    using Xamarin.Forms;
    using Xamarin.Forms.Xaml;

    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class WatermeterEditPage : ContentPage
    {
        public static readonly BindableProperty EnableBackButtonOverrideProperty =
            BindableProperty.Create(
                nameof(EnableBackButtonOverride),
                typeof(bool),
                typeof(WatermeterEditPage),
                true);

        private readonly WatermeterEditPageViewModel watermeterEditVM;

        public bool EnableBackButtonOverride
        {
            get => (bool)GetValue(EnableBackButtonOverrideProperty);
            set => SetValue(EnableBackButtonOverrideProperty, value);
        }

        public WatermeterEditPage(WatermeterEditPageViewModel viewModel)
        {
            InitializeComponent();
            BindingContext = watermeterEditVM =  viewModel;
        }

        public Action CustomBackButtonAction => async () => await watermeterEditVM.DeselectListViewItemOnBackButton();

        protected override bool OnBackButtonPressed()
        {
            DeselectListViewItem();

            return true;
        }

        private void DeselectListViewItem()
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                await watermeterEditVM.DeselectListViewItemOnBackButton();
            });
        }
    }
}