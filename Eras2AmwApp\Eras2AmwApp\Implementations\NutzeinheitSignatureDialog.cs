﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitSignatureDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Implementations
{
    using Syncfusion.XForms.PopupLayout;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using Models;
    using System;
    using System.Collections.Generic;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System.Linq;
    using Eras2AmwApp.Interfaces;
    using SignaturePad.Forms;
    using Eras2AmwApp.BusinessLogic.Models;
    using System.IO;
    using Eras2AmwApp.ViewModels;
    using Eras2AmwApp.Services;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;

    public class NutzeinheitSignatureDialog : INutzeinheitSignatureDialog
    {
        #region fields

        private readonly ISignatureService signatureService;
        private readonly IAppSettings appSettings;

        private Command deleteSignatureCommand;

        private TaskCompletionSource<object> source;

        private SfPopupLayout sfPopupLayout;
        private SignaturePadView signaturePad;
        private ImageButton deleteSignatureButton;

        private EcViewModelBase parentViewModel;
        private NutzeinheitOrder nutzeinheitOrder;

        #endregion

        #region ctor

        public NutzeinheitSignatureDialog(ISignatureService signatureService, IAppSettings appSettings)
        {
            this.signatureService = signatureService ?? throw new ArgumentNullException(nameof(signatureService));
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
        }

        #endregion

        #region Commands

        public Command DeleteSignatureCommand => deleteSignatureCommand ?? (deleteSignatureCommand = new Command(DeleteSignatureExecute));

        #endregion

        #region public methods

        public Task ShowNutzeinheiSignatureDialog(NutzeinheitVM parentVM)
        {
            parentViewModel = parentVM.ParentViewModel;
            nutzeinheitOrder = parentVM.NutzeinheitOrder;
            source = new TaskCompletionSource<object>();

            sfPopupLayout = CreateDialogWindow();
            sfPopupLayout.Show();

            return source.Task;
        }

        public NutzeinheitPageViewModel GetParentProperty()
        {
            return parentViewModel as NutzeinheitPageViewModel;
        }

        #endregion

        #region private methods

        private SfPopupLayout CreateDialogWindow()
        {
            Guid nutzeinheitGuid = nutzeinheitOrder.NutzeinheitGuid;
            Guid orderGuid = nutzeinheitOrder.OrderGuid;
            Signature oldSignature = signatureService.GetSignatureForOrder(nutzeinheitGuid, orderGuid);

            signaturePad = GetSignaturePadSource(oldSignature);

            Grid grid = CreateGrid();
            grid.Children.Add(signaturePad, 0, 0);

            DataTemplate headerTempleteView = new DataTemplate(() =>
            {
                return CreateHeader();
            });

            Grid footerStackLayout = CreateFooterGrid();

            sfPopupLayout = new SfPopupLayout()
            {
                StaysOpen = true,
                PopupView =
                {
                    ShowCloseButton = false,
                    HeaderTemplate = headerTempleteView,
                    FooterTemplate = new DataTemplate (() => footerStackLayout),
                    ContentTemplate = new DataTemplate(() => grid),
                    MinimumWidthRequest = 510,
                    MinimumHeightRequest = 460,
                    ShowFooter = true
                }
            };

            return sfPopupLayout;
        }

        private Grid CreateGrid()
        {
            Grid grid = new Grid();

            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(360)});
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(508) });
            return grid;
        }

        private Grid CreateFooterGrid()
        {
            Grid grid = new Grid
            {
                Margin = 0
            };

            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(70, GridUnitType.Star)});
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Auto) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Auto) });

            deleteSignatureButton = CreateDeleteSignatureButton();
            Label ok = new Label
            {
                Margin = 5,
                Text = "OK",
                FontAttributes = FontAttributes.Bold,
                FontSize = 16,
                VerticalTextAlignment = TextAlignment.Center,
                HorizontalTextAlignment = TextAlignment.End,
            };
            ok.GestureRecognizers.Add(new TapGestureRecognizer { Command = new Command(AcceptButtonExecuted) });

            grid.Children.Add(deleteSignatureButton, 1, 0);
            grid.Children.Add(ok, 2, 0);
            return grid;
        }

        private Label CreateHeader()
        {
            Label headerContent = new Label
            {
                Padding = 0,
                Text = "Nutzeinheit Unterschrift",
                FontAttributes = FontAttributes.Bold,
                TextColor = Color.White,
                BackgroundColor = Color.FromHex("#538EEC"),
                FontSize = 20,
                HorizontalTextAlignment = TextAlignment.Center,
                VerticalTextAlignment = TextAlignment.Center
            };
            return headerContent;
        }

        private SignaturePadView GetSignaturePadSource(Signature oldSignature)
        {
            SignaturePadView signaturePadView = new SignaturePadView();

            if (oldSignature != null)
            {
                if (oldSignature.Path == null)
                {
                    string signatureDirectoryPath = Path.Combine(appSettings.SignaturesDirectory, oldSignature.Guid.ToString()) + ".png";
                    if (File.Exists(signatureDirectoryPath))
                    {
                        oldSignature.Path = signatureDirectoryPath;
                        signatureService.UpdateSignature(oldSignature);
                    }
                }

                ImageSource oldSignatureImage = ImageSource.FromFile(oldSignature.Path);
                signaturePadView.BackgroundImageView.Source = oldSignatureImage;
                signaturePadView.IsEnabled = false;
            }
            signaturePadView.BackgroundColor = Color.LightGray;
            signaturePadView.CaptionText = "Unterschrift";
            signaturePadView.ClearText = "";

            return signaturePadView;
        }

        private ImageButton CreateDeleteSignatureButton()
        {
            ImageButton button = new ImageButton()
            {
                Margin = 0,
                BackgroundColor = Color.Transparent,
                WidthRequest = 50,
                HeightRequest = 50,
                Source = "deleteIcon.png",
                Command = DeleteSignatureCommand,
                HorizontalOptions = LayoutOptions.End
            };

            return button;
        }

        private async void DeleteSignatureExecute()
        {
            if (!await CheckIfCanBeSave())
            {
                return;
            }

            var previousViewModel = GetParentProperty();
            previousViewModel.SetNutzeinheitStateInProgress();

            signaturePad.Clear();
            signaturePad.IsEnabled = true;
            Signature oldSignature = signatureService.GetSignatureForOrder(nutzeinheitOrder.NutzeinheitGuid, nutzeinheitOrder.OrderGuid);

            if (oldSignature != null)
            {
                if (oldSignature.Path != null)
                {
                    signatureService.RemoveSignatureFromDevice(oldSignature);
                }
                signatureService.DeleteSignature(oldSignature);
                signaturePad.BackgroundImageView.Source = null;
            }
        }

        private async Task<bool> CheckIfCanBeSave()
        {
            //Check if there is AmwKey in Ne
            var pageViewModel = GetParentProperty();
            bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

            if (!result)
            {
                return false;
            }

            return true;
        }

        private async void AcceptButtonExecuted()
        {
            if (!await CheckIfCanBeSave())
            {
                return;
            }

            using (Stream image = await signaturePad.GetImageStreamAsync(SignatureImageFormat.Png))
            {
                if(image != null)
                {
                    Signature oldSignature = signatureService.GetSignatureForOrder(nutzeinheitOrder.NutzeinheitGuid, nutzeinheitOrder.OrderGuid);
                    if (oldSignature != null)
                    {
                        signatureService.DeleteSignature(oldSignature);
                    }
                    signatureService.SaveSignature(nutzeinheitOrder, image);
                }
            }
            source.TrySetResult(DialogResponse.Accept);
            sfPopupLayout.Dismiss();
        }

        #endregion
    }

}
