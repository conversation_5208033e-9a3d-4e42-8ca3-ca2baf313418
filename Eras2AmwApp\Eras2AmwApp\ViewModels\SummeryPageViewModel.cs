﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="SummeryPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.Models;
    using SignaturePad.Forms;
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using Device = Domain.Eras2Amw.Models.Device;

    public class SummeryPageViewModel : EcViewModelBase
    {
        #region fields

        private readonly IOrderService orderService;
        private readonly ISignatureService signatureService;
        private readonly INutzeinheitService nutzeinheitService;

        private Command saveSignatureCommand;
        private Command deleteSignatureCommand;

        private SignaturePadView _signaturePad;
        private string _allDeviceInfo;
        private string _maintainedDeviceInfo;
        private string _notMaintainedDeviceInfo;

        private NutzeinheitVM selectedNutzeinheitVM;
        private int orderDeviceCount = 0;
        private int orderMaintainedDeviceCount = 0;
        #endregion

        #region ctor

        public SummeryPageViewModel(IServiceLocator serviceLocator, 
            IEcNavigationService navigationService,
            IOrderService orderService,
            ISignatureService signatureService,
            INutzeinheitService nutzeinheitService)
           : base(serviceLocator, navigationService)
        {
            this.orderService = orderService ?? throw new ArgumentNullException(nameof(orderService));
            this.signatureService = signatureService ?? throw new ArgumentNullException(nameof(signatureService));
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));

            SignaturePad = new SignaturePadView();
            DeviceAdditionalArticleList = new ObservableCollection<DeviceAdditionalArticle>();
            AmwInfoKeyErrorList = new ObservableCollection<AmwKeyVM>();
        }

        #endregion

        #region commands

        public Command SaveSignatureCommand => saveSignatureCommand ?? (saveSignatureCommand = new Command(SaveSignatureExecute));

        public Command DeleteSignatureCommand => deleteSignatureCommand ?? (deleteSignatureCommand = new Command(DeleteSignatureExecute));

        #endregion

        #region properties

        public SignaturePadView SignaturePad
        {
            get { return _signaturePad; }
            set { Set(ref _signaturePad, value); }
        }

        public string AllDeviceInfo
        {
            get { return _allDeviceInfo; }
            set { Set(ref _allDeviceInfo, value); }
        }

        public string MaintainedDeviceInfo
        {
            get { return _maintainedDeviceInfo; }
            set { Set(ref _maintainedDeviceInfo, value); }
        }

        public string NotMaintainedDeviceInfo
        {
            get { return _notMaintainedDeviceInfo; }
            set { Set(ref _notMaintainedDeviceInfo, value); }
        }

        public ObservableCollection<DeviceAdditionalArticle> DeviceAdditionalArticleList { get; set; }

        public ObservableCollection<AmwKeyVM> AmwInfoKeyErrorList { get; set; }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is NutzeinheitVM nutzeinheitVM)
            {
                selectedNutzeinheitVM = nutzeinheitVM;
                InitilizeProperties();
            }
            return base.SetupAsync(navigationData);
        }

        #endregion

        #region private methods

        private void InitilizeProperties()
        {
            try
            {
                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(selectedNutzeinheitVM.NutzeinheitGuid);
                List<Device> allNutzeinheitDevices = nutzeinheit.Devices.ToList();
                Guid orderGuid = selectedNutzeinheitVM.NutzeinheitOrder.OrderGuid;
                
                List<Device> nutzeinheitOrderDevices = new List<Device>();

                foreach (Device device in allNutzeinheitDevices)
                {
                    List<DeviceOrderState> deviceOrderStates = device.OrderStates.ToList();
                    if (deviceOrderStates.Any())
                    {
                        DeviceOrderState orderState = deviceOrderStates.Where(x => x.OrderGuid == orderGuid).SingleOrDefault();
                        orderDeviceCount++;
                        nutzeinheitOrderDevices.Add(device);
                    }
                }
                
                foreach (Device device in nutzeinheitOrderDevices)
                {
                    InitDeviceErrors(device);

                    IList<DeviceAdditionalArticle> deviceAdditionalArticleList = device.DeviceAdditionalArticles;

                    if (deviceAdditionalArticleList.Any())
                    {
                        foreach(DeviceAdditionalArticle deviceAdditionalArticle in deviceAdditionalArticleList)
                        {
                            DeviceAdditionalArticle existingArticle = DeviceAdditionalArticleList.SingleOrDefault(x => x.AdditionalArticle.Label == deviceAdditionalArticle.AdditionalArticle.Label);
                            if (existingArticle != null)
                            {
                                DeviceAdditionalArticleList.Remove(existingArticle);
                                existingArticle.Quantity += deviceAdditionalArticle.Quantity;
                                DeviceAdditionalArticleList.Add(existingArticle);
                            }
                            else
                            {
                                DeviceAdditionalArticleList.Add(deviceAdditionalArticle);
                            }
                        } 
                    }
                }

                DisplayInformations(orderDeviceCount, orderMaintainedDeviceCount);
                InitSignature();
            }
            catch(Exception e)
            {		
                logger.Error(e, "Excepiton occured while initializing SummaryPage!");
                throw;
            }
        }

        private void InitDeviceErrors(Device device)
        {
            if (device.IsMaintained)
            {
                orderMaintainedDeviceCount++;
            }
            else
            {
                AmwInfoKey AmwInfoKeyError = device.OrderStates.Select(x => x.AmwInfoKey).SingleOrDefault();
                if (AmwInfoKeyError != null)
                {
                    AmwKeyVM existingError = AmwInfoKeyErrorList.SingleOrDefault(x => x.AmwInfoKey.Info == AmwInfoKeyError.Info);
                    if (existingError != null)
                    {
                        AmwInfoKeyErrorList.Remove(existingError);
                        existingError.Amount++;
                        AmwInfoKeyErrorList.Add(existingError);
                    }
                    else
                    {
                        AmwKeyVM newAmWKeyVm = new AmwKeyVM()
                        {
                            AmwInfoKey = AmwInfoKeyError,
                            Amount = 1,
                            IsCreatedByApp= true
                        };
                        AmwInfoKeyErrorList.Add(newAmWKeyVm);
                    }
                }
            }
        }

        private void DisplayInformations(int orderDeviceCount, int orderMaintainedDeviceCount)
        {
            int orderNotMaintainedDeviceCount = orderDeviceCount - orderMaintainedDeviceCount;

            AllDeviceInfo = $"Geräteanzahl in dieser Nutzeinheit : {orderDeviceCount}.";
            MaintainedDeviceInfo = $"Geräteanzahl im Zustand \"gewartet\" : {orderMaintainedDeviceCount}.";
            NotMaintainedDeviceInfo = $"Geräteanzahl im Zustand \"nicht gewartet\" : {orderNotMaintainedDeviceCount}.";
        }

        private void InitSignature()
        {
            Signature oldSignature = signatureService.GetSignatureForOrder(selectedNutzeinheitVM.NutzeinheitGuid, selectedNutzeinheitVM.NutzeinheitOrder.OrderGuid);
            if (oldSignature != null)
            {
                ImageSource oldSignatureImage = ImageSource.FromFile(oldSignature.Path);
                SignaturePad.BackgroundImageView.Source = oldSignatureImage;
            }

        }

        private async void SaveSignatureExecute()
        {
            try
            {
                using (Stream image = await SignaturePad.GetImageStreamAsync(SignatureImageFormat.Png))
                {
                    Signature oldSignature = signatureService.GetSignatureForOrder(selectedNutzeinheitVM.NutzeinheitGuid, selectedNutzeinheitVM.NutzeinheitOrder.OrderGuid);
                    if (image == null)
                    {
                        await navigationService.GoBackAsync();
                        return;
                    }

                    if (oldSignature != null)
                    {
                        if (oldSignature.Path != null)
                        {
                            signatureService.RemoveSignatureFromDevice(oldSignature);
                        }
                        signatureService.DeleteSignature(oldSignature);
                    }
                    signatureService.SaveSignature(selectedNutzeinheitVM.NutzeinheitOrder, image);
                }

                UpdateNutzeinheitState();
                UpdateOrderState();

                await navigationService.GoBackAsync();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to save the signature!");
                throw;
            }
        }

        private void DeleteSignatureExecute()
        {
            try
            {
                DeleteSignature();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to delete signature from memory!");
                throw;
            }
        }

        private void DeleteSignature()
        {
            SignaturePad.Clear();
            Signature oldSignature = signatureService.GetSignatureForOrder(selectedNutzeinheitVM.NutzeinheitGuid, selectedNutzeinheitVM.NutzeinheitOrder.OrderGuid);

            if (oldSignature != null)
            {
                if (oldSignature.Path != null)
                {
                    signatureService.RemoveSignatureFromDevice(oldSignature);
                }
                signatureService.DeleteSignature(oldSignature);
                SignaturePad.BackgroundImageView.Source = null;
            }
        }

        private void UpdateNutzeinheitState()
        {
            NutzeinheitOrder nutzeinheitOrder = selectedNutzeinheitVM.NutzeinheitOrder;
            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitOrder);
            nutzeinheitOrderState.ProcessState = ProcessState.Updating;
            nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);
        }

        private void UpdateOrderState()
        {
            NutzeinheitOrder nutzeinheitOrder = selectedNutzeinheitVM.NutzeinheitOrder;
            Order order = orderService.GetOrder(nutzeinheitOrder.OrderGuid);
            OrderState orderState = order.OrderState;
            orderState.ProcessState = ProcessState.Updating;
            orderService.UpdateOrderState(orderState);
        }

        #endregion
    }
}
