﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppointmentVm.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Models
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using GalaSoft.MvvmLight;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.ViewModels;

    public class AppointmentVm : ViewModelBase, ISupportParentViewModel
    {
        public AppointmentPageViewModel GetParentProperty()
        {
            return ParentViewModel as AppointmentPageViewModel;
        }

        private EcViewModelBase _parentViewModel;
        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { Set(ref _parentViewModel, value); }
        }

        private Guid _guid;
        public Guid Guid
        {
            get { return _guid; }
            set { Set(ref _guid, value); }
        }

        private OrderState _orderState;
        public OrderState OrderState
        {
            get { return _orderState; }
            set { Set(ref _orderState, value); }
        }

        private ProcessState _nutzeinheitenState;
        public ProcessState NutzeinheitenState
        {
            get { return _nutzeinheitenState; }
            set { Set(ref _nutzeinheitenState, value); }
        }

        private DateTime _startTime;
        public DateTime StartTime
        {
            get { return _startTime; }
            set { Set(ref _startTime, value); }
        }

        private DateTime _endTime;
        public DateTime EndTime
        {
            get { return _endTime; }
            set { Set(ref _endTime, value); }
        }

        private string _dateTime;
        public string DateTime
        {
            get { return _dateTime; }
            set { Set(ref _dateTime, value); }
        }

        private string _orderNumber;
        public string OrderNumber
        {
            get { return _orderNumber; }
            set { Set(ref _orderNumber, value); }
        }

        private bool _hasNote;
        public bool HasNote
        {
            get { return _hasNote; }
            set { Set(ref _hasNote, value); }
        }

        private string _orderLabel;
        public string OrderLabel
        {
            get { return _orderLabel; }
            set { Set(ref _orderLabel, value); }
        }

        private string _address;
        public string Address
        {
            get { return _address; }
            set { Set(ref _address, value); }
        }

        private string _orderNote;
        public string OrderNote
        {
            get { return _orderNote; }
            set { Set(ref _orderNote, value); }
        }
    }
}