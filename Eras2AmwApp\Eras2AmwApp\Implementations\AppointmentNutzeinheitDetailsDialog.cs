﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppointmentNutzeinheitDetailsDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Implementations
{
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.Models;
    using Syncfusion.XForms.PopupLayout;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Xamarin.Forms;

    public class AppointmentNutzeinheitDetailsDialog : IAppointmentNutzeinheitDetailsDialog
    {

        #region fields

        private Command acceptButtonCommand;

        private TaskCompletionSource<DialogResponse> source;
        private SfPopupLayout sfPopupLayout;

        private ListView listView;

        #endregion

        #region Commands

        public Command AcceptButtonCommand => acceptButtonCommand ?? (acceptButtonCommand = new Command(AcceptButtonExecuted));

        #endregion

        public enum DialogResponse
        {
            Accept,
            Decline
        }

        #region public methods

        public Task<DialogResponse> ShowAppointmentNutzeinheitInfo(Dictionary<string,string> dictOfAddressNutzeinheiten)
        {
            source = new TaskCompletionSource<DialogResponse>();

            sfPopupLayout = CreateDialogWindow(dictOfAddressNutzeinheiten);
            sfPopupLayout.Show();

            return source.Task;
        }

        #endregion

        #region private methods

        private SfPopupLayout CreateDialogWindow(Dictionary<string, string> dictOfAddressNutzeinheiten)
        {
            int numOfAddressRows = dictOfAddressNutzeinheiten.Count;

            Grid grid = CreateGrid(numOfAddressRows);

            
            Label headerLabelOne = CreateHeaderFirst();
            Label headerLabelTwo = CreateHeaderTwo();

            grid.Children.Add(headerLabelOne, 0, 0);
            grid.Children.Add(headerLabelTwo, 1, 0);

            List<NutzeinheitInfo> listOfNutzeinheitInfo = new List<NutzeinheitInfo>();

            for (int x = 1; x <= numOfAddressRows; x++)
            {
                NutzeinheitInfo nutzeinheitInfo = new NutzeinheitInfo()
                {
                    NutzeinheitAddress = dictOfAddressNutzeinheiten.Keys.ElementAt(x - 1),
                    NutzeinheitCount = dictOfAddressNutzeinheiten.Values.ElementAt(x - 1)
                };
                listOfNutzeinheitInfo.Add(nutzeinheitInfo);
            }
            listView = CreateListView(listOfNutzeinheitInfo);

            grid.Children.Add(listView, 0, 1);
            Grid.SetColumnSpan(listView, 2);


            DataTemplate headerTempleteView = new DataTemplate(() =>
            {
                return CreateHeader();
            });

            SfPopupLayout popupLayout = new SfPopupLayout()
            {
                PopupView =
                {
                    
                    ShowCloseButton = false,
                    HeaderTemplate = headerTempleteView,
                    ContentTemplate = new DataTemplate(() => grid),
                    MinimumWidthRequest = 330,
                    MinimumHeightRequest = 300,
                    AppearanceMode = AppearanceMode.OneButton,
                    ShowFooter = false
                }
            };

            return popupLayout;
        }

        private ListView CreateListView(List<NutzeinheitInfo> listOfNutzeinheitInfo)
        {
            listView = new ListView()
            {
                ItemsSource = listOfNutzeinheitInfo,
                ItemTemplate = new DataTemplate(typeof(CustomCell)),
                SelectionMode = ListViewSelectionMode.None,
                HasUnevenRows = false,
                IsPullToRefreshEnabled = false,
                RowHeight = 30
            };

            return listView;
        }

        private Label CreateHeaderFirst()
        {
            var label = new Label
            {
                Margin = 5,
                BackgroundColor = Color.White,
                HorizontalOptions = LayoutOptions.StartAndExpand,
                VerticalOptions = LayoutOptions.CenterAndExpand,
                FontAttributes = FontAttributes.Bold,
                FontSize = 15,
                TextColor = Color.Black,
                Text = "Adressen"
            };

            return label;
        }

        private Label CreateHeaderTwo()
        {
            var label = new Label
            {
                Margin = new Thickness(5, 0, 10, 0),
                BackgroundColor = Color.White,
                HorizontalOptions = LayoutOptions.EndAndExpand,
                VerticalOptions = LayoutOptions.CenterAndExpand,
                FontAttributes = FontAttributes.Bold,
                FontSize = 15,
                TextColor = Color.Black,
                Text = "Anzahl NE"
            };

            return label;
        }

        private Grid CreateGrid(int numOfAddressRows)
        {
            Grid grid = new Grid
            {
                RowSpacing = 1
            };

            //One row for Header
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });

            for (int x = 1; x <= numOfAddressRows; x++)
            {
                //rows for each address in dict
                grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });
            }

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(220) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });
            return grid;
        }

        private Label CreateHeader()
        {
            Label headerContent = new Label
            {
                Padding = 0,
                Text = "Nutzeinheiten Info",
                FontAttributes = FontAttributes.Bold,
                TextColor = Color.White,
                BackgroundColor = Color.FromHex("#538EEC"),
                FontSize = 20,
                HorizontalTextAlignment = TextAlignment.Center,
                VerticalTextAlignment = TextAlignment.Center
            };
            return headerContent;
        }

        private void AcceptButtonExecuted()
        {
            source.SetResult(DialogResponse.Accept);
        }

        public class CustomCell : ViewCell
        {
            public CustomCell()
            {
                Grid listViewGrid = new Grid
                {
                    RowSpacing = 1
                };
                Label firstLabel = CreateRowOne();
                Label secondLabel = CreateRowTwo();

                firstLabel.SetBinding(Label.TextProperty, "NutzeinheitAddress");
                secondLabel.SetBinding(Label.TextProperty, "NutzeinheitCount");

                listViewGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });
                listViewGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(220) });
                listViewGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(80) });

                listViewGrid.Children.Add(firstLabel, 0, 0);
                listViewGrid.Children.Add(secondLabel, 1, 0);

                IsEnabled = false;
                View = listViewGrid;
            }

            private Label CreateRowOne()
            {
                var label = new Label
                {
                    Margin = 5,
                    BackgroundColor = Color.White,
                    HorizontalOptions = LayoutOptions.StartAndExpand,
                    VerticalOptions = LayoutOptions.CenterAndExpand,
                    FontSize = 15,
                    TextColor = Color.Black,
                };

                return label;
            }

            private Label CreateRowTwo()
            {
                var label = new Label
                {
                    Margin = new Thickness(5, 0, 10, 0),
                    BackgroundColor = Color.White,
                    HorizontalOptions = LayoutOptions.EndAndExpand,
                    VerticalOptions = LayoutOptions.CenterAndExpand,
                    FontSize = 15,
                    TextColor = Color.Black
                };

                return label;
            }
        }

        #endregion
    }
}
