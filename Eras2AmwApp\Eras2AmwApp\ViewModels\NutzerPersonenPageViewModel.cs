﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerPersonenPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using Xamarin.Essentials;
    using Xamarin.Forms;
    using Endiancode.Utilities.Extensions;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.Models;
    using Eras2AmwApp.Services;
    using System.Collections.ObjectModel;
    using Eras2AmwApp.CustomControls;
    using Eras2Amw.Service.Server.ServiceModel.Common;
    using NutzerPersonen = Domain.Eras2Amw.Models.NutzerPersonen;
    using Nutzer = Domain.Eras2Amw.Models.Nutzer;

    public class NutzerPersonenPageViewModel : EcViewModelBase, ISupportParentViewModel
    {
        #region fields

        private readonly INutzerService nutzerService;

        private Command saveNutzerPersonenCommand;
        private Command deleteNutzerPersonenCommand;
        private Command addNutzerPersonenCommand;
        private Command newEmptyNutzerPersonenCommand;

        private EcViewModelBase _parentViewModel;

        private bool _isLastDateNull;
        private bool _canNutzerPersonenBeDeleted;

        private string _nutzerPersonenNumber;
        private DateTime _nutzerPersonenFromDate;
        private DateTime? _nutzerPersonenToDate;
        private NutzerPersonen _selectedNutzerPersonen;

        private NutzeinheitVM selectedNutzer;

        #endregion

        #region ctor

        public NutzerPersonenPageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            INutzerService nutzerService)
           : base(serviceLocator, navigationService)
        {
            this.nutzerService = nutzerService ?? throw new ArgumentNullException(nameof(nutzerService));

            NutzerPersonenObsCollec = new ObservableCollection<NutzerPersonen>();
        }

        #endregion

        #region commands

        public Command AddNutzerPersonenCommand => addNutzerPersonenCommand ?? (addNutzerPersonenCommand = new Command(AddNutzerPersonenExecute));

        public Command SaveNutzerPersonenCommand => saveNutzerPersonenCommand ?? (saveNutzerPersonenCommand = new Command(SaveNutzerPersonenExecute));

        public Command DeleteNutzerPersonenCommand => deleteNutzerPersonenCommand ?? (deleteNutzerPersonenCommand = new Command(DeleteNutzerPersonenExecute, () => CanNutzerPersonenBeDeleted));

        public Command NewEmptyNutzerPersonenCommand => newEmptyNutzerPersonenCommand ?? (newEmptyNutzerPersonenCommand = new Command(NewEmptyNutzerPersonenExecute, () => IsLastDateNull));

        #endregion

        #region properties

        public bool CanNutzerPersonenBeDeleted
        {
            get { return _canNutzerPersonenBeDeleted; }
            set { Set(ref _canNutzerPersonenBeDeleted, value); }
        }

        public bool IsLastDateNull
        {
            get { return _isLastDateNull; }
            set { Set(ref _isLastDateNull, value); }
        }

        public string NutzerPersonenNumber
        {
            get { return _nutzerPersonenNumber; }
            set { Set(ref _nutzerPersonenNumber, value); }
        }

        public DateTime NutzerPersonenFromDate
        {
            get { return _nutzerPersonenFromDate; }
            set { Set(ref _nutzerPersonenFromDate, value); }
        }

        public DateTime? NutzerPersonenToDate
        {
            get { return _nutzerPersonenToDate; }
            set { Set(ref _nutzerPersonenToDate, value); }
        }

        public ObservableCollection<NutzerPersonen> NutzerPersonenObsCollec { get; set; }

        public NutzerPersonen SelectedNutzerPersonen
        {
            get { return _selectedNutzerPersonen; }
            set
            {
                if (value != _selectedNutzerPersonen)
                {
                    Set(ref _selectedNutzerPersonen, value);
                    DisplaySelectedNutzerPersonen(_selectedNutzerPersonen);
                }
            }
        }

        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { Set(ref _parentViewModel, value); }
        }

        public NutzerEditPageViewModel GetParentProperty()
        {
            return ParentViewModel as NutzerEditPageViewModel;
        }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is NutzeinheitVM nutzerVM)
            {
                selectedNutzer = nutzerVM;
                ParentViewModel = nutzerVM.ParentViewModel;

                Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                List<NutzerPersonen> nutzerPersonen = currentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();

                InitializeObservCollection(nutzerPersonen);
                InitilizeProperties(currentNutzer,nutzerPersonen);
            }
            return base.SetupAsync(navigationData);
        }

        #endregion

        private void InitilizeProperties(Nutzer currentNutzer, List<NutzerPersonen> nutzerPersonen)
        {
            try
            {
                NutzerPersonen currentFirstNutzerPersonen = nutzerPersonen.FirstOrDefault();
                if (currentFirstNutzerPersonen != null)
                {
                    SelectedNutzerPersonen = currentFirstNutzerPersonen;
                    if(currentFirstNutzerPersonen.RangeTo.HasValue)
                    {
                        IsLastDateNull = true;
                    }
                }
                else // if there is no Personen in the list then we have empty Personen Number and From date is set to Nutzer MoveInDate
                {
                    NutzerPersonenNumber = "";
                    NutzerPersonenFromDate = currentNutzer.MoveInDate;
                    IsLastDateNull = false;
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while initializing properties for NutzeinheitEditPageVM!");
                throw;
            }
        }

        private void InitializeObservCollection(List<NutzerPersonen> nutzerPersonen)
        {

            NutzerPersonenObsCollec.Clear();
            foreach (NutzerPersonen nePerson in nutzerPersonen)
            {
                NutzerPersonenObsCollec.Add(nePerson);
            }
        }

        private void DisplaySelectedNutzerPersonen(NutzerPersonen selectedNutzerPersonen)
        {
            if(selectedNutzerPersonen.NumberOfPeople.HasValue)
            {
                NutzerPersonenNumber = selectedNutzerPersonen.NumberOfPeople.Value.ToString();
            }
            else
            {
                NutzerPersonenNumber = "";
            }

            NutzerPersonenFromDate = selectedNutzerPersonen.RangeFrom;
            if (selectedNutzerPersonen.RangeTo.HasValue)
            {
                NutzerPersonenToDate = selectedNutzerPersonen.RangeTo.Value;
            }
            else
            {
                NutzerPersonenToDate = null;
            }
            CanNutzerPersonenBeDeleted = selectedNutzerPersonen.IsCreatedByApp;
        }

        private void AddNutzerPersonenExecute()
        {
            try
            {
                //currentNutzer that we are currently inside
                Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                NutzerPersonen newNutzerPersonen = new NutzerPersonen
                {
                    NutzerGuid = currentNutzer.Guid,
                    IsCreatedByApp = true
                };

                if (int.TryParse(NutzerPersonenNumber, out int numberOfPeople))
                {
                    newNutzerPersonen.NumberOfPeople = numberOfPeople;
                }

                List<NutzerPersonen> currentNutzerPersonenList = currentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();

                if (currentNutzerPersonenList.Count > 0)
                {
                    NutzerPersonen currentNutzerPersonen = currentNutzerPersonenList[0];
                    if(currentNutzerPersonen.RangeTo != null && currentNutzerPersonen.RangeFrom < NutzerPersonenFromDate)
                    {
                        newNutzerPersonen.RangeFrom = currentNutzerPersonen.RangeTo.Value.AddDays(1);
                        newNutzerPersonen.RangeTo = null;
                        nutzerService.AddNutzerPersonen(newNutzerPersonen);
                    }
                    else if (currentNutzerPersonen.RangeTo == null && currentNutzerPersonen.RangeFrom < NutzerPersonenFromDate)
                    {
                        newNutzerPersonen.RangeFrom = NutzerPersonenFromDate;
                        currentNutzerPersonen.RangeTo = newNutzerPersonen.RangeFrom.AddDays(-1);
                        nutzerService.UpdateNutzerPersonen(currentNutzerPersonen);
                        nutzerService.AddNutzerPersonen(newNutzerPersonen);
                    }
                }
                else
                {
                    newNutzerPersonen.RangeFrom = NutzerPersonenFromDate;
                    newNutzerPersonen.RangeTo = NutzerPersonenToDate;
                    nutzerService.AddNutzerPersonen(newNutzerPersonen);
                }
                    
                Nutzer updatedCurrentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                List<NutzerPersonen> updatedNutzerPersonenList = updatedCurrentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();
                InitializeObservCollection(updatedNutzerPersonenList);

                NutzerPersonen currentFirstNutzerPersonen = updatedNutzerPersonenList.FirstOrDefault();
                if (currentFirstNutzerPersonen != null)
                {
                    SelectedNutzerPersonen = currentFirstNutzerPersonen;
                    if (currentFirstNutzerPersonen.RangeTo.HasValue)
                    {
                        IsLastDateNull = true;
                    }
                    else
                    {
                        IsLastDateNull = false;
                    }
                }

                //update list on previous view 
                var previousViewModel = GetParentProperty();
                if(updatedNutzerPersonenList[0].NumberOfPeople.HasValue)
                {
                    previousViewModel.NutzerPersonenNumber = "Personen : " + updatedNutzerPersonenList[0].NumberOfPeople;
                }
                else
                {
                    previousViewModel.NutzerPersonenNumber = "Personen : -";
                }
                previousViewModel.NutzerPersonenFromDate = "Von : " + updatedNutzerPersonenList[0].RangeFrom.ToString("dd.MM.yyyy");
                if (updatedNutzerPersonenList[0].RangeTo.HasValue)
                {
                    previousViewModel.NutzerPersonenToDate = "Bis : " + NutzerPersonenToDate.Value.ToString("dd.MM.yyyy");
                }
                else
                {
                    previousViewModel.NutzerPersonenToDate = "Bis : -";
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to execute AddNutzerPersonenExecute");
                throw;
            }
        }

        private void SaveNutzerPersonenExecute()
        {
            try
            {
                if (SelectedNutzerPersonen != null)
                {
                    //update the entry before it
                    Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                    List<NutzerPersonen> orderedNutzerPersonenList = currentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();
                    int numberOfNutzerPersonenEntries = orderedNutzerPersonenList.Count;

                    NutzerPersonen selectedNutzerPersonen = orderedNutzerPersonenList.Where(x => x.Guid == SelectedNutzerPersonen.Guid).SingleOrDefault();
                    if (selectedNutzerPersonen != null)
                    {
                        int updatedNutzerPersonenIndex = orderedNutzerPersonenList.IndexOf(selectedNutzerPersonen);

                        if (int.TryParse(NutzerPersonenNumber, out int numberOfPeople))
                        {
                            selectedNutzerPersonen.NumberOfPeople = numberOfPeople;
                        }
                        selectedNutzerPersonen.RangeFrom = NutzerPersonenFromDate;
                        selectedNutzerPersonen.RangeTo = NutzerPersonenToDate;
                       
                        nutzerService.UpdateNutzerPersonen(selectedNutzerPersonen);

                        //check if there is previous entry in the table to update
                        if ((updatedNutzerPersonenIndex + 1) >= 0 && (updatedNutzerPersonenIndex + 1) < numberOfNutzerPersonenEntries)
                        {
                            NutzerPersonen previousNutzerPerson = orderedNutzerPersonenList[updatedNutzerPersonenIndex + 1];
                            previousNutzerPerson.RangeTo = selectedNutzerPersonen.RangeFrom.AddDays(-1);
                            nutzerService.UpdateNutzerPersonen(previousNutzerPerson);
                        }
                        //check if there is next entry in the table to update
                        if ((updatedNutzerPersonenIndex - 1) >= 0)
                        {
                            NutzerPersonen nextNutzerPerson = orderedNutzerPersonenList[updatedNutzerPersonenIndex - 1];
                            nextNutzerPerson.RangeFrom = selectedNutzerPersonen.RangeTo.Value.AddDays(1);
                            nutzerService.UpdateNutzerPersonen(nextNutzerPerson);
                        }
                    }
                }
                else
                {
                    //get current nutzer and newest entry in nutzerPersonen and populate UI if there was nothing yet selected from the list
                    Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                    List<NutzerPersonen> orderedNutzerPersonenList = currentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();
                    NutzerPersonen nutzerPerson = orderedNutzerPersonenList.FirstOrDefault();

                    if (nutzerPerson != null)
                    {
                        if (int.TryParse(NutzerPersonenNumber, out int numberOfPeople))
                        {
                            nutzerPerson.NumberOfPeople = numberOfPeople;
                        }
                        
                        int numberOfNutzerPersonenEntries = orderedNutzerPersonenList.Count;
                        int updatedNutzerPersonenIndex = orderedNutzerPersonenList.IndexOf(nutzerPerson);

                        nutzerPerson.RangeFrom = NutzerPersonenFromDate;
                        nutzerPerson.RangeTo = NutzerPersonenToDate;
                        
                        nutzerService.UpdateNutzerPersonen(nutzerPerson);

                        //check if there is next entry in the table to update
                        if ((updatedNutzerPersonenIndex + 1) >= 0 && (updatedNutzerPersonenIndex + 1) < numberOfNutzerPersonenEntries)
                        {
                            NutzerPersonen previousNutzerPerson = orderedNutzerPersonenList[updatedNutzerPersonenIndex + 1];
                            previousNutzerPerson.RangeTo = orderedNutzerPersonenList[updatedNutzerPersonenIndex].RangeFrom.AddDays(-1);
                            nutzerService.UpdateNutzerPersonen(previousNutzerPerson);
                        }
                        //check if there is next entry in the table to update
                        if ((updatedNutzerPersonenIndex - 1) >= 0)
                        {
                            NutzerPersonen nextNutzerPerson = orderedNutzerPersonenList[updatedNutzerPersonenIndex - 1];
                            nextNutzerPerson.RangeFrom = orderedNutzerPersonenList[updatedNutzerPersonenIndex].RangeTo.Value.AddDays(1);
                            nutzerService.UpdateNutzerPersonen(nextNutzerPerson);
                        }
                    }
                    else
                    {
                        NutzerPersonen newNutzerPersonen = new NutzerPersonen();
                        newNutzerPersonen.NutzerGuid = currentNutzer.Guid;
                        if (int.TryParse(NutzerPersonenNumber, out int numberOfPeople))
                        {
                            newNutzerPersonen.NumberOfPeople = numberOfPeople;
                        }
                        newNutzerPersonen.RangeFrom = NutzerPersonenFromDate;
                        newNutzerPersonen.RangeTo = NutzerPersonenToDate;
                        nutzerService.AddNutzerPersonen(newNutzerPersonen);
                    }
                }

                Nutzer updatedCurrentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                List<NutzerPersonen> updatedNutzerPersonenList = updatedCurrentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();
                InitializeObservCollection(updatedNutzerPersonenList);

                NutzerPersonen currentFirstNutzerPersonen = updatedNutzerPersonenList.FirstOrDefault();
                if (currentFirstNutzerPersonen != null)
                {
                    SelectedNutzerPersonen = currentFirstNutzerPersonen;
                    if (currentFirstNutzerPersonen.RangeTo.HasValue)
                    {
                        IsLastDateNull = true;
                    }
                    else
                    {
                        IsLastDateNull = false;
                    }
                }

                //update list on previous view 
                var previousViewModel = GetParentProperty();
                if (updatedNutzerPersonenList[0].NumberOfPeople.HasValue)
                {
                    previousViewModel.NutzerPersonenNumber = "Personen : " + updatedNutzerPersonenList[0].NumberOfPeople;
                }
                else
                {
                    previousViewModel.NutzerPersonenNumber = "Personen : -";
                }
                previousViewModel.NutzerPersonenFromDate = "Von : " + updatedNutzerPersonenList[0].RangeFrom.ToString("dd.MM.yyyy");
                if (updatedNutzerPersonenList[0].RangeTo.HasValue)
                {
                    previousViewModel.NutzerPersonenToDate = "Bis : " + NutzerPersonenToDate.Value.ToString("dd.MM.yyyy");
                }
                else
                {
                    previousViewModel.NutzerPersonenToDate = "Bis : -";
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to execute SaveNutzerPersonenExecute");
                throw;
            }
        }

        private void DeleteNutzerPersonenExecute()
        {
            try
            {
                if (SelectedNutzerPersonen != null)
                {
                    Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                    List<NutzerPersonen> orderedNutzerPersonenList = currentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();
                    int numberOfNutzerPersonenEntries = orderedNutzerPersonenList.Count;

                    NutzerPersonen selectedNutzerPersonen = orderedNutzerPersonenList.Where(x => x.Guid == SelectedNutzerPersonen.Guid).Single();
                    int updatedNutzerPersonenIndex = orderedNutzerPersonenList.IndexOf(selectedNutzerPersonen);

                    if(updatedNutzerPersonenIndex != 0 && (updatedNutzerPersonenIndex + 1) < numberOfNutzerPersonenEntries)
                    {
                        NutzerPersonen previousNutzerPersonen = orderedNutzerPersonenList[updatedNutzerPersonenIndex + 1];
                        previousNutzerPersonen.RangeTo = selectedNutzerPersonen.RangeTo;
                        nutzerService.UpdateNutzerPersonen(previousNutzerPersonen);
                    }

                    nutzerService.DeleteNutzerPersonen(selectedNutzerPersonen);
                }
                else
                {
                    Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                    List<NutzerPersonen> orderedNutzerPersonenList = currentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();
                    NutzerPersonen nutzerPerson = orderedNutzerPersonenList.FirstOrDefault();
                    
                    if (nutzerPerson != null)
                    {
                        int updatedNutzerPersonenIndex = orderedNutzerPersonenList.IndexOf(nutzerPerson);
                        int numberOfNutzerPersonenEntries = orderedNutzerPersonenList.Count;

                        //if (updatedNutzerPersonenIndex == 0 && (updatedNutzerPersonenIndex + 1) < numberOfNutzerPersonenEntries)
                        //{
                        //    NutzerPersonen previousNutzerPersonen = orderedNutzerPersonenList[1];
                        //    previousNutzerPersonen.RangeTo = null;
                        //    nutzerService.UpdateNutzerPersonen(previousNutzerPersonen);
                        //}

                        if (updatedNutzerPersonenIndex != 0 && (updatedNutzerPersonenIndex + 1) < numberOfNutzerPersonenEntries)
                        {
                            NutzerPersonen previousNutzerPersonen = orderedNutzerPersonenList[updatedNutzerPersonenIndex + 1];
                            previousNutzerPersonen.RangeTo = nutzerPerson.RangeTo;
                            nutzerService.UpdateNutzerPersonen(previousNutzerPersonen);
                        }

                        nutzerService.DeleteNutzerPersonen(nutzerPerson);
                    }
                }

                //Update Observable Collection After Deleting
                Nutzer updatedCurrentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                List<NutzerPersonen> updatedNutzerPersonList = updatedCurrentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();
                InitializeObservCollection(updatedNutzerPersonList);

                //update list on previous view 
                NutzerPersonen currentFirstNutzerPersonen = updatedNutzerPersonList.FirstOrDefault();
                if(currentFirstNutzerPersonen != null)
                {
                    SelectedNutzerPersonen = currentFirstNutzerPersonen;
                    if (currentFirstNutzerPersonen.RangeTo.HasValue)
                    {
                        IsLastDateNull = true;
                    }
                    else
                    {
                        IsLastDateNull = false;
                    }
                }
                else
                {
                    NutzerPersonenNumber = "0";
                    NutzerPersonenFromDate = updatedCurrentNutzer.MoveInDate;
                    NutzerPersonenToDate = null;
                    CanNutzerPersonenBeDeleted = false;
                }
                
                var previousViewModel = GetParentProperty();
                if (currentFirstNutzerPersonen != null)
                {
                    if (currentFirstNutzerPersonen.NumberOfPeople.HasValue)
                    {
                        previousViewModel.NutzerPersonenNumber = "Personen : " + currentFirstNutzerPersonen.NumberOfPeople.Value.ToString();
                    }
                    else
                    {
                        previousViewModel.NutzerPersonenNumber = "Personen : -";
                    }
                    previousViewModel.NutzerPersonenFromDate = "Von : " + currentFirstNutzerPersonen.RangeFrom.ToString("dd.MM.yyyy");
                    if (currentFirstNutzerPersonen.RangeTo.HasValue)
                    {
                        previousViewModel.NutzerPersonenToDate = "Bis : " + currentFirstNutzerPersonen.RangeTo.Value.ToString("dd.MM.yyyy");
                    }
                    else
                    {
                        previousViewModel.NutzerPersonenToDate = "Bis : -";
                    }
                }
                else
                {
                    previousViewModel.NutzerPersonenNumber = "Personen : -";
                    previousViewModel.NutzerPersonenFromDate = "Von : ";
                    previousViewModel.NutzerPersonenToDate = "Bis : -";
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to execute DeleteNutzerPersonenExecute");
                throw;
            }
        }

        private void NewEmptyNutzerPersonenExecute()
        {
            try
            {
                //currentNutzer that we are currently inside
                Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                NutzerPersonen newNutzerPersonen = new NutzerPersonen
                {
                    NutzerGuid = currentNutzer.Guid,
                    IsCreatedByApp = true
                };

                List<NutzerPersonen> currentNutzerPersonenList = currentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();

                NutzerPersonen currentNutzerPersonen = currentNutzerPersonenList[0];
                newNutzerPersonen.NumberOfPeople = currentNutzerPersonen.NumberOfPeople;
                newNutzerPersonen.RangeFrom = currentNutzerPersonen.RangeTo.Value.AddDays(1);
                newNutzerPersonen.RangeTo = null;
                nutzerService.AddNutzerPersonen(newNutzerPersonen);
                
                Nutzer updatedCurrentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                List<NutzerPersonen> updatedNutzerPersonenList = updatedCurrentNutzer.NutzerPersonen.OrderByDescending(x => x.RangeFrom).ToList();
                InitializeObservCollection(updatedNutzerPersonenList);

                NutzerPersonen currentFirstNutzerPersonen = updatedNutzerPersonenList.FirstOrDefault();
                if (currentFirstNutzerPersonen != null)
                {
                    SelectedNutzerPersonen = currentFirstNutzerPersonen;
                    if (currentFirstNutzerPersonen.RangeTo.HasValue)
                    {
                        IsLastDateNull = true;
                    }
                    else
                    {
                        IsLastDateNull = false;
                    }
                }

                //update list on previous view 
                var previousViewModel = GetParentProperty();
                if (updatedNutzerPersonenList[0].NumberOfPeople.HasValue)
                {
                    previousViewModel.NutzerPersonenNumber = "Personen : " + updatedNutzerPersonenList[0].NumberOfPeople;
                }
                else
                {
                    previousViewModel.NutzerPersonenNumber = "Personen : -";
                }
                previousViewModel.NutzerPersonenFromDate = "Von : " + updatedNutzerPersonenList[0].RangeFrom.ToString("dd.MM.yyyy");
                if (updatedNutzerPersonenList[0].RangeTo.HasValue)
                {
                    previousViewModel.NutzerPersonenToDate = "Bis : " + NutzerPersonenToDate.Value.ToString("dd.MM.yyyy");
                }
                else
                {
                    previousViewModel.NutzerPersonenToDate = "Bis : -";
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to execute NewEmptyNutzerPersonenExecute");
                throw;
            }
        }
    }
}
