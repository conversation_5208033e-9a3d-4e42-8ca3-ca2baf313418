﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="CreateNutzeinheitPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq;
    using System.Threading.Tasks;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.Models;
    using Eras2AmwApp.Validators;
    using Eras2AmwApp.WebService.EventArgs;
    using Eras2AmwApp.WebService.Interfaces;
    using FluentValidation.Results;
    using Syncfusion.SfBusyIndicator.XForms;
    using Xamarin.Essentials;
    using Xamarin.Forms;
    using domain = Domain.Eras2Amw.Models;
    using INutzeinheitService = BusinessLogic.Interfaces.INutzeinheitService;

    public class CreateNutzeinheitPageViewModel : EcViewModelBase, ISupportParentViewModel
    {
        #region fields

        private readonly INutzeinheitService nutzeinheitService;
        private readonly IAddressService addressService;
        private readonly IPersonService personService;

        private Command saveNutzeinheitCommand;

        private EcViewModelBase _parentViewModel;

        private string _nutzeinheitNummer;
        private string _nutzeinheitLage;
        private string _nutzeinheitWalkSequence;
        private string _nutzeinheitNote;

        private string _addressStreet;
        private string _addressStreet2;
        private string _addressStreetNumber;
        private string _addressZipCode;
        private string _addressCity;
        private string _addressMailbox;
        private string _addressAdditional;
        private string _addressLatitude;
        private string _addressLongitude;

        private string _nutzeinheitNumberErrorText;
        private string _nutzeinheitLocationErrorText;

        private bool _nutzeinheitNumberHasError;
        private bool _nutzeinheitLocationHasError;

        private Abrechnungseinheit abrechnungseinheit;
        private Appointment appointment;

        #endregion

        #region ctor

        public CreateNutzeinheitPageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            INutzeinheitService nutzeinheitService,
            IAddressService addressService,
            IPersonService personService)
        : base(serviceLocator, navigationService)
        {
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.addressService = addressService ?? throw new ArgumentNullException(nameof(addressService));
            this.personService = personService ?? throw new ArgumentNullException(nameof(personService));
        }

        #endregion

        #region Commands

        public Command SaveNutzeinheitCommand => saveNutzeinheitCommand ?? (saveNutzeinheitCommand = new Command(SaveNutzeinheitExecute));

        #endregion

        #region properties

        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { Set(ref _parentViewModel, value); }
        }

        public string NutzeinheitNumber
        {
            get { return _nutzeinheitNummer; }
            set { Set(ref _nutzeinheitNummer, value); }
        }

        public string NutzeinheitNumberErrorText
        {
            get { return _nutzeinheitNumberErrorText; }
            set { Set(ref _nutzeinheitNumberErrorText, value); }
        }

        public bool NutzeinheitNumberHasError
        {
            get { return _nutzeinheitNumberHasError; }
            set { Set(ref _nutzeinheitNumberHasError, value); }
        }

        public string NutzeinheitLocation
        {
            get { return _nutzeinheitLage; }
            set { Set(ref _nutzeinheitLage, value); }
        }

        public string NutzeinheitLocationErrorText
        {
            get { return _nutzeinheitLocationErrorText; }
            set { Set(ref _nutzeinheitLocationErrorText, value); }
        }

        public bool NutzeinheitLocationHasError
        {
            get { return _nutzeinheitLocationHasError; }
            set { Set(ref _nutzeinheitLocationHasError, value); }
        }

        public string NutzeinheitWalkSequence
        {
            get { return _nutzeinheitWalkSequence; }
            set { Set(ref _nutzeinheitWalkSequence, value); }
        }

        public string NutzeinheitNote
        {
            get { return _nutzeinheitNote; }
            set { Set(ref _nutzeinheitNote, value); }
        }

        public string AddressStreet
        {
            get { return _addressStreet; }
            set { Set(ref _addressStreet, value); }
        }

        public string AddressStreet2
        {
            get { return _addressStreet2; }
            set { Set(ref _addressStreet2, value); }
        }

        public string AddressStreetNumber
        {
            get { return _addressStreetNumber; }
            set { Set(ref _addressStreetNumber, value); }
        }

        public string AddressZipCode
        {
            get { return _addressZipCode; }
            set { Set(ref _addressZipCode, value); }
        }

        public string AddressCity
        {
            get { return _addressCity; }
            set { Set(ref _addressCity, value); }
        }

        public string AddressMailbox
        {
            get { return _addressMailbox; }
            set { Set(ref _addressMailbox, value); }
        }

        public string AddressAdditional
        {
            get { return _addressAdditional; }
            set { Set(ref _addressAdditional, value); }
        }

        public string AddressLatitude
        {
            get { return _addressLatitude; }
            set { Set(ref _addressLatitude, value); }
        }

        public string AddressLongitude
        {
            get { return _addressLongitude; }
            set { Set(ref _addressLongitude, value); }
        }

        public Guid AbrechnungseinheitGuid { get; set; }

        #endregion

        #region public methods

        public OrderPageViewModel GetParentProperty()
        {
            return ParentViewModel as OrderPageViewModel;
        }

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is OrderPageViewModel parentVm)
            {
                ParentViewModel = parentVm;
                appointment = parentVm.SelectedAppointment;
                abrechnungseinheit = parentVm.SelectedAppointment.Order.Abrechnungseinheit;
                AbrechnungseinheitGuid = abrechnungseinheit.Guid;
                InitializeProperties();
                InitNutzeinheitNumberPlaceholder();
            }
            return base.SetupAsync(navigationData);
        }

        #endregion

        #region private methods

        private void InitializeProperties()
        {
            try
            {
                Address address = abrechnungseinheit.Address;
                AddressStreet = address.Street;
                AddressStreet2 = address.Street2;
                AddressStreetNumber = address.StreetNumber;
                AddressZipCode = address.Zipcode;
                AddressCity = address.City;
                AddressMailbox = address.Mailbox;
                AddressAdditional = address.Additional;
                AddressLatitude = address.Latitude;
                AddressLongitude = address.Longitude;
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while initializing properties in CreateNutzeinheitPageVM!");
                throw;
            }
        }

        private void InitNutzeinheitNumberPlaceholder()
        {
            try
            {
                NutzeinheitNumber = nutzeinheitService.GetPlaceholderNutzeinheitNumber(AbrechnungseinheitGuid);
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to initialize placeholder value for new Nutzeinheit Number!");
                throw;
            }
        }

        private async void SaveNutzeinheitExecute()
        {
            try
            {
                if (!Validate())
                {
                    return;
                }

                Nutzeinheit nutzeinheit = CreateNutzeinheit();
                nutzeinheit.AppointmentNutzeinheiten = new List<AppointmentNutzeinheit>
                {
                    CreateAppointmentNutzeinheit(nutzeinheit)
                };

                nutzeinheitService.SaveNutzeinheit(nutzeinheit);
                NutzeinheitOrderState nutzeinheitOrderState = nutzeinheit.OrderStates.Single();

                //update previous model list 
                OrderPageViewModel previousViewModel = GetParentProperty();
                ObservableCollection<NutzeinheitVM> appointmentNutzeinheiten = previousViewModel.AppointmentNutzeinheiten;
                NutzeinheitVM newNutzeinheiten = previousViewModel.CreateNutzeinheitVmObject(nutzeinheitService.GetNutzeinheit(nutzeinheit.Guid), appointment);
                appointmentNutzeinheiten.Add(newNutzeinheiten);

                await navigationService.GoBackAsync();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to save new Nutzeinheit!");
                throw;
            }
        }

        private Guid GetCreateAddressGuid()
        {
            Address address = new Address()
            {
                Street = AddressStreet,
                Street2 = AddressStreet2,
                StreetNumber = AddressStreetNumber,
                Zipcode = AddressZipCode,
                City = AddressCity,
                Mailbox = AddressMailbox,
                Additional = AddressAdditional,
                Latitude = AddressLatitude,
                Longitude = AddressLongitude,
            };
            return addressService.AddNutzeinheitAddress(address);
        }

        private Nutzeinheit CreateNutzeinheit()
        {
            return new Nutzeinheit()
            {
                Number = NutzeinheitNumber,
                Location = NutzeinheitLocation,
                WalkSequence = NutzeinheitWalkSequence,
                Note = NutzeinheitNote,
                AbrechnungseinheitGuid = abrechnungseinheit.Guid,
                AddressGuid = GetCreateAddressGuid(),
                Nutzer = new List<Nutzer>()
                {
                    CreateLeerstandNutzerToNutzeinheit()
                },
                Devices = new List<domain.Device>(),
                Photos = new List<Photo>(),
                Signatures = new List<Signature>(),
                NutzeinheitOrderPositions = new List<NutzeinheitOrderPosition>()
                {
                    new NutzeinheitOrderPosition()
                    {
                        OrderPositionGuid = appointment.Order.OrderPositions.First().Guid
                    }
                },
                OrderStates = new List<NutzeinheitOrderState>()
                {
                    CreateNutzeinheitOrderState()
                },
                IsCreatedByApp = true
            };
        }

        private AppointmentNutzeinheit CreateAppointmentNutzeinheit(Nutzeinheit nutzeinheit)
        {
            var appointmentNutzeinheit = new AppointmentNutzeinheit
            {
                AppointmentGuid = appointment.Guid,
                NutzeinheitGuid = nutzeinheit.Guid
            };

            return appointmentNutzeinheit;
        }

        private Nutzer CreateLeerstandNutzerToNutzeinheit()
        {
            var defaultTime = default(DateTime);
            return new Nutzer()
            {
                Kind = NutzerKind.Leerstand,
                MoveInDate = defaultTime,
                TitleId = null,
                SalutationId = 6,
                Name1 = "Leerstand",
                NutzerCommunications = new List<NutzerCommunication>()
                {
                     CreateNutzerCommunication()
                }
            };
        }

        private NutzerCommunication CreateNutzerCommunication()
        {
            CommunicationFeature communicationFeature = personService.GetCommunicationFeature(CommunicationKind.Mobil);
            return new NutzerCommunication()
            {
                Address = string.Empty,
                Note = string.Empty,
                CommunicationFeatureGuid = communicationFeature.Guid
            };
        }

        private NutzeinheitOrderState CreateNutzeinheitOrderState()
        {
            return new NutzeinheitOrderState()
            {
                OrderGuid = appointment.Order.Guid,
                OrderKinds = new List<NutzeinheitOrderKind>()
                {
                    NutzeinheitOrderKind.Assembly
                },
                ProcessState = ProcessState.Creating
            };
        }

        private bool Validate()
        {
            var validator = new CreateNutzeinheitPageViewModelValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }
    }
    #endregion
    
}
