﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceMaintenanceDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Implementations
{
    using Eras2AmwApp.Interfaces;
    using Syncfusion.XForms.PopupLayout;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using System;
    using System.Collections.Generic;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System.Linq;
    using Device = Domain.Eras2Amw.Models.Device;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;

    public class DeviceMaintenanceDialog : IDeviceMaintenanceDialog
    {
        #region fields

        private readonly IStammdatenService stammdatenService;
        private readonly IDeviceService deviceService;

        private TaskCompletionSource<DeviceOrderState> source;

        private Command acceptButtonCommand;
        private Command declineButtonCommand;

        private DeviceOrderState deviceOrderState;

        private Grid grid;
        private Label label;
        private DataTemplate headerTempleteView;
        private SfPopupLayout popupLayout;
        private ListView listView;
        private List<AmwInfoKey> infoKeyList;

        #endregion

        #region Commands

        public Command AcceptButtonCommand => acceptButtonCommand ?? (acceptButtonCommand = new Command(AcceptButtonExecuted));

        public Command DeclineButtonCommand => declineButtonCommand ?? (declineButtonCommand = new Command(DeclineButtonExecuted));

        #endregion

        public DeviceMaintenanceDialog(IStammdatenService stammdatenService, IDeviceService deviceService)
        {
            this.stammdatenService = stammdatenService ?? throw new ArgumentNullException(nameof(stammdatenService));
            this.deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
        }

        public Task<DeviceOrderState> ShowMaintenanceDialog(IDevice device)
        {
            string message = "Auftrag nicht ausgeführt weil:";

            return AssignMaintenance(message, device);
        }

        private Task<DeviceOrderState> AssignMaintenance(string message, IDevice device)
        {
            source = new TaskCompletionSource<DeviceOrderState>();

            deviceOrderState = device.DeviceOrderState;

            label = CreateLabel(message);
            listView = CreateListView(device);

            CreateGrid();
            grid.Children.Add(label,0,0);
            grid.Children.Add(listView, 0, 1);

            headerTempleteView = new DataTemplate(() =>
            {
                return CreateHeader();
            });

            popupLayout = new SfPopupLayout
            {
                PopupView =
                {
                    ShowCloseButton = false,
                    HeaderTemplate = headerTempleteView,
                    ContentTemplate = new DataTemplate(() => grid),
                    WidthRequest = 300,
                    AppearanceMode = AppearanceMode.TwoButton,
                    ShowFooter = true,
                    AcceptButtonText="OK",
                    DeclineButtonText="Abbrechen",
                    AcceptCommand = AcceptButtonCommand,
                    DeclineCommand = DeclineButtonCommand
                }
            };

            popupLayout.Show();

            return source.Task;
        }

        private ListView CreateListView(IDevice device)
        {
            Device domainDevice = deviceService.GetDevice(device.DeviceGuid);
            DeviceClass deviceClass = domainDevice.DeviceCatalog.DeviceKind.Class;

            if (deviceClass == DeviceClass.RM)
            {
                infoKeyList = stammdatenService.GetRauchmelderInfoKey();
            }
            else if (deviceClass == DeviceClass.KWZ || deviceClass == DeviceClass.WWZ || deviceClass == DeviceClass.WMZ || deviceClass == DeviceClass.SZ)
            {
                infoKeyList = stammdatenService.GetWasserzählerInfoKey();
            }
            else if (deviceClass == DeviceClass.HKV)
            {
                infoKeyList = stammdatenService.GetHkvInfoKey();
            }

            AmwInfoKey emptyKey = new AmwInfoKey()
            {
                Key = -1,
                Info = "<Infoschlüssel löschen>"
            };
            infoKeyList.Insert(0, emptyKey);

            List<string> stringList = new List<string>();
            foreach (AmwInfoKey amwInfoKey in infoKeyList)
            {
                stringList.Add(amwInfoKey.Info);
            };

            string selectedKeyInfo = infoKeyList[0].Info;
            AmwInfoKey selectedKey = infoKeyList.FirstOrDefault(x => x.Guid == deviceOrderState.AmwInfoKeyGuid);
            if (selectedKey != null)
            {
                selectedKeyInfo = selectedKey.Info;
            }

            listView = new ListView()
            {
                ItemsSource = stringList,
                SelectedItem = selectedKeyInfo,
                SelectionMode = ListViewSelectionMode.Single,
                HasUnevenRows = true,
                IsPullToRefreshEnabled = false
            };

            return listView;
        }

        private void CreateGrid()
        {
            grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Auto) });

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(280) });
        }

        private Label CreateHeader()
        {
            Label headerContent = new Label
            {
                Padding = 0,
                Text = "Infoschlüssel Gerät",
                FontAttributes = FontAttributes.Bold,
                TextColor = Color.White,
                BackgroundColor = Color.FromHex("#538EEC"),
                FontSize = 20,
                HorizontalTextAlignment = TextAlignment.Center,
                VerticalTextAlignment = TextAlignment.Center
            };
            return headerContent;
        }

        private Label CreateLabel(string message)
        {
            var label = new Label
            {
                Margin = 5,
                BackgroundColor = Color.White,
                HorizontalOptions = LayoutOptions.CenterAndExpand,
                VerticalOptions = LayoutOptions.CenterAndExpand,
                FontSize = 20,
                TextColor = Color.Black,
                Text = message
            };

            return label;
        }

        private void AcceptButtonExecuted()
        {
            if (listView.SelectedItem is string infoKey)
            {
                AmwInfoKey amwInfoKey = infoKeyList.FirstOrDefault(x => x.Info == infoKey);

                if (amwInfoKey.Key == -1)
                {
                    deviceOrderState.AmwInfoKeyGuid = null;
                    deviceOrderState.AmwInfoKey = null;
                    if (deviceOrderState.ProcessState != ProcessState.Creating)
                    {
                        deviceOrderState.ProcessState = ProcessState.InProgress;
                    }
                }
                else
                {
                    deviceOrderState.AmwInfoKey = amwInfoKey;
                    deviceOrderState.AmwInfoKeyGuid = amwInfoKey.Guid;
                    if(deviceOrderState.ProcessState != ProcessState.Creating)
                    {
                        deviceOrderState.ProcessState = ProcessState.Updating;
                        deviceOrderState.CompletedDate = DateTime.Now;
                    }
                }
            }

            source.TrySetResult(deviceOrderState);
        }

        private void DeclineButtonExecuted()
        {
            source.TrySetResult(deviceOrderState);
        }
    }
}
