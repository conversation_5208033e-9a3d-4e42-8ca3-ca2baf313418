﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:inputLayout="clr-namespace:Syncfusion.XForms.TextInputLayout;assembly=Syncfusion.Core.XForms"
             xmlns:converter="clr-namespace:Eras2AmwApp.Converter"
             xmlns:behaviors="clr-namespace:Eras2AmwApp.Behaviors;assembly=Eras2AmwApp"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.CreateNutzeinheitPage">

    <ContentPage.Resources>

        <converter:NutzerKindConverter x:Key="NutzerKindConverter"></converter:NutzerKindConverter>

    </ContentPage.Resources>

    <ContentPage.Content>

        <Grid RowSpacing="0">
            
            <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="50*"></ColumnDefinition>
                <ColumnDefinition Width="50*"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <Label  x:Name="Nutzeinheit"
                    Grid.Row="0"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    Text="{markupExtensions:Localisation NewNe}"
                    FontSize="Large"
                    FontAttributes="Bold"
                    HorizontalOptions="Center"
                    TextColor="Black">
            </Label>

            <Frame  x:Name="NutzeinheitFrame"
                    Margin="5,5,5,5"
                    Padding="5"
                    Grid.Row="1"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    BorderColor="LightGray"
                    CornerRadius="0"
                    HasShadow="True">

                <Grid>

                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="50*"></ColumnDefinition>
                        <ColumnDefinition Width="50*"></ColumnDefinition>
                    </Grid.ColumnDefinitions>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="Nummer:"
                                                    ContainerType="Outlined"
                                                    Grid.Row="0"
                                                    Grid.Column="0"
                                                    VerticalOptions="Center"
                                                    HasError="{Binding NutzeinheitNumberHasError}"
                                                    ErrorText="{Binding NutzeinheitNumberErrorText}">

                        <Entry  Text="{Binding NutzeinheitNumber}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="{markupExtensions:Localisation Location}"
                                                    ContainerType="Outlined"
                                                    Grid.Row="0"
                                                    Grid.Column="1"
                                                    VerticalOptions="Center"
                                                    HasError="{Binding NutzeinheitLocationHasError}"
                                                    ErrorText="{Binding NutzeinheitLocationErrorText}">

                        <Entry  Text="{Binding NutzeinheitLocation}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="{markupExtensions:Localisation WalkSequence}"
                                                    ContainerType="Outlined"
                                                    Grid.Row="1"
                                                    Grid.Column="0"
                                                    VerticalOptions="Center">

                        <Entry  Text="{Binding NutzeinheitWalkSequence}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="Notiz:"
                                                    ContainerType="Outlined"
                                                    Grid.Row="2"
                                                    Grid.Column="0"
                                                    Grid.ColumnSpan="2"
                                                    VerticalOptions="Center">

                        <Entry  Text="{Binding NutzeinheitNote}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                </Grid>

            </Frame>

            <Label  x:Name="Address"
                    Grid.Row="2"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    Text="{markupExtensions:Localisation Address}"
                    FontSize="Large"
                    FontAttributes="Bold"
                    HorizontalOptions="Center"
                    TextColor="Black">
            </Label>

            <Frame  x:Name="AddressFrame"
                    Margin="5,5,5,5"
                    Padding="5"
                    Grid.Row="3"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    BorderColor="LightGray"
                    CornerRadius="0"
                    HasShadow="True">

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="50*"></ColumnDefinition>
                        <ColumnDefinition Width="50*"></ColumnDefinition>
                    </Grid.ColumnDefinitions>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="{markupExtensions:Localisation Street}"
                                                    ContainerType="Outlined"
                                                    Grid.Row="0"
                                                    Grid.Column="0"
                                                    VerticalOptions="Center"
                                                    IsEnabled="True">

                        <Entry  Text="{Binding AddressStreet}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="Straße2"
                                                    ContainerType="Outlined"
                                                    Grid.Row="0"
                                                    Grid.Column="1"
                                                    VerticalOptions="Center"
                                                    IsEnabled="True">

                        <Entry  Text="{Binding AddressStreet2}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="{markupExtensions:Localisation HouseNumber}"
                                                    ContainerType="Outlined"
                                                    Grid.Row="1"
                                                    Grid.Column="0"
                                                    VerticalOptions="Center">

                        <Entry  Text="{Binding AddressStreetNumber}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="{markupExtensions:Localisation PostCode}"
                                                    ContainerType="Outlined"
                                                    Grid.Row="2"
                                                    Grid.Column="0"
                                                    VerticalOptions="Center"
                                                    IsEnabled="False">

                        <Entry  Text="{Binding AddressZipCode}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="{markupExtensions:Localisation City}"
                                                    ContainerType="Outlined"
                                                    Grid.Row="2"
                                                    Grid.Column="1"
                                                    VerticalOptions="Center"
                                                    IsEnabled="False">

                        <Entry  Text="{Binding AddressCity}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="{markupExtensions:Localisation Mailbox}"
                                                    ContainerType="Outlined"
                                                    Grid.Row="3"
                                                    Grid.Column="0"
                                                    VerticalOptions="Center">

                        <Entry  Text="{Binding AddressMailbox}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                    <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                                    Margin="0"
                                                    Hint="{markupExtensions:Localisation AddressAdditional}"
                                                    ContainerType="Outlined"
                                                    Grid.Row="3"
                                                    Grid.Column="1"
                                                    VerticalOptions="Center">

                        <Entry  Text="{Binding AddressAdditional}"
                                VerticalOptions="End"
                                FontSize="Medium">

                        </Entry>

                    </inputLayout:SfTextInputLayout>

                </Grid>

            </Frame>

            <ImageButton    x:Name="SaveButton"
                            Source="saveIcon.png"
                            BackgroundColor="Transparent"
                            Grid.Row="4"
                            Grid.Column="1"
                            HorizontalOptions="End"
                            Margin="0"
                            WidthRequest="70"
                            HeightRequest="70"
                            Command="{Binding SaveNutzeinheitCommand}">
            </ImageButton>

        </Grid>

    </ContentPage.Content>
    
</ContentPage>