﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="CreateNutzeinheitPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using System.Linq;
    using Eras2AmwApp.Common.Ioc;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using FluentValidation;
    using ViewModels;

    public class CreateNutzeinheitPageViewModelValidator : AbstractValidator<CreateNutzeinheitPageViewModel>
    {
        private readonly IDbContextFactory contextFactory = NinjectKernel.Get<IDbContextFactory>();

        public CreateNutzeinheitPageViewModelValidator()
        {
            RuleFor(x => x.NutzeinheitNumber)
                .Must(ValidateNutzeinheitNumber)
                .WithMessage("Die Nutzeinheit Nummer existiert bereits in dieser Abrechnungseinheit und kann nicht noch einmal vergeben werden.")
                .NotEmpty().WithMessage("Die Nummer darf nicht leer sein.");

            RuleFor(x => x.NutzeinheitLocation)
                .NotEmpty().WithMessage("Die Lage darf nicht leer sein.");
        }

        private bool ValidateNutzeinheitNumber(CreateNutzeinheitPageViewModel abrechnungseinheit, string nutzeinheitNumber)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return !context.Nutzeinheiten.Any(y => y.Number == nutzeinheitNumber && y.AbrechnungseinheitGuid == abrechnungseinheit.AbrechnungseinheitGuid);
            }
        }
    }
}
