﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Bootstrapper.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Bootstrap
{
    using System;
    using System.Threading.Tasks;
    using Common.Interfaces;
    using Common.Ioc;
    using Eras2AmwApp.Database.Interfaces;
    using Interfaces;

    using Serilog;
    
    public class Bootstrapper
    {
        private readonly IServiceLocator serviceLocator;

        private readonly IEcNavigationService navigationService;

        private readonly IAppSettings appSettings;

        private readonly ILogger logger;

        private readonly IDbContextFactory databaseFactory;

        public Bootstrapper(IServiceLocator serviceLocator)
        {
            this.serviceLocator = serviceLocator ?? throw new ArgumentNullException(nameof(serviceLocator));
            appSettings = serviceLocator.AppSettings;
            logger = serviceLocator.Logger;
            navigationService = NinjectKernel.Get<IEcNavigationService>();
            databaseFactory = NinjectKernel.Get<IDbContextFactory>();
        }

        public void Setup()
        {
            SetupSyncfusion();
            CreateFileSystem();
            CreateDatabases();
            SetupUi();
            SetupUnhandledExceptions();
        }

        private void TaskSchedulerOnUnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs unobservedTaskExceptionEventArgs)
        {
            AggregateException exp = unobservedTaskExceptionEventArgs.Exception;

            logger.Error(exp, "Unhandled UnobservedTaskException");
        }

        private void CurrentDomainOnUnhandledException(object sender, UnhandledExceptionEventArgs unhandledExceptionEventArgs)
        {
            var exp = unhandledExceptionEventArgs.ExceptionObject as Exception;

            logger.Error(unhandledExceptionEventArgs.ExceptionObject as Exception, "UnhandledException");
        }

        private void SetupSyncfusion()
        {
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(appSettings.SyncfusionLicenceKey);
        }

        private void CreateFileSystem()
        {
            new FileSystemCreator(appSettings, logger).Start();
        }

        private void CreateDatabases()
        {
            new DatabasesCreator(logger).Start();
        }

        private void SetupUi()
        {
            new UiSetup(navigationService, logger, databaseFactory).Start();
        }

        private void SetupUnhandledExceptions()
        {
            AppDomain.CurrentDomain.UnhandledException += CurrentDomainOnUnhandledException;
            TaskScheduler.UnobservedTaskException += TaskSchedulerOnUnobservedTaskException; 
        }
    }
}