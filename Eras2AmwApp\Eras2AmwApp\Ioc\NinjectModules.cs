﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NinjectModules.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Ioc
{
    using AutoMapper;
    using Eras2AmwApp.Implementations;
    using Interfaces;
    using Ninject;
    using Ninject.Modules;
    using Services;
    using WebService.Mapper;

    public class NinjectModules : NinjectModule
    {
        public override void Load()
        {
            Bind<IEcNavigationService>().To<NavigationService>().InSingletonScope();
            Bind<IEcDialogService>().To<EcDialogService>();
            Bind<IDeviceMaintenanceDialog>().To<DeviceMaintenanceDialog>();
            Bind<IScannerService>().To<ScannerService>();
            Bind<INewAppointmentDialog>().To<NewAppointmentDialog>();
            Bind<IAppointmentNutzeinheitDetailsDialog>().To<AppointmentNutzeinheitDetailsDialog>();
            Bind<INutzeinheitSignatureDialog>().To<NutzeinheitSignatureDialog>();
            Bind<IDeviceOrderKindChangeDialog>().To<DeviceOrderKindChangeDialog>();

            MapperConfiguration mapperConfiguration = CreateConfiguration();
            Bind<MapperConfiguration>().ToConstant(mapperConfiguration).InSingletonScope();

            Bind<IMapper>().ToMethod(ctx =>
                new Mapper(mapperConfiguration, type => ctx.Kernel.Get(type)));
        }

        private MapperConfiguration CreateConfiguration()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<StammdatenProfile>();
                cfg.AddProfile<OrdersProfile>();
            });

            return config;
        }
    }
}