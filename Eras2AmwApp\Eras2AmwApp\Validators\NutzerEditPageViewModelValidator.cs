﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerEditPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using FluentValidation;
    using ViewModels;

    public class NutzerEditPageViewModelValidator : AbstractValidator<NutzerEditPageViewModel>
    {
        public NutzerEditPageViewModelValidator()
        {
            RuleFor(x => x.NutzerSalutation)
                .NotEmpty().WithMessage("Die Anrede darf nicht leer sein.");

            RuleFor(x => x.SelectedNutzerKind)
                .NotEmpty().WithMessage("Die NutzerKind darf nicht leer sein.");
        }
    }
}
