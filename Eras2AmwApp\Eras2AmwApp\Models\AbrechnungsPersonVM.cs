﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Models
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;

    public class AbrechnungsPersonVM
    {
        public Guid PersonAEGuid { get; set; }

        public Guid PersonGuid { get; set; }

        public string PersonFirstname { get; set; }

        public string PersonLastname { get; set; }

        public Salutation PersonSalutation { get; set; }

        public Title PersonTitle { get; set; }

        public string PersonNote { get; set; }

        public string PersonPosition { get; set; }

        public string PersonCommunicationTyp { get; set; }

        public string PersonCommunicationValue { get; set; }

        public Dictionary<CommunicationKind, string> PersonCommunicationDetails { get; set; }
    }
}
