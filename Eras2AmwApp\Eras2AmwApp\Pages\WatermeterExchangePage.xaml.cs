﻿namespace Eras2AmwApp.Pages
{
    using Eras2AmwApp.ViewModels;
    using System;
    using Xamarin.Forms;
    using Xamarin.Forms.Xaml;

    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class WatermeterExchangePage : ContentPage
    {
        public static readonly BindableProperty EnableBackButtonOverrideProperty =
          BindableProperty.Create(
              nameof(EnableBackButtonOverride),
              typeof(bool),
              typeof(WatermeterExchangePage),
              true);

        private readonly WatermeterExchangePageViewModel watermeterExchangeVM;

        public bool EnableBackButtonOverride
        {
            get => (bool)GetValue(EnableBackButtonOverrideProperty);
            set => SetValue(EnableBackButtonOverrideProperty, value);
        }

        public WatermeterExchangePage(WatermeterExchangePageViewModel viewModel)
        {
            InitializeComponent();
            BindingContext = watermeterExchangeVM = viewModel;
            viewModel.TabView = pageTabView;
        }

        public Action CustomBackButtonAction => async () => await watermeterExchangeVM.DeselectListViewItemOnBackButton();

        protected override bool OnBackButtonPressed()
        {
            DeselectListViewItem();

            return true;
        }

        private void DeselectListViewItem()
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                await watermeterExchangeVM.DeselectListViewItemOnBackButton();
            });
        }
    }
}