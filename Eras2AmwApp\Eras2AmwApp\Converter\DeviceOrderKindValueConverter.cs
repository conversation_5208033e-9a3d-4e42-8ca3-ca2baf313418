﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceOrderKindValueConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class DeviceOrderKindValueConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            DeviceOrderKind orderKind = (DeviceOrderKind)value;
            string orderKindText;

            switch (orderKind)
            {
                case DeviceOrderKind.Assembly:
                    orderKindText = "Assembly";
                    break;
                case DeviceOrderKind.Maintenance:
                    orderKindText = "Maintenance";
                    break;
                case DeviceOrderKind.Exchange:
                    orderKindText = "Exchange";
                    break;
                case DeviceOrderKind.Reading:
                    orderKindText = "Reading";
                    break;
                case DeviceOrderKind.Inspection:
                    orderKindText = "Inspection";
                    break;
                case DeviceOrderKind.MainReading:
                    orderKindText = "MainReading";
                    break;
                default:
                    orderKindText = "Unknown";
                    break;
            }

            return orderKindText;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
