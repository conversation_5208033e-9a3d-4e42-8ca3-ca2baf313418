﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Models
{
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.ViewModels;
    using GalaSoft.MvvmLight;
    using System;

    public class NutzeinheitVM : ViewModelBase, ISupportParentViewModel
    {
        private EcViewModelBase _parentViewModel;
        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { Set(ref _parentViewModel, value); }
        }

        private Guid _nutzeinheitGuid;
        public Guid NutzeinheitGuid
        {
            get { return _nutzeinheitGuid; }
            set { Set(ref _nutzeinheitGuid, value); }
        }

        private Guid _nutzerGuid;
        public Guid NutzerGuid
        {
            get { return _nutzerGuid; }
            set { Set(ref _nutzerGuid, value); }
        }

        private NutzeinheitOrder _nutzeinheitOrder;
        public NutzeinheitOrder NutzeinheitOrder
        {
            get { return _nutzeinheitOrder; }
            set { Set(ref _nutzeinheitOrder, value); }
        }

        private DateTime _appointmentDate;
        public DateTime AppointmentDate
        {
            get { return _appointmentDate; }
            set { Set(ref _appointmentDate, value); }
        }

        private string _nutzerNameLocation;
        public string NutzerNameLocation
        {
            get { return _nutzerNameLocation; }
            set { Set(ref _nutzerNameLocation, value); }
        }

        private NutzerKind _nutzerKind;
        public NutzerKind NutzerKind
        {
            get { return _nutzerKind; }
            set { Set(ref _nutzerKind, value); }
        }

        private string _nutzerContact;
        public string NutzerContact
        {
            get { return _nutzerContact; }
            set { Set(ref _nutzerContact, value); }
        }

        private string _nutzeinheitNumber;
        public string NutzeinheitNumber
        {
            get { return _nutzeinheitNumber; }
            set { Set(ref _nutzeinheitNumber, value); }
        }

        private string _nutzeinheitAddress;
        public string NutzeinheitAddress
        {
            get { return _nutzeinheitAddress; }
            set { Set(ref _nutzeinheitAddress, value); }
        }

        private string _nutzeinheitNote;
        public string NutzeinheitNote
        {
            get { return _nutzeinheitNote; }
            set { Set(ref _nutzeinheitNote, value); }
        }

        private bool _hasNewAppointment;
        public bool HasNewAppointment
        {
            get { return _hasNewAppointment; }
            set { Set(ref _hasNewAppointment, value); }
        }

        private ProcessState _nutzeinheitState;
        public ProcessState NutzeinheitState
        {
            get { return _nutzeinheitState; }
            set { Set(ref _nutzeinheitState, value); }
        }

        private NutzeinheitUiState _nutzeinheitUiState;
        public NutzeinheitUiState NutzeinheitUiState
        {
            get { return _nutzeinheitUiState; }
            set { Set(ref _nutzeinheitUiState, value); }
        }

        private bool _isNeLocked;
        public bool IsNeLocked
        {
            get { return _isNeLocked; }
            set { Set(ref _isNeLocked, value); }
        }
    }
}
