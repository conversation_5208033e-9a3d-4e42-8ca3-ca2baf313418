﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerwechselPageVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using Endiancode.Utilities.Extensions;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.Models;
    using Eras2AmwApp.Services;
    using Eras2AmwApp.Validators;
    using FluentValidation.Results;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Xamarin.Forms;

    public class NutzerwechselPageViewModel : EcViewModelBase
    {
        #region fields

        private readonly IPersonService personService;
        private readonly INutzeinheitService nutzeinheitService;
        private readonly INutzerService nutzerService;
        private readonly IEcDialogService dialogService;

        private Command saveNutzerwechselCommand;

        //Old Nutzer
        private string _oldNutzerNameTitleSalutation;
        private string _oldNutzerKind;
        private string _oldNutzerContact;
        private DateTime _oldNutzerMoveInDate;
        private DateTime? _oldNutzerMoveOutDate;
        private string _oldNutzerMoveOutDateErrorText;
        private bool _oldNutzerMoveOutDateHasError;

        //New Nutzer
        private string _nutzerName1;
        private string _nutzerName2;
        private string _nutzerName3;
        private string _nutzerName4;
        private DateTime _nutzerMoveInDate;
        private string _nutzerNote;
        private string _nutzerCommunicationValue;
        private Title _nutzerTitle;
        private Salutation _nutzerSalutatuion;
        private NutzerKindVM _selectedNutzerKind;
        private CommunicationDetailsVM _selectedNutzerCommunicationType;

        private string _nutzerMoveInDateErrorText;
        private string _selectedNutzerKindErrorText;
        private string _nutzerName1ErrorText;
        private string _nutzerSalutationErrorText;

        private bool _nutzerMoveInDateHasError;
        private bool _selectedNutzerKindHasError;
        private bool _nutzerName1HasError;
        private bool _nutzerSalutationHasError;

        private NutzeinheitVM selectednutzeinheitVM;
        private Guid nutzeinheitGuid;
        #endregion

        #region ctor

        public NutzerwechselPageViewModel(IServiceLocator serviceLocator, IEcNavigationService navigationService, IPersonService personService,
            INutzeinheitService nutzeinheitService, INutzerService nutzerService, IEcDialogService dialogService)
        : base(serviceLocator, navigationService)
        {
            this.personService = personService ?? throw new ArgumentNullException(nameof(personService));
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.nutzerService = nutzerService ?? throw new ArgumentNullException(nameof(nutzerService));
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));

            InitilizePickersItemLists();
        }

        #endregion

        #region commands

        public Command SaveNutzerwechselCommand => saveNutzerwechselCommand ?? (saveNutzerwechselCommand = new Command(SaveNutzerwechselExecute));

        #endregion

        #region properties

        public string OldNutzerNameTitleSalutation
        {
            get { return _oldNutzerNameTitleSalutation; }
            set { Set(ref _oldNutzerNameTitleSalutation, value); }
        }

        public string OldNutzerKind
        {
            get { return _oldNutzerKind; }
            set { Set(ref _oldNutzerKind, value); }
        }

        public string OldNutzerContact
        {
            get { return _oldNutzerContact; }
            set { Set(ref _oldNutzerContact, value); }
        }

        public DateTime OldNutzerMoveInDate
        {
            get { return _oldNutzerMoveInDate; }
            set { Set(ref _oldNutzerMoveInDate, value); }
        }

        public DateTime? OldNutzerMoveOutDate
        {
            get { return _oldNutzerMoveOutDate; }
            set
            {
                if (value != _oldNutzerMoveOutDate)
                {
                    Set(ref _oldNutzerMoveOutDate, value);
                    if (!Validate())
                    {
                        return;
                    }
                }
            }
        }

        public string OldNutzerMoveOutDateErrorText
        {
            get { return _oldNutzerMoveOutDateErrorText; }
            set { Set(ref _oldNutzerMoveOutDateErrorText, value); }
        }

        public bool OldNutzerMoveOutDateHasError
        {
            get { return _oldNutzerMoveOutDateHasError; }
            set { Set(ref _oldNutzerMoveOutDateHasError, value); }
        }

        public Title NutzerTitle
        {
            get { return _nutzerTitle; }
            set { Set(ref _nutzerTitle, value); }
        }

        public Salutation NutzerSalutation
        {
            get { return _nutzerSalutatuion; }
            set
            {
                if (value != _nutzerSalutatuion)
                {
                    Set(ref _nutzerSalutatuion, value);
                    if (!Validate())
                    {
                        return;
                    }
                }
            }
        }

        public string NutzerSalutationErrorText
        {
            get { return _nutzerSalutationErrorText; }
            set { Set(ref _nutzerSalutationErrorText, value); }
        }

        public bool NutzerSalutationHasError
        {
            get { return _nutzerSalutationHasError; }
            set { Set(ref _nutzerSalutationHasError, value); }
        }

        public NutzerKindVM SelectedNutzerKind
        {
            get { return _selectedNutzerKind; }
            set
            {
                if (value != _selectedNutzerKind)
                {
                    Set(ref _selectedNutzerKind, value);
                    if (!Validate())
                    {
                        return;
                    }
                }
            }
        }

        public string SelectedNutzerKindErrorText
        {
            get { return _selectedNutzerKindErrorText; }
            set { Set(ref _selectedNutzerKindErrorText, value); }
        }

        public bool SelectedNutzerKindHasError
        {
            get { return _selectedNutzerKindHasError; }
            set { Set(ref _selectedNutzerKindHasError, value); }
        }

        public DateTime NutzerMoveInDate
        {
            get { return _nutzerMoveInDate; }
            set
            {
                if (value != _nutzerMoveInDate)
                {
                    Set(ref _nutzerMoveInDate, value);
                    if (!Validate())
                    {
                        return;
                    }
                }
            }
        }

        public string NutzerMoveInDateErrorText
        {
            get { return _nutzerMoveInDateErrorText; }
            set { Set(ref _nutzerMoveInDateErrorText, value); }
        }

        public bool NutzerMoveInDateHasError
        {
            get { return _nutzerMoveInDateHasError; }
            set { Set(ref _nutzerMoveInDateHasError, value); }
        }

        public string NutzerName1
        {
            get { return _nutzerName1; }
            set
            {
                if (value != _nutzerName1)
                {
                    Set(ref _nutzerName1, value);
                    if (!Validate())
                    {
                        return;
                    }
                }
            }
        }

        public string NutzerName1ErrorText
        {
            get { return _nutzerName1ErrorText; }
            set { Set(ref _nutzerName1ErrorText, value); }
        }

        public bool NutzerName1HasError
        {
            get { return _nutzerName1HasError; }
            set { Set(ref _nutzerName1HasError, value); }
        }

        public string NutzerName2
        {
            get { return _nutzerName2; }
            set { Set(ref _nutzerName2, value); }
        }

        public string NutzerName3
        {
            get { return _nutzerName3; }
            set { Set(ref _nutzerName3, value); }
        }

        public string NutzerName4
        {
            get { return _nutzerName4; }
            set { Set(ref _nutzerName4, value); }
        }

        public string NutzerNote
        {
            get { return _nutzerNote; }
            set { Set(ref _nutzerNote, value); }
        }

        public CommunicationDetailsVM SelectedNutzerCommunicationType
        {
            get { return _selectedNutzerCommunicationType; }
            set { Set(ref _selectedNutzerCommunicationType, value); }
        }

        public string NutzerCommunicationValue
        {
            get { return _nutzerCommunicationValue; }
            set { Set(ref _nutzerCommunicationValue, value); }
        }

        public List<Salutation> NutzerSalutationList { get; set; }

        public List<Title> NutzerTitleList { get; set; }

        public List<NutzerKindVM> NutzerKindList { get; set; }

        public List<CommunicationDetailsVM> NutzerCommunicationTypeList { get; set; }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is NutzeinheitVM nutzeinheitVM)
            {
                selectednutzeinheitVM = nutzeinheitVM;
                nutzeinheitGuid = nutzeinheitVM.NutzeinheitGuid;
                
                InitilizeNutzer();
                
            }
            return base.SetupAsync(navigationData);
        }

        #endregion

        #region private methods

        private void InitilizePickersItemLists()
        {
            try
            {
                List<Salutation> listOfSalutations = personService.GetSalutations();
                NutzerSalutationList = new List<Salutation>();
                foreach (Salutation salutation in listOfSalutations)
                {
                    NutzerSalutationList.Add(salutation);
                }

                List<Title> listOfTitles = personService.GetTitles();
                NutzerTitleList = new List<Title>();
                foreach (Title title in listOfTitles)
                {
                    NutzerTitleList.Add(title);
                }

                List<NutzerKind> listOfNutzerKinds = Enum.GetValues(typeof(NutzerKind)).OfType<NutzerKind>().ToList();
                NutzerKindList = new List<NutzerKindVM>();
                foreach (NutzerKind nutzerKind in listOfNutzerKinds)
                {
                    NutzerKindVM nutzerKindVM = new NutzerKindVM()
                    {
                        NutzerKindType = nutzerKind.GetDescription()
                    };

                    NutzerKindList.Add(nutzerKindVM);
                }

                NutzerCommunicationTypeList = new List<CommunicationDetailsVM>();
                List<CommunicationKind> listOfCommunicationTypes = Enum.GetValues(typeof(CommunicationKind)).OfType<CommunicationKind>().ToList();

                foreach (CommunicationKind communicationEnum in listOfCommunicationTypes)
                {
                    CommunicationDetailsVM communicationDetail = new CommunicationDetailsVM()
                    {
                        CommunicationType = communicationEnum.GetDescription()
                    };
                    NutzerCommunicationTypeList.Add(communicationDetail);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while initializing content of pickers list in NutzeinheitEditPageVM");
                throw;
            }
        }

        private void InitilizeNutzer()
        {
            try
            {
                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(nutzeinheitGuid);
                DateTime appointmentTime = selectednutzeinheitVM.AppointmentDate;

                Nutzer oldNutzer = nutzerService.GetNutzerForDate(nutzeinheit, appointmentTime);
                OldNutzerNameTitleSalutation = GetFullNutzerName(oldNutzer);
                OldNutzerKind = oldNutzer.Kind.GetDescription();
                OldNutzerContact = nutzerService.GetNutzerContactValue(nutzeinheit);
                OldNutzerMoveInDate = oldNutzer.MoveInDate;
                OldNutzerMoveOutDate = oldNutzer.MoveInDate.AddDays(1);

                NutzerMoveInDate = oldNutzer.MoveInDate.AddDays(2);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while initializing properties for NutzeinheitEditPageVM!");
                throw;
            }
        }

        private string GetFullNutzerName(Nutzer oldNutzer)
        {
            string oldNutzerTitle = oldNutzer.Title?.Label + " " ?? string.Empty;
            string oldNutzerSalutation = oldNutzer.Salutation.Label ;
            string oldNutzerName1 = oldNutzer.Name1;
            string oldNutzerName2 = oldNutzer.Name2;

            return oldNutzerTitle + oldNutzerSalutation + " " + oldNutzerName1 + " " + oldNutzerName2;
        }

        private async void SaveNutzerwechselExecute()
        {
            try
            {
                if (!Validate())
                {
                    return;
                }

                
                DialogResponse dialogResponse = DialogResponse.Decline;
                if (IsLeerstandNeeded())
                {
                    string dialogMessagee = localisationService.Get("SaveConfirmationWithLeerstand");
                    dialogResponse = await dialogService.AcceptDeclineAsync(dialogMessagee, "Ja", "Nein", "Speichern bestätigen");
                }
                else
                {
                    string dialogMessagee = localisationService.Get("SaveConfirmation");
                    dialogResponse = await dialogService.AcceptDeclineAsync(dialogMessagee, "Ja", "Nein", "Speichern bestätigen");
                }

                if (dialogResponse == DialogResponse.Accept)
                {
                    Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(nutzeinheitGuid);
                    DateTime appointmentTime = selectednutzeinheitVM.AppointmentDate;

                    Nutzer oldNutzer = nutzerService.GetNutzerForDate(nutzeinheit, appointmentTime);
                    UpdateOldNutzerMoveOutDate(oldNutzer);

                    if(IsLeerstandNeeded())
                    {
                        Nutzer leerstandNutzer = CreateLeerstandNutzer(nutzeinheit);
                        nutzerService.AddNutzer(leerstandNutzer);
                    }

                    Nutzer newNutzer = AddPropertiesToNutzer(nutzeinheit);
                    newNutzer.NutzerCommunications = UpdateNutzerCommunication(newNutzer);
                    nutzerService.AddNutzer(newNutzer);

                    await navigationService.GoBackAsync();
                }
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to save New Nutzer Details in NutzerwechselPageVM!");
                throw;
            }
        }

        private void UpdateOldNutzerMoveOutDate(Nutzer oldNutzer)
        {
            oldNutzer.MoveOutDate = OldNutzerMoveOutDate;
            nutzerService.UpdateNutzer(oldNutzer);
        }

        private bool IsLeerstandNeeded()
        {
            if ((NutzerMoveInDate - OldNutzerMoveOutDate).Value.TotalDays > 1)
            {
                return true;
            }

            return false;
        }

        private Nutzer CreateLeerstandNutzer(Nutzeinheit nutzeinheit)
        {
            return new Nutzer()
            {
                Kind = NutzerKind.Leerstand,
                MoveInDate = OldNutzerMoveOutDate.Value.AddDays(1),
                MoveOutDate = NutzerMoveInDate.AddDays(-1),
                TitleId = null,
                SalutationId = 6,
                Name1 = "Leerstand",
                NutzeinheitGuid = nutzeinheit.Guid,
                NutzerCommunications = new List<NutzerCommunication>(),
                IsCreatedByApp = true
            };
        }

        private Nutzer AddPropertiesToNutzer(Nutzeinheit nutzeinheit)
        {
            Nutzer nutzer = new Nutzer
            {
                Name1 = NutzerName1,
                Name2 = NutzerName2,
                Name3 = NutzerName3,
                Name4 = NutzerName4,
                Note = NutzerNote,
                MoveInDate = NutzerMoveInDate,
                NutzeinheitGuid = nutzeinheit.Guid,
                IsCreatedByApp = true
                
            };

            if (NutzerTitle != null)
            {
                nutzer.TitleId = NutzerTitle.Id;
            }

            nutzer.SalutationId = NutzerSalutation.Id;

            Enum.TryParse(SelectedNutzerKind.NutzerKindType, out NutzerKind nutzerKind);
            nutzer.Kind = nutzerKind;

            return nutzer;
        }

        private List<NutzerCommunication> UpdateNutzerCommunication(Nutzer nutzer)
        {
            List<NutzerCommunication> nutzerCommunications = new List<NutzerCommunication>();

            if(SelectedNutzerCommunicationType == null)
            {
                return nutzerCommunications;
            }

            Enum.TryParse(SelectedNutzerCommunicationType.CommunicationType, out CommunicationKind kind);
            CommunicationFeature communicationFeature = personService.GetCommunicationFeature(kind);
            NutzerCommunication newNuterCommunication = new NutzerCommunication()
            {
                NutzerGuid = nutzer.Guid,
                Address = NutzerCommunicationValue,
                CommunicationFeatureGuid = communicationFeature.Guid
            };

            nutzerCommunications.Add(newNuterCommunication);
            
            return nutzerCommunications;
        }

        private bool Validate()
        {
            var validator = new NutzerwechselPageViewModelValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }
        #endregion
    }
}
