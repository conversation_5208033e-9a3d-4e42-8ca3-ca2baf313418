﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:inputLayout="clr-namespace:Syncfusion.XForms.TextInputLayout;assembly=Syncfusion.Core.XForms"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.NutzerEditPage">

    <NavigationPage.TitleView>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="50*"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <ImageButton    Source="deleteIcon.png"
                            Grid.Column="1"
                            BackgroundColor="Transparent"
                            HorizontalOptions="End"
                            IsEnabled="{Binding IsDeleteVisible}"
                            IsVisible="{Binding IsDeleteVisible}"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            Command="{Binding DeleteNutzeinheitCommand}">

            </ImageButton>

            <ImageButton    Grid.Column="2"
                            BackgroundColor="Transparent"
                            HorizontalOptions="End"
                            IsEnabled="False"
                            IsVisible="True"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50">  
                
                <ImageButton.Triggers>
                    <DataTrigger    TargetType="ImageButton"
                                    Binding="{Binding IsLiveSyncOn}"
                                    Value="True">
                        <Setter     Property="Source" Value="liveSyncOnIcon.png" />
                    </DataTrigger>

                    <DataTrigger    TargetType="ImageButton"
                                    Binding="{Binding IsLiveSyncOn}"
                                    Value="False">
                        <Setter     Property="Source" Value="liveSyncOffIcon.png" />
                    </DataTrigger>
                </ImageButton.Triggers>
            </ImageButton>

            <ImageButton    Source="photoIcon.png"
                            Grid.Column="3"
                            BackgroundColor="Transparent"
                            HorizontalOptions="End"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            Command="{Binding NutzeinheitPhotoCommand}">

            </ImageButton>

            <ImageButton    Source="saveIcon.png"
                            Grid.Column="4"
                            BackgroundColor="Transparent"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            HorizontalOptions="End"
                            Command="{Binding SaveNutzeinheitCommand}">
            </ImageButton>

        </Grid>

    </NavigationPage.TitleView>

    <ContentPage.Content>

        <ScrollView>

            <Grid RowSpacing="0">
                
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="70*"></ColumnDefinition>
                    <ColumnDefinition Width="10*"></ColumnDefinition>
                    <ColumnDefinition Width="10*"></ColumnDefinition>
                    <ColumnDefinition Width="10*"></ColumnDefinition>
                </Grid.ColumnDefinitions>

                <Label  x:Name="Nutzeinheit"
                        Margin="5"
                        Grid.Row="0"
                        Grid.Column="0"
                        Grid.ColumnSpan="4"
                        Text="Nutzeinheit Details"
                        FontSize="Large"
                        FontAttributes="Bold"
                        HorizontalOptions="Center"
                        TextColor="Black">
                </Label>

                <Frame  x:Name="NutzeinheitFrame"
                        Margin="5"
                        Grid.Row="1"
                        Grid.Column="0"
                        Grid.ColumnSpan="4"
                        BorderColor="Gray"
                        CornerRadius="5"
                        HasShadow="True">

                    <Grid RowSpacing="0">

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <inputLayout:SfTextInputLayout  x:Name="Location"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Location}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="0"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzeinheitLocation}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="WalkingSequnence"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation WalkSequence}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="0"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzeinheitWalkSequence}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NeNote"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Notiz:"
                                                        ContainerType="Outlined"
                                                        Grid.Row="1"
                                                        Grid.Column="0"
                                                        Grid.ColumnSpan="2"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzeinheitNote}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                    </Grid>

                </Frame>

                <Label  x:Name="NutzerDetais"
                        Margin="5"
                        Grid.Row="2"
                        Grid.Column="0"
                        Grid.ColumnSpan="4"
                        Text="Nutzer"
                        FontSize="Large"
                        FontAttributes="Bold"
                        HorizontalOptions="Center"
                        TextColor="Black">
                </Label>

                <Frame  Margin="5"
                        BorderColor="Gray"
                        CornerRadius="5"
                        HasShadow="True"
                        Grid.Row="3"
                        Grid.Column="0"
                        Grid.ColumnSpan="4">

                    <Grid>

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerKind"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Kind}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="0"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding SelectedNutzerKindHasError}"
                                                        ErrorText="{Binding SelectedNutzerKindErrorText}">

                            <Picker ItemsSource="{Binding NutzerKindList}"
                                    SelectedItem="{Binding SelectedNutzerKind}"
                                    ItemDisplayBinding="{Binding NutzerKindType}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Picker>
                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Titel"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Title}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="1"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Picker ItemsSource="{Binding NutzerTitleList}"
                                    SelectedItem="{Binding NutzerTitle}"
                                    ItemDisplayBinding="{Binding Label}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Picker>
                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Salutation"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Salutation}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="1"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center"
                                                        HasError="{Binding NutzerSalutationHasError}"
                                                        ErrorText="{Binding NutzerSalutationErrorText}">

                            <Picker ItemsSource="{Binding NutzerSalutationList}"
                                    SelectedItem="{Binding NutzerSalutation}"
                                    ItemDisplayBinding="{Binding Label}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Picker>
                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Name1"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Name1:"
                                                        ContainerType="Outlined"
                                                        Grid.Row="2"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzerName1}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Name2"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Name2:"
                                                        ContainerType="Outlined"
                                                        Grid.Row="2"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzerName2}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Name3"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Name3:"
                                                        ContainerType="Outlined"
                                                        Grid.Row="3"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzerName3}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Name4"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Name4:"
                                                        ContainerType="Outlined"
                                                        Grid.Row="3"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzerName4}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="KontaktType"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation ContactType}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="4"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Picker ItemsSource="{Binding NutzerCommunicationTypeList}"
                                    SelectedItem="{Binding SelectedNutzerCommunicationType}"
                                    ItemDisplayBinding="{Binding CommunicationType}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Picker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="KontaktValue"    
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="{markupExtensions:Localisation Contact}"
                                                        ContainerType="Outlined"
                                                        Grid.Row="4"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzerCommunicationValue}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                    </Grid>

                </Frame>

                <Label  x:Name="NutzerProperties"
                        Margin="5"
                        Grid.Row="4"
                        Grid.Column="0"
                        Grid.ColumnSpan="4"
                        Text="Nutzer Parameter"
                        FontSize="Large"
                        FontAttributes="Bold"
                        HorizontalOptions="Center"
                        TextColor="Black">
                </Label>

                <Frame  Margin="6"
                        BorderColor="Gray"
                        CornerRadius="5"
                        HasShadow="True"
                        Grid.Row="5"
                        Grid.Column="0"
                        Grid.ColumnSpan="4">

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="50"></RowDefinition>
                            <RowDefinition Height="50"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                            <ColumnDefinition Width="50*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <Frame  x:Name="NutzerPersonenFrame"
                                Margin="10,3,10,3"
                                Padding="0"
                                BorderColor="LightGray"
                                CornerRadius="5"
                                HasShadow="True"
                                Grid.Row="0"
                                Grid.Column="0"
                                Grid.ColumnSpan="2">

                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer   Command="{Binding NutzerPersonenCommand}"
                                                        NumberOfTapsRequired="1">
                                </TapGestureRecognizer>
                            </Frame.GestureRecognizers>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                </Grid.ColumnDefinitions>

                                <Label  x:Name="NutzerPersonenNumber"
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        Margin="5,5,0,5"
                                        Text="{Binding NutzerPersonenNumber}"
                                        FontSize="Small"
                                        FontAttributes="Bold">

                                </Label>

                                <Label  x:Name="NutzerPersonenFromDate"
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        Margin="5,5,0,5"
                                        Text="{Binding NutzerPersonenFromDate}"
                                        FontSize="Small"
                                        FontAttributes="Bold">

                                </Label>

                                <Label  x:Name="NutzerPersonenToDate"
                                        Grid.Row="0"
                                        Grid.Column="2"
                                        Margin="5,5,0,5"
                                        Text="{Binding NutzerPersonenToDate}"
                                        FontSize="Small"
                                        FontAttributes="Bold">

                                </Label>

                            </Grid>

                        </Frame>

                        <Frame  x:Name="NutzerQuadratmeterFrame"
                                Margin="10,3,10,3"
                                Padding="0"
                                BorderColor="LightGray"
                                CornerRadius="5"
                                HasShadow="True"
                                Grid.Row="1"
                                Grid.Column="0"
                                Grid.ColumnSpan="2">

                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer   Command="{Binding NutzerQuadratmeterCommand}"
                                                        NumberOfTapsRequired="1">
                                </TapGestureRecognizer>
                            </Frame.GestureRecognizers>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                </Grid.ColumnDefinitions>

                                <Label  x:Name="QuadratmeterHzg"
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        Margin="5,5,0,5"
                                        Text="{Binding QuadratmeterHzg}"
                                        FontSize="Small"
                                        FontAttributes="Bold">

                                </Label>

                                <Label  x:Name="QuadratmeterWw"
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        Margin="5,5,0,5"
                                        Text="{Binding QuadratmeterWw}"
                                        FontSize="Small"
                                        FontAttributes="Bold">

                                </Label>

                                <Label  x:Name="QuadratmeterNk"
                                        Grid.Row="0"
                                        Grid.Column="2"
                                        Margin="5,5,0,5"
                                        Text="{Binding QuadratmeterNk}"
                                        FontSize="Small"
                                        FontAttributes="Bold">

                                </Label>

                                <Label  x:Name="NutzerQuadratmeterFromDate"
                                        Grid.Row="0"
                                        Grid.Column="3"
                                        Margin="5,5,0,5"
                                        Text="{Binding NutzerQuadratmeterFromDate}"
                                        FontSize="Small"
                                        FontAttributes="Bold">

                                </Label>

                                <Label  x:Name="NutzerQuadratmeterToDate"
                                        Grid.Row="0"
                                        Grid.Column="4"
                                        Margin="5,5,0,5"
                                        Text="{Binding NutzerQuadratmeterToDate}"
                                        FontSize="Small"
                                        FontAttributes="Bold">

                                </Label>

                            </Grid>

                        </Frame>

                    </Grid>

                </Frame>

            </Grid>
            
        </ScrollView>

    </ContentPage.Content>
</ContentPage>