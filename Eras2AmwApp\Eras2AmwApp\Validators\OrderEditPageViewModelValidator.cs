﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderEditPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using FluentValidation;
    using ViewModels;

    public class OrderEditPageViewModelValidator : AbstractValidator<OrderEditPageViewModel>
    {
        public OrderEditPageViewModelValidator()
        {
            RuleFor(x => x.PersonSalutation)
                .NotEmpty().WithMessage("Die Anrede darf nicht leer sein.");

            RuleFor(x => x.PersonFirstName)
                .NotEmpty().WithMessage("Der Vorname darf nicht leer sein.")
                .Length(2, 25).WithMessage("Der Vorname muss zwischen 2 und 25 Zeichen lang sein.");

            RuleFor(x => x.PersonLastName)
                .NotEmpty().WithMessage("Der Nachname darf nicht leer sein.")
                .Length(2, 25).WithMessage("Der Nachname muss zwischen 2 und 25 Zeichen lang sein.");

            RuleFor(x => x.PersonPosition)
                .NotEmpty().WithMessage("Das Feld darf nicht leer sein.");

            RuleFor(x => x.PersonCommunicationValue)
                .NotEmpty().WithMessage("Das Feld darf nicht leer sein.");

            RuleFor(x => x.PersonCommunicationType)
                .NotEmpty().WithMessage("Das Feld darf nicht leer sein.");
        }
    }
}
