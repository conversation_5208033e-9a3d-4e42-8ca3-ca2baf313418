﻿<?xml version="1.0" encoding="utf-8" ?>
<Application xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:buttons="clr-namespace:Syncfusion.XForms.Buttons;assembly=Syncfusion.Buttons.XForms"
             mc:Ignorable="d"
             
             x:Class="Eras2AmwApp.App">
    <Application.Resources>
        <ResourceDictionary>
            
            <Style x:Key="OrderDetailsLabel" TargetType="Label">
                <Setter Property="FontSize">
                    <OnPlatform x:TypeArguments="x:String" Android="Medium" iOS="Medium" WinPhone="Small" />
                </Setter>
                <Setter Property="TextColor" Value="Black"></Setter>
            </Style>

            <Style x:Key="AppointmentListLabel" TargetType="Label">
                <Setter Property="FontSize">
                    <OnPlatform x:TypeArguments="x:String" Android="Medium" iOS="Medium" WinPhone="Small" />
                </Setter>
                <Setter Property="TextColor" Value="Black"></Setter>
                <Setter Property="HeightRequest" Value="65"></Setter>
            </Style>

            <Style x:Key="SyncfusionButtonBlue" TargetType="buttons:SfButton">
                <Setter Property="CornerRadius" Value="20"></Setter>
                <Setter Property="TextColor" Value="White"></Setter>
                <Setter Property="BackgroundColor" Value="#538EEC"></Setter>
                <Setter Property="BorderColor" Value="Gray"></Setter>
                <Setter Property="BorderWidth" Value="1"></Setter>
                <Setter Property="HasShadow" Value="True"></Setter>
            </Style>
            
            <Style x:Key="BlueLabelColor" TargetType="Label">
                <Setter Property="BackgroundColor"
                        Value="#3498db"></Setter>
            </Style>

            <Style x:Key="BlueButtonColor" TargetType="Button">
                <Setter Property="BackgroundColor"
                        Value="#3498db"></Setter>
            </Style>

            <Style x:Key="GreenLabelColor" TargetType="Label">
                <Setter Property="BackgroundColor" Value="#009688"></Setter>
            </Style>

            <Style x:Key="GreenButtonColor" TargetType="Button">
                <Setter Property="BackgroundColor" Value="#009688"></Setter>
            </Style>
            
        </ResourceDictionary>
    </Application.Resources>
</Application>