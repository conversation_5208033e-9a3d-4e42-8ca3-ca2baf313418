﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:controls="clr-namespace:SignaturePad.Forms;assembly=SignaturePad.Forms"
             xmlns:converter="clr-namespace:Eras2AmwApp.Converter"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.SummeryPage">

    <ContentPage.Resources>
        <converter:StringDecimalConverter x:Key="StringDecimalConverter"></converter:StringDecimalConverter>
    </ContentPage.Resources>

    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="30*"></RowDefinition>
                <RowDefinition Height="100"></RowDefinition>
                <RowDefinition Height="50"></RowDefinition>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="90*"></ColumnDefinition>
                <ColumnDefinition Width="10*"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <Frame  Margin="3"
                    BorderColor="LightGray"
                    Padding="0"
                    CornerRadius="0"
                    HasShadow="True"
                    Grid.Row="0"
                    Grid.Column="0"
                    Grid.ColumnSpan="2">

                <Grid RowSpacing="1">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                    </Grid.RowDefinitions>

                    <Label x:Name="PageTitle"
                           Grid.Row="0"
                           Margin="5,5,5,5"
                           HorizontalOptions="Center"
                           Text="Zusammenfassung"
                           TextColor="Black"
                           FontAttributes="Bold"
                           FontSize="Title">

                    </Label>

                    <Label x:Name="AllDevices"
                           Grid.Row="1"
                           Margin="20,20,5,5"
                           Text="{Binding AllDeviceInfo}"
                           FontSize="Medium">

                    </Label>

                    <Label x:Name="MaintainedDevices"
                           Grid.Row="2"
                           Margin="20,5,5,5"
                           Text="{Binding MaintainedDeviceInfo}"
                           FontSize="Medium">

                    </Label>

                    <Label x:Name="NotMaintainedDevices"
                           Grid.Row="3"
                           Margin="20,5,5,5"
                           Text="{Binding NotMaintainedDeviceInfo}"
                           FontSize="Medium">

                    </Label>

                    <Frame  Margin="10"
                            BorderColor="LightGray"
                            Padding="5"
                            CornerRadius="5"
                            HasShadow="True"
                            Grid.Row="4">

                        <Grid>

                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"></RowDefinition>
                                <RowDefinition Height="Auto"></RowDefinition>
                                <RowDefinition Height="Auto"></RowDefinition>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="60*"></ColumnDefinition>
                                <ColumnDefinition Width="20*"></ColumnDefinition>
                                <ColumnDefinition Width="20*"></ColumnDefinition>
                            </Grid.ColumnDefinitions>

                            <Label  x:Name="NotMaintainedDevicesHeader"
                                    Margin="5"
                                    Grid.Row="0"
                                    Grid.Column="0"
                                    Text="Gerätefehler Liste:"
                                    FontAttributes="Bold"
                                    FontSize="Medium">

                            </Label>

                            <ListView x:Name="deviceErrorList"
                                      HeightRequest="120"
                                      Margin="5,0,5,0"
                                      Grid.Row="1"
                                      Grid.Column="0"
                                      Grid.ColumnSpan="3"
                                      RowHeight="30"
                                      SelectionMode="None"
                                      IsPullToRefreshEnabled="False"
                                      HasUnevenRows="False"
                                      CachingStrategy="RetainElement"
                                      ItemsSource="{Binding AmwInfoKeyErrorList}">

                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <ViewCell>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="50*"></ColumnDefinition>
                                                    <ColumnDefinition Width="50*"></ColumnDefinition>
                                                </Grid.ColumnDefinitions>

                                                <Label  Text="{Binding AmwInfoKey.Info}"
                                                        HorizontalOptions="Start"
                                                        Grid.Column="0"
                                                        Margin="5"
                                                        FontSize="Small"
                                                        VerticalOptions="Start">
                                                </Label>

                                                <Label  Text="{Binding Amount}"
                                                        HorizontalOptions="End"
                                                        Grid.Column="1"
                                                        Margin="5"
                                                        FontSize="Small"
                                                        VerticalOptions="Start">
                                                </Label>
                                            </Grid>

                                        </ViewCell>
                                    </DataTemplate>
                                </ListView.ItemTemplate>

                            </ListView>

                        </Grid>

                    </Frame>
                    
                    <Frame  Margin="10"
                            BorderColor="LightGray"
                            Padding="5"
                            CornerRadius="5"
                            HasShadow="True"
                            Grid.Row="5">

                        <Grid>

                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"></RowDefinition>
                                <RowDefinition Height="Auto"></RowDefinition>
                                <RowDefinition Height="Auto"></RowDefinition>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="60*"></ColumnDefinition>
                                <ColumnDefinition Width="20*"></ColumnDefinition>
                                <ColumnDefinition Width="20*"></ColumnDefinition>
                            </Grid.ColumnDefinitions>

                            <Label x:Name="AdditinalArtikles"
                                   Margin="5"
                                   Grid.Row="0"
                                   Grid.Column="0"
                                   Text="Zusatzartikel:"
                                   FontAttributes="Bold"
                                   FontSize="Medium">
                            </Label>

                            <ListView x:Name="selectedArticlesList"
                                      HeightRequest="120"
                                      Margin="5,0,5,0"
                                      Grid.Row="1"
                                      Grid.Column="0"
                                      Grid.ColumnSpan="3"
                                      RowHeight="30"
                                      SelectionMode="None"
                                      IsPullToRefreshEnabled="False"
                                      HasUnevenRows="False"
                                      CachingStrategy="RetainElement"
                                      ItemsSource="{Binding DeviceAdditionalArticleList}">

                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <ViewCell>
                                            <Grid>

                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="50*"></ColumnDefinition>
                                                    <ColumnDefinition Width="50*"></ColumnDefinition>
                                                </Grid.ColumnDefinitions>
                                                
                                                <Label  Text="{Binding AdditionalArticle.Label}"
                                                        Grid.Column="0"
                                                        Margin="5"
                                                        FontSize="Small"
                                                        VerticalOptions="Center">
                                                </Label>

                                                <Label  Text="{Binding Quantity,Converter={StaticResource StringDecimalConverter}}"
                                                        Grid.Column="1"
                                                        HorizontalOptions="End"
                                                        Margin="5"
                                                        FontSize="Small"
                                                        VerticalOptions="Center">
                                                </Label>
                                            </Grid>
                                            


                                        </ViewCell>
                                    </DataTemplate>
                                </ListView.ItemTemplate>

                            </ListView>

                        </Grid>

                    </Frame>

                </Grid>
            </Frame>
            
            <controls:SignaturePadView  x:Name="signaturePad"
                                        Grid.Row="2"
                                        Grid.RowSpan="2"
                                        Grid.Column="0"
                                        Margin="10,0,0,10"
                                        StrokeWidth="3"
                                        StrokeColor="Black"
                                        BackgroundColor="LightGray"
                                        CaptionText="Unterschrift:" />

            <ImageButton    x:Name="DeleteButton"
                            Source="deleteIcon.png"
                            BackgroundColor="Transparent"
                            VerticalOptions="End"
                            Margin="0"
                            Grid.Row="2"
                            Grid.Column="1"
                            WidthRequest="50"
                            HeightRequest="50"
                            Command="{Binding DeleteSignatureCommand}">

            </ImageButton>

            <ImageButton    Source="saveIcon.png"
                            BackgroundColor="Transparent"
                            Margin="0"
                            WidthRequest="50"
                            HeightRequest="50"
                            HorizontalOptions="End"
                            Grid.Row="3"
                            Grid.Column="1"
                            Command="{Binding SaveSignatureCommand}" />

        </Grid>
    </ContentPage.Content>
</ContentPage>