﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="RauchmelderExchangePageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using Device = Domain.Eras2Amw.Models.Device;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Interfaces;
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using Eras2AmwApp.Models;
    using Eras2AmwApp.Services;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.BusinessLogic.Factory;
    using Plugin.Media;
    using Plugin.Media.Abstractions;
    using System.IO;
    using FluentValidation.Results;
    using System.Globalization;
    using Eras2AmwApp.Validators;
    using Syncfusion.XForms.TabView;
    using Xamarin.Essentials;

    public class RauchmelderExchangePageViewModel : EcViewModelBase, ISupportParentViewModel
    {
        #region fields
        private readonly IDeviceService deviceService;
        private readonly ISignatureService signatureService;
        private readonly IStammdatenService stammdatenService;
        private readonly INutzeinheitService nutzeinheitService;
        private readonly IEcDialogService dialogService;
        private readonly IPhotoService photoService;
        private readonly IScannerService scannerService;
        private readonly IDeviceOrderKindChangeDialog deviceOrderKindChangeDialog;
        private readonly IOrderService orderService;

        private Command saveDeviceCommand;
        private Command photoCommand;
        private Command readBarcodCommand;
        private Command addArticleCommand;
        private Command removeArticleCommand;
        private Command setInstallationDateCommand;
        private Command setDeinstallationDateCommand;
        private Command removeNewArticleCommand;
        private Command confirmOldDeviceCommand;
        private Command addNewArticleCommand;
        private Command newReadBarcodCommand;
        private Command newPhotoCommand;
        private Command changeDeviceOrderKindCommand;

        private EcViewModelBase _parentViewModel;
        private bool _showMaintainOrErrorWarning;

        //current device fields
        private string _oldDeviceNumber;
        private DeviceClass _oldDeviceClass;
        private Room _oldSelectedRoom;
        private string _oldOngoingNumber;
        private string _oldArticleDescription;
        private string _oldArticleNumber;
        private string _oldDeviceCalibrationDate;
        private DateTime _oldDeviceInstallationDate;
        private DateTime? _oldDeviceDeinstallationDate;
        private double? _oldDeviceReading;
        private bool _oldDeviceIsMaintained;
        private string _oldDeviceNote;
        private AmwInfoKey _oldSelectedAmwKey;
        private AdditionalArticle _selectedAdditionalArticle;
        private int _selectedAdditionalArticleListCount;
        private AdditionalArticleVM _selectedToRemoveAdditionalAricle;
        private string _additionalArticlesNumber;
        private List<DeviceCatalog> _deviceCatalogList;

        //new device fields
        private string _newDeviceNumber;
        private DeviceClass _newDeviceClass;
        private Room _newSelectedRoom;
        private string _newOngoingNumber;
        private DeviceCatalog _newSelectedDeviceCatalog;
        private bool _isDeviceCatalogEnabled;
        private DeviceKind _newSelectedDeviceKind;
        private string _newDeviceCalibrationDate;
        private DateTime _newDeviceInstallationDate;
        private double? _newDeviceReading;
        private bool _newDeviceIsMaintained;
        private AmwInfoKey _newSelectedAmwKey;
        private string _newDeviceNote;
        private string _newAdditionalArticlesNumber;
        private AdditionalArticle _newSelectedAdditionalArticle;
        private int _newSelectedAdditionalArticleListCount;
        private AdditionalArticleVM _newSelectedToRemoveAdditionalAricle;

        private string _oldDeviceNumberErrorText;
        private string _newDeviceNumberErrorText;
        private string _newSelectedDeviceKindErrorText;
        private string _newSelectedDeviceCatalogErrorText;
        private string _newSelectedRoomErrorText;
        private string _newOngoingNumberErrorText;
        private string _oldDeinstallationDateErrorText;
        private string _newDeviceInstallationDateErrorText;

        private bool _oldDeviceNumberHasError;
        private bool _newSelectedDeviceKindHasError;
        private bool _newSelectedDeviceCatalogHasError;
        private bool _newDeviceNumberHasError;
        private bool _newSelectedRoomHasError;
        private bool _newOngoingNumberHasError;
        private bool _oldDeinstallationDateHasError;
        private bool _newDeviceInstallationDateHasError;

        private SfTabView _tabView;

        private IRauchmelder selectedDevice;
        private Device currentDevice;
        private DeviceOrderState currentDeviceOrderState;
        private NutzeinheitOrder nutzeinheitOrder;
        private bool wasDeleteDeclined;
        private List<AmwInfoKey> _amwInfoKeyList;

        #endregion

        #region ctor

        public RauchmelderExchangePageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            IDeviceService deviceService,
            ISignatureService signatureService,
            IStammdatenService stammdatenService,
            INutzeinheitService nutzeinheitService,
            IEcDialogService dialogService,
            IPhotoService photoService,
            IScannerService scannerService,
            IDeviceOrderKindChangeDialog deviceOrderKindChangeDialog,
            IOrderService orderService)
         : base(serviceLocator, navigationService)
        {
            this.deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
            this.signatureService = signatureService ?? throw new ArgumentNullException(nameof(signatureService));
            this.stammdatenService = stammdatenService ?? throw new ArgumentNullException(nameof(stammdatenService));
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.photoService = photoService ?? throw new ArgumentNullException(nameof(photoService));
            this.scannerService = scannerService ?? throw new ArgumentNullException(nameof(scannerService));
            this.deviceOrderKindChangeDialog = deviceOrderKindChangeDialog ?? throw new ArgumentNullException(nameof(deviceOrderKindChangeDialog));
            this.orderService = orderService ?? throw new ArgumentNullException(nameof(orderService));

            SelectedAdditionalArticleList = new ObservableCollection<AdditionalArticleVM>();
            SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;

            NewSelectedAdditionalArticleList = new ObservableCollection<AdditionalArticleVM>();
            NewSelectedAdditionalArticleListCount = NewSelectedAdditionalArticleList.Count;
            NewDevicePictues = new List<MediaFile>();
            TabView = new SfTabView();
            AmwInfoKeyList = new List<AmwInfoKey>();
            ShowMaintainOrErrorWarning = false;

            wasDeleteDeclined = false;
            InitPickerItemList();
            InitDeviceTyp();
        }

        #endregion

        #region commands

        public Command SaveDeviceCommand => saveDeviceCommand ?? (saveDeviceCommand = new Command(SaveDeviceExecute));

        public Command PhotoCommand => photoCommand ?? (photoCommand = new Command(PhotoExecute));

        public Command ReadBarcodCommand => readBarcodCommand ?? (readBarcodCommand = new Command(ReadBarcodExecute));

        public Command AddArticleCommand => addArticleCommand ?? (addArticleCommand = new Command<AdditionalArticle>(AddArticleExecute));

        public Command RemoveArticleCommand => removeArticleCommand ?? (removeArticleCommand = new Command<AdditionalArticleVM>(RemoveArticleExecute));

        public Command SetInstallationDateCommand => setInstallationDateCommand ?? (setInstallationDateCommand = new Command(SetInstallationDateExecute));

        public Command SetDeinstallationDateCommand => setDeinstallationDateCommand ?? (setDeinstallationDateCommand = new Command(SetDeinstallationDateExecute));

        public Command AddNewArticleCommand => addNewArticleCommand ?? (addNewArticleCommand = new Command<AdditionalArticle>(AddNewArticleExecute));

        public Command RemoveNewArticleCommand => removeNewArticleCommand ?? (removeNewArticleCommand = new Command<AdditionalArticleVM>(RemoveNewArticleExecute));

        public Command NewReadBarcodCommand => newReadBarcodCommand ?? (newReadBarcodCommand = new Command(NewReadBarcodExecute));

        public Command NewPhotoCommand => newPhotoCommand ?? (newPhotoCommand = new Command(NewPhotoExecute));

        public Command ConfirmOldDeviceCommand => confirmOldDeviceCommand ?? (confirmOldDeviceCommand = new Command(ConfirmOldDeviceExecute));

        public Command ChangeDeviceOrderKindCommand => changeDeviceOrderKindCommand ?? (changeDeviceOrderKindCommand = new Command(ChangeDeviceOrderKindExecute));

        #endregion

        #region properties

        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { Set(ref _parentViewModel, value); }
        }

        public bool ShowMaintainOrErrorWarning
        {
            get { return _showMaintainOrErrorWarning; }
            set { Set(ref _showMaintainOrErrorWarning, value); }
        }

        public Guid NutzeinheitGuid { get; set; }

        public string OldDeviceNumber
        {
            get { return _oldDeviceNumber; }
            set { Set(ref _oldDeviceNumber, value); }
        }

        public string OldDeviceNumberErrorText
        {
            get { return _oldDeviceNumberErrorText; }
            set { Set(ref _oldDeviceNumberErrorText, value); }
        }

        public bool OldDeviceNumberHasError
        {
            get { return _oldDeviceNumberHasError; }
            set { Set(ref _oldDeviceNumberHasError, value); }
        }

        public DeviceClass OldDeviceClass
        {
            get { return _oldDeviceClass; }
            set
            {
                if (value != _oldDeviceClass)
                {
                    Set(ref _oldDeviceClass, value);
                    GetAmwInfoKeyList();
                }
            }
        }

        public Room OldSelectedRoom
        {
            get { return _oldSelectedRoom; }
            set { Set(ref _oldSelectedRoom, value); }
        }

        public string OldOngoingNumber
        {
            get { return _oldOngoingNumber; }
            set { Set(ref _oldOngoingNumber, value); }
        }

        public string OldArticleDescription
        {
            get { return _oldArticleDescription; }
            set { Set(ref _oldArticleDescription, value); }
        }

        public string OldArticleNumber
        {
            get { return _oldArticleNumber; }
            set { Set(ref _oldArticleNumber, value); }
        }

        public string OldDeviceCalibrationDate
        {
            get { return _oldDeviceCalibrationDate; }
            set
            {
                if (value == " ")
                {
                    value = null;
                }
                Set(ref _oldDeviceCalibrationDate, value);
            }
        }

        public DateTime OldDeviceInstallationDate
        {
            get { return _oldDeviceInstallationDate; }
            set { Set(ref _oldDeviceInstallationDate, value); }
        }

        public DateTime? OldDeviceDeinstallationDate
        {
            get { return _oldDeviceDeinstallationDate; }
            set { Set(ref _oldDeviceDeinstallationDate, value); }
        }

        public string OldDeviceDeinstallationDateErrorText
        {
            get { return _oldDeinstallationDateErrorText; }
            set { Set(ref _oldDeinstallationDateErrorText, value); }
        }

        public bool OldDeviceDeinstallationDateHasError
        {
            get { return _oldDeinstallationDateHasError; }
            set { Set(ref _oldDeinstallationDateHasError, value); }
        }

        public double? OldDeviceReading
        {
            get { return _oldDeviceReading; }
            set { Set(ref _oldDeviceReading, value); }
        }

        public bool OldDeviceIsMaintained
        {
            get { return _oldDeviceIsMaintained; }
            set { Set(ref _oldDeviceIsMaintained, value); }
        }

        public AmwInfoKey OldSelectedAmwKey
        {
            get { return _oldSelectedAmwKey; }
            set { Set(ref _oldSelectedAmwKey, value); }
        }

        public string OldDeviceNote
        {
            get { return _oldDeviceNote; }
            set
            {
                if (value != null)
                {
                    value = RemoveUnsupportedCharacters(value);
                }
                Set(ref _oldDeviceNote, value);
            }
        }

        public string NewDeviceNumber
        {
            get { return _newDeviceNumber; }
            set { Set(ref _newDeviceNumber, value); }
        }

        public string NewDeviceNumberErrorText
        {
            get { return _newDeviceNumberErrorText; }
            set { Set(ref _newDeviceNumberErrorText, value); }
        }

        public bool NewDeviceNumberHasError
        {
            get { return _newDeviceNumberHasError; }
            set { Set(ref _newDeviceNumberHasError, value); }
        }

        public DeviceClass NewDeviceClass
        {
            get { return _newDeviceClass; }
            set { Set(ref _newDeviceClass, value); }
        }

        public Room NewSelectedRoom
        {
            get { return _newSelectedRoom; }
            set { Set(ref _newSelectedRoom, value); }
        }

        public string NewSelectedRoomErrorText
        {
            get { return _newSelectedRoomErrorText; }
            set { Set(ref _newSelectedRoomErrorText, value); }
        }

        public bool NewSelectedRoomHasError
        {
            get { return _newSelectedRoomHasError; }
            set { Set(ref _newSelectedRoomHasError, value); }
        }

        public string NewOngoingNumber
        {
            get { return _newOngoingNumber; }
            set { Set(ref _newOngoingNumber, value); }
        }

        public string NewOngoingNumberErrorText
        {
            get { return _newOngoingNumberErrorText; }
            set { Set(ref _newOngoingNumberErrorText, value); }
        }

        public bool NewOngoingNumberHasError
        {
            get { return _newOngoingNumberHasError; }
            set { Set(ref _newOngoingNumberHasError, value); }
        }

        public DeviceKind NewSelectedDeviceKind
        {
            get { return _newSelectedDeviceKind; }
            set
            {
                if (value != _newSelectedDeviceKind)
                {
                    Set(ref _newSelectedDeviceKind, value);
                    GetDeviceCatalogOptions(value);
                }
            }
        }

        public bool NewSelectedDeviceKindHasError
        {
            get { return _newSelectedDeviceKindHasError; }
            set { Set(ref _newSelectedDeviceKindHasError, value); }
        }

        public string NewSelectedDeviceKindErrorText
        {
            get { return _newSelectedDeviceKindErrorText; }
            set { Set(ref _newSelectedDeviceKindErrorText, value); }
        }

        public DeviceCatalog NewSelectedDeviceCatalog
        {
            get { return _newSelectedDeviceCatalog; }
            set { Set(ref _newSelectedDeviceCatalog, value); }
        }

        public string NewDeviceCalibrationDate
        {
            get { return _newDeviceCalibrationDate; }
            set
            {
                if (value == " ")
                {
                    value = null;
                }
                Set(ref _newDeviceCalibrationDate, value);
            }
        }

        public DateTime NewDeviceInstallationDate
        {
            get { return _newDeviceInstallationDate; }
            set { Set(ref _newDeviceInstallationDate, value); }
        }

        public bool NewDeviceInstallationDateHasError
        {
            get { return _newDeviceInstallationDateHasError; }
            set { Set(ref _newDeviceInstallationDateHasError, value); }
        }

        public string NewDeviceInstallationDateErrorText
        {
            get { return _newDeviceInstallationDateErrorText; }
            set { Set(ref _newDeviceInstallationDateErrorText, value); }
        }

        public double? NewDeviceReading
        {
            get { return _newDeviceReading; }
            set { Set(ref _newDeviceReading, value); }
        }

        public bool NewDeviceIsMaintained
        {
            get { return _newDeviceIsMaintained; }
            set { Set(ref _newDeviceIsMaintained, value); }
        }

        public AmwInfoKey NewSelectedAmwKey
        {
            get { return _newSelectedAmwKey; }
            set { Set(ref _newSelectedAmwKey, value); }
        }

        public string NewDeviceNote
        {
            get { return _newDeviceNote; }
            set
            {
                if (value != null)
                {
                    value = RemoveUnsupportedCharacters(value);
                }
                Set(ref _newDeviceNote, value);
            }
        }

        public int SelectedAdditionalArticleListCount
        {
            get { return _selectedAdditionalArticleListCount; }
            set { Set(ref _selectedAdditionalArticleListCount, value); }
        }

        public List<DeviceKind> DeviceKindsList { get; set; }

        public List<DeviceCatalog> DeviceCatalogList
        {
            get { return _deviceCatalogList; }
            set { Set(ref _deviceCatalogList, value); }
        }

        public string NewSelectedDeviceCatalogErrorText
        {
            get { return _newSelectedDeviceCatalogErrorText; }
            set { Set(ref _newSelectedDeviceCatalogErrorText, value); }
        }

        public bool NewSelectedDeviceCatalogHasError
        {
            get { return _newSelectedDeviceCatalogHasError; }
            set { Set(ref _newSelectedDeviceCatalogHasError, value); }
        }

        public bool IsDeviceCatalogEnabled
        {
            get { return _isDeviceCatalogEnabled; }
            set { Set(ref _isDeviceCatalogEnabled, value); }
        }

        public string AdditionalArticlesNumber
        {
            get { return _additionalArticlesNumber; }
            set { Set(ref _additionalArticlesNumber, value); }
        }

        public AdditionalArticle SelectedAdditionalArticle
        {
            get { return _selectedAdditionalArticle; }
            set { Set(ref _selectedAdditionalArticle, value); }
        }

        public AdditionalArticleVM SelectedToRemoveAdditionalAricle
        {
            get { return _selectedToRemoveAdditionalAricle; }
            set { Set(ref _selectedToRemoveAdditionalAricle, value); }
        }

        public AdditionalArticleVM NewSelectedToRemoveAdditionalAricle
        {
            get { return _newSelectedToRemoveAdditionalAricle; }
            set { Set(ref _newSelectedToRemoveAdditionalAricle, value); }
        }

        public AdditionalArticle NewSelectedAdditionalArticle
        {
            get { return _newSelectedAdditionalArticle; }
            set { Set(ref _newSelectedAdditionalArticle, value); }
        }

        public int NewSelectedAdditionalArticleListCount
        {
            get { return _newSelectedAdditionalArticleListCount; }
            set { Set(ref _newSelectedAdditionalArticleListCount, value); }
        }

        public string NewAdditionalArticlesNumber
        {
            get { return _newAdditionalArticlesNumber; }
            set { Set(ref _newAdditionalArticlesNumber, value); }
        }

        public List<Room> RoomList { get; set; }

        public List<AmwInfoKey> AmwInfoKeyList
        {
            get { return _amwInfoKeyList; }
            set { Set(ref _amwInfoKeyList, value); }
        }

        public ObservableCollection<AdditionalArticle> AdditionalArticleList { get; set; }

        public ObservableCollection<AdditionalArticleVM> SelectedAdditionalArticleList { get; set; }

        public ObservableCollection<AdditionalArticle> NewAdditionalArticleList { get; set; }

        public ObservableCollection<AdditionalArticleVM> NewSelectedAdditionalArticleList { get; set; }

        public List<MediaFile> NewDevicePictues { get; set; }

        public ObservableCollection<string> PickerYearList { get; set; }

        public SfTabView TabView
        {
            get { return _tabView; }
            set { Set(ref _tabView, value); }
        }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is NutzeinheitPageViewModel parentVM)
            {
                ParentViewModel = parentVM;
                selectedDevice = (IRauchmelder)parentVM.SelectedDevice.Device;
                NutzeinheitGuid = parentVM.SelectedNutzeinheitVM.NutzeinheitGuid;
                InitilizeProperties();
                InitOngoingNumberPlaceholder();
            }

            return base.SetupAsync(navigationData);
        }

        public NutzeinheitPageViewModel GetParentProperty()
        {
            return ParentViewModel as NutzeinheitPageViewModel;
        }

        public async Task DeselectListViewItemOnBackButton()
        {
            var previousVm = GetParentProperty();
            previousVm.SelectedDevice = null;
            await navigationService.GoBackAsync();
        }

        #endregion

        #region private methods

        private void InitDeviceTyp()
        {
            List<DeviceKind> deviceKindsList = deviceService.GetDeviceKinds();
            DeviceKindsList = deviceKindsList.Where(x => x.LabelShort == "Allg. KWZ" || x.LabelShort == "Allg. WWZ" || x.LabelShort == "Allg. WMZ" || x.LabelShort == "Allg. HKV" || x.LabelShort == "Allg. RM").ToList();

            if (DeviceKindsList.Count == 1)
            {
                NewSelectedDeviceKind = DeviceKindsList[0];
            }
        }

        private void GetDeviceCatalogOptions(DeviceKind deviceKind)
        {
            try
            {
                if (deviceKind is null)
                {
                    return;
                }

                List<DeviceCatalog> deviceCatalogList = deviceService.GetDeviceCatalogList(deviceKind.Guid);
                if (deviceCatalogList.Any())
                {
                    IsDeviceCatalogEnabled = true;
                }
                else
                {
                    IsDeviceCatalogEnabled = false;
                }
                DeviceCatalogList = deviceCatalogList;

                if (DeviceCatalogList.Count == 1)
                {
                    NewSelectedDeviceCatalog = DeviceCatalogList[0];
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while initilizing DeviceCatalogOption list!");
                throw;
            }
        }

        private void GetAmwInfoKeyList()
        {
            List<AmwInfoKey> infoKeyList = new List<AmwInfoKey>();

            if (OldDeviceClass == DeviceClass.RM)
            {
                infoKeyList = stammdatenService.GetRauchmelderInfoKey();
            }
            else if (OldDeviceClass == DeviceClass.KWZ || OldDeviceClass == DeviceClass.WWZ)
            {
                infoKeyList = stammdatenService.GetWasserzählerInfoKey();
            }
            else if (OldDeviceClass == DeviceClass.HKV)
            {
                infoKeyList = stammdatenService.GetHkvInfoKey();
            }

            AmwInfoKey emptyKey = new AmwInfoKey()
            {
                Key = -1,
                Info = "<Infoschlüssel löschen>"
            };
            infoKeyList.Insert(0, emptyKey);

            AmwInfoKeyList = infoKeyList;
        }

        private void InitPickerItemList()
        {
            try
            {
                List<AdditionalArticle> articleList = stammdatenService.GetAdditionalArticles();
                AdditionalArticleList = new ObservableCollection<AdditionalArticle>();
                foreach (AdditionalArticle additionalArticle in articleList)
                {
                    AdditionalArticleList.Add(additionalArticle);
                }

                NewAdditionalArticleList = new ObservableCollection<AdditionalArticle>();
                foreach (AdditionalArticle additionalArticle in articleList)
                {
                    NewAdditionalArticleList.Add(additionalArticle);
                }

                List<Room> roomList = stammdatenService.GetRooms();
                RoomList = new List<Room>();
                foreach (Room room in roomList)
                {
                    RoomList.Add(room);
                }

                PickerYearList = new ObservableCollection<string>
                {
                    " "
                };


                for (int x = 0; x <= 10; x++)
                {
                    string year = DateTime.Now.AddYears(x).ToString("yyyy");
                    PickerYearList.Add(year);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while initilizing AmwInfoKey list and AdditionalArticleList!");
                throw;
            }
        }

        private void InitOngoingNumberPlaceholder()
        {
            try
            {
                NewOngoingNumber = OldOngoingNumber;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to init suggested placeholder number for ongoing number!");
                throw;
            }
        }

        private void InitilizeProperties()
        {
            try
            {
                currentDevice = deviceService.GetDevice(selectedDevice.DeviceGuid);
                currentDeviceOrderState = deviceService.GetDeviceOrderState(selectedDevice.DeviceGuid, selectedDevice.DeviceOrderState.OrderGuid);

                //Init values for Old Device
                OldDeviceNumber = selectedDevice.DeviceNumber;
                OldDeviceClass = currentDevice.DeviceCatalog.DeviceKind.Class;
                OldArticleDescription = currentDevice.DeviceCatalog.ArticleDescription;
                OldArticleNumber = currentDevice.DeviceCatalog.ArticleNumber;
                OldSelectedRoom = RoomList.FirstOrDefault(x => x.Guid == selectedDevice.DeviceRoom.Guid);
                OldOngoingNumber = currentDevice.OngoingNumber;
                OldDeviceIsMaintained = currentDevice.IsMaintained;

                if (selectedDevice.DeviceCalibrationDate.HasValue)
                {
                    OldDeviceCalibrationDate = PickerYearList.SingleOrDefault(x => x == selectedDevice.DeviceCalibrationDate.Value.Year.ToString());
                }
                OldDeviceInstallationDate = selectedDevice.DeviceInstallationDate;
                OldDeviceDeinstallationDate = selectedDevice.DeviceDeinstallationDate;
                OldDeviceReading = currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First().Reading;
                OldSelectedAmwKey = AmwInfoKeyList.FirstOrDefault(x => x.Guid == currentDeviceOrderState.AmwInfoKeyGuid);
                OldDeviceNote = selectedDevice.DeviceNote;
                if (currentDevice.DeviceAdditionalArticles.Any())
                {
                    IList<DeviceAdditionalArticle> additionalArticles = currentDevice.DeviceAdditionalArticles;
                    foreach (DeviceAdditionalArticle additionalArticle in additionalArticles)
                    {
                        AdditionalArticleVM additionalArticleVM = new AdditionalArticleVM()
                        {
                            AdditionalArticle = additionalArticle.AdditionalArticle,
                            Amount = additionalArticle.Quantity,
                            IsCreatedByApp = additionalArticle.IsCreatedByApp
                        };
                        SelectedAdditionalArticleList.Add(additionalArticleVM);
                        AdditionalArticle existingArticle = AdditionalArticleList.Where(x => x.Guid == additionalArticle.AdditionalArticle.Guid).Single();
                        AdditionalArticleList.Remove(existingArticle);
                    }

                    SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;
                }

                //Init values for New Devices
                NewDeviceClass = currentDevice.DeviceCatalog.DeviceKind.Class;
                NewDeviceInstallationDate = DateTime.Now.AddDays(1);
                NewSelectedDeviceKind = DeviceKindsList.Where(x => x.Guid == currentDevice.DeviceCatalog.DeviceKind.Guid).SingleOrDefault();
                NewSelectedRoom = RoomList.Where(x => x.Guid == selectedDevice.DeviceRoom.Guid).SingleOrDefault();
                NewDeviceIsMaintained = false;
                NewDeviceCalibrationDate = PickerYearList.Where(x => x == DateTime.Now.AddYears(10).ToString("yyyy")).SingleOrDefault();
                
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to initialize properties in RauchmelderExchangePage!");
                throw;
            }
        }

        private async void SaveDeviceExecute()
        {
            try
            {
                if (OldDeviceDeinstallationDate == null && OldSelectedAmwKey == null)
                {
                    ShowMaintainOrErrorWarning = true;
                    TabView.SelectedIndex = 0;
                    return;
                }
                else
                {
                    ShowMaintainOrErrorWarning = false;
                }

                if (!Validate())
                {
                    return;
                }

                await UpdateOldDeviceState();

                await AddNewDevice();

                NutzeinheitPageViewModel previousVM = GetParentProperty();
                previousVM.SelectedDevice = null;
                await navigationService.GoBackAsync();

            }
            catch (Exception e)
            {
                logger.Error(e, "Excepton occured while attempting to save Device information edit!");
                throw;
            }
        }

        private async Task UpdateOldDeviceState()
        {
            currentDevice = await UpdateDeviceInformation();

            if (wasDeleteDeclined)
            {
                return;
            }

            await DeleteDeviceArticlesRemovedFromList();

            DeviceOrderState deviceOrderState = deviceService.GetDeviceOrderState(selectedDevice.DeviceGuid, selectedDevice.DeviceOrderState.OrderGuid);

            await AddUpdateDeviceAdditionalArticle(deviceOrderState);
        }

        private async Task<Device> UpdateDeviceInformation()
        {
            if (!await CheckIfCanBeSave())
            {
                return currentDevice;
            }

            GetUIDeviceState();
            UpdateDeviceOrderState();

            if (currentDeviceOrderState.AmwInfoKeyGuid != null)
            {
                currentDevice.IsMaintained = false;
            }

            deviceService.UpdateDeviceConsumption(currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First());
            deviceService.UpdateDevice(currentDevice);

            return currentDevice;
        }

        private void GetUIDeviceState()
        {
            currentDevice.Number = OldDeviceNumber;
            currentDevice.InstallationDate = OldDeviceInstallationDate;
            currentDevice.DeinstallationDate = OldDeviceDeinstallationDate;
            currentDevice.Note = OldDeviceNote;
            currentDevice.IsMaintained = OldDeviceIsMaintained;

            if (!string.IsNullOrEmpty(OldDeviceCalibrationDate))
            {
                var cultureInfo = new CultureInfo("de-DE");
                string stringCalibrationDate = "1 Jan " + OldDeviceCalibrationDate;
                DateTime dateTime = DateTime.Parse(stringCalibrationDate, cultureInfo);
                currentDevice.CalibrationDate = dateTime;
            }
            else
            {
                currentDevice.CalibrationDate = null;
            }

            if (currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First().Reading != OldDeviceReading)
            {
                currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First().Reading = OldDeviceReading;
                currentDevice.DeviceConsumptions.OrderByDescending(x => x.ReadingDate).First().ReadingDate = DateTime.Now;
            }

        }

        private void UpdateDeviceOrderState()
        {
            if (OldSelectedAmwKey != null && OldSelectedAmwKey.Key != -1)
            {
                currentDeviceOrderState.AmwInfoKeyGuid = OldSelectedAmwKey.Guid;
                currentDeviceOrderState.AmwInfoKey = OldSelectedAmwKey;
            }

            if (currentDeviceOrderState.ProcessState != ProcessState.Creating)
            {
                currentDeviceOrderState.ProcessState = ProcessState.Updating;
                currentDeviceOrderState.CompletedDate = DateTime.Now;
            }
            deviceService.UpdateDeviceOrderState(currentDeviceOrderState);
        }

        private async Task<bool> CheckIfCanBeSave()
        {
            //Check if there is AmwKey in Ne
            NutzeinheitPageViewModel pageViewModel = GetParentProperty();
            bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

            if (!result)
            {
                return false;
            }

            //Check if user wants to procede and delete signature if exists
            Signature signature = GetSignatureForThisOrder();

            if (signature != null)
            {
                DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                if (signatureDeleteWarningResult == DialogResponse.Decline)
                {
                    return false;
                }
            }

            return true;
        }

        private async Task DeleteDeviceArticlesRemovedFromList()
        {
            List<DeviceAdditionalArticle> existingArticlesList = currentDevice.DeviceAdditionalArticles.ToList();
            List<DeviceAdditionalArticle> removedArticles = new List<DeviceAdditionalArticle>();

            foreach (DeviceAdditionalArticle deviceAdditionalArticle in existingArticlesList)
            {
                bool isPresent = SelectedAdditionalArticleList.Any(x => x.AdditionalArticle.Guid == deviceAdditionalArticle.AdditionalArticle.Guid);
                if (!isPresent)
                {
                    removedArticles.Add(deviceAdditionalArticle);
                }
            }

            if (removedArticles.Any())
            {
                NutzeinheitPageViewModel pageViewModel = GetParentProperty();
                bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

                if (!result)
                {
                    return;
                }


                Signature signature = GetSignatureForThisOrder();

                if (signature != null)
                {
                    DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                    if (signatureDeleteWarningResult == DialogResponse.Decline)
                    {
                        return;
                    }
                }

                deviceService.RemoveDeviceAdditionalArticle(removedArticles);
            }
        }

        private Signature GetSignatureForThisOrder()
        {
            Guid orderGuid = selectedDevice.OrderGuid;
            Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheitWithDevice(currentDevice);

            nutzeinheitOrder = new NutzeinheitOrder()
            {
                NutzeinheitGuid = nutzeinheit.Guid,
                OrderGuid = orderGuid
            };

            Order order = orderService.GetOrder(orderGuid);

            if (order.Number.Contains("#x#"))
            {
                Order mainOrder = orderService.GetMainOrderFromBackup(order);
                orderGuid = mainOrder.Guid;
            }

            return signatureService.GetSignatureForOrder(nutzeinheitOrder.NutzeinheitGuid, orderGuid);
        }

        private async Task AddUpdateDeviceAdditionalArticle(DeviceOrderState deviceOrderState)
        {
            Device updatedDevice = deviceService.GetDevice(selectedDevice.DeviceGuid);
            IList<DeviceAdditionalArticle> updatedArticlesList = updatedDevice.DeviceAdditionalArticles;

            if (SelectedAdditionalArticleList != null)
            {
                foreach (AdditionalArticleVM additionalArticle in SelectedAdditionalArticleList)
                {
                    DeviceAdditionalArticle existingArticle = updatedArticlesList.SingleOrDefault(x => x.AdditionalArticleGuid == additionalArticle.AdditionalArticle.Guid);
                    Signature signature = GetSignatureForThisOrder();

                    if (existingArticle != null)
                    {
                        NutzeinheitPageViewModel pageViewModel = GetParentProperty();
                        bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

                        if (!result)
                        {
                            return;
                        }

                        if (signature != null)
                        {
                            DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                            if (signatureDeleteWarningResult == DialogResponse.Decline)
                            {
                                return;
                            }
                        }

                        UpdateExistingDeviceArticle(additionalArticle, existingArticle);
                    }
                    else
                    {
                        NutzeinheitPageViewModel pageViewModel = GetParentProperty();
                        bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

                        if (!result)
                        {
                            return;
                        }

                        if (signature != null)
                        {
                            DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                            if (signatureDeleteWarningResult == DialogResponse.Decline)
                            {
                                return;
                            }
                        }

                        AddNewDeviceArticles(updatedDevice, additionalArticle);
                    }
                }
            }

            //Get the new state of business logic device
            IDevice iDevice = DeviceFactory.Create(updatedDevice, deviceOrderState);

            //Assign new values to selected item from previousViewModel
            NutzeinheitPageViewModel previousViewModel = GetParentProperty();

            DeviceVM currentDeviceVM = previousViewModel.ListOfCurrentDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);
            DeviceVM currentAllDeviceVM = previousViewModel.ListOfAllDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);
            DeviceVM currentRemainingDeviceVM = previousViewModel.ListOfRemainingDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);

            Order currentOrder = orderService.GetOrder(deviceOrderState.OrderGuid);

            if (currentOrder.Number.Contains("#x#"))
            {
                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(updatedDevice.NutzeinheitGuid);
                NutzeinheitOrderState nutzeinheitOrderState = nutzeinheit.OrderStates.Single(x => x.OrderGuid == deviceOrderState.OrderGuid);
                nutzeinheitOrderState.ProcessState = ProcessState.Updating;

                List<DeviceOrderState> backupOrderDeviceOrderStates = nutzeinheit.Devices.SelectMany(x => x.OrderStates).Where(y => y.OrderGuid == deviceOrderState.OrderGuid).ToList();

                if (!backupOrderDeviceOrderStates.Any(x => x.ProcessState == ProcessState.InProgress))
                {
                    nutzeinheitOrderState.CompletedDate = DateTime.Now;
                }
                nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);

                OrderState orderState = currentOrder.OrderState;
                orderState.ProcessState = ProcessState.Updating;
                orderService.UpdateOrderState(orderState);
            }

            //update UI Model in previousVM
            if (currentDeviceVM != null && iDevice.DeviceDeinstallationDate != null)
            {
                previousViewModel.ListOfCurrentDevices.Remove(currentDeviceVM);
                UpdatePreviousVM(currentDeviceVM, iDevice);
            }
            else if (currentDeviceVM == null && iDevice.DeviceDeinstallationDate == null && currentRemainingDeviceVM == null)
            {
                UpdatePreviousVM(currentAllDeviceVM, iDevice);
                previousViewModel.ListOfCurrentDevices.Add(currentAllDeviceVM);
            }
            else if (currentRemainingDeviceVM != null) //update remainingList
            {
                UpdatePreviousVM(currentRemainingDeviceVM, iDevice);
            }
            else
            {
                UpdatePreviousVM(currentAllDeviceVM, iDevice);
            }
        }

        private async Task AddNewDevice()
        {
            Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(nutzeinheitOrder.NutzeinheitGuid);

            Device newDevice = CreateNewDevice(nutzeinheit);

            Signature signature = GetSignatureForThisOrder();

            if (signature != null)
            {
                DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                if (signatureDeleteWarningResult == DialogResponse.Decline)
                {
                    return;
                }
            }

            deviceService.AddDevice(newDevice);
            //get new instance of the new device that contains DeviceCatalog property
            Device createdDevice = deviceService.GetDevice(newDevice.Guid);
            DeviceOrderState deviceOrderState = createdDevice.OrderStates.Single();
            IDevice iDevice = DeviceFactory.Create(createdDevice, deviceOrderState);
            DeviceClass deviceClass = createdDevice.DeviceCatalog.DeviceKind.Class;

            NutzeinheitPageViewModel previousViewModel = GetParentProperty();
            DeviceVM deviceVM = new DeviceVM
            {
                DeviceClass = deviceClass,
                DeviceNumber = iDevice.DeviceNumber,
                DeviceLabel = iDevice.DeviceLabel,
                DeviceRoom = iDevice.DeviceRoom.Label,
                DeviceOngoingNumber = iDevice.DeviceOngoingNumber,
                ParentViewModel = previousViewModel,
                Device = iDevice,
                DeviceUiState = new DeviceUiState
                {
                    DeviceOrderState = iDevice.DeviceOrderState,
                    IsMaintained = createdDevice.IsMaintained
                },
                IsDeviceListEditedOrCreated = true,
                Photos = new List<Photo>(),
                IsDeviceMaintained = createdDevice.IsMaintained
            };

            DeviceVM currentAllDeviceVM = previousViewModel.ListOfAllDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);
            if (currentAllDeviceVM != null)
            {
                previousViewModel.ListOfCurrentDevices.Add(deviceVM);
                previousViewModel.ListOfAllDevices.Add(deviceVM);
            }
            else
            {
                previousViewModel.ListOfRemainingDevices.Add(deviceVM);
            }

            AddNewDevicePhotos(createdDevice);
        }

        private Device CreateNewDevice(Nutzeinheit nutzeinheit)
        {
            Device device = new Device
            {
                NutzeinheitGuid = nutzeinheit.Guid,
                Number = NewDeviceNumber,
                InstallationDate = NewDeviceInstallationDate,
                Note = NewDeviceNote,
                OngoingNumber = OldOngoingNumber,
                Subtraction = false,
                IsMaintained = NewDeviceIsMaintained,
                IsCreatedByApp = true,
                IsLeased = false,
                RoomGuid = NewSelectedRoom.Guid,
                DeviceCatalogGuid = NewSelectedDeviceCatalog.Guid
            };

            if (!string.IsNullOrEmpty(NewDeviceCalibrationDate))
            {
                var cultureInfo = new CultureInfo("de-DE");
                string stringCalibrationDate = "1 Jan " + NewDeviceCalibrationDate;
                DateTime dateTime = DateTime.Parse(stringCalibrationDate, cultureInfo);
                device.CalibrationDate = dateTime;
            }
            else
            {
                device.CalibrationDate = null;
            }

            DeviceOrderState deviceOrderState = CreateDeviceOrderState(device);

            if (deviceOrderState.AmwInfoKeyGuid != null)
            {
                device.IsMaintained = false;
            }

            List<DeviceOrderState> deviceOrderStatesList = new List<DeviceOrderState>
            {
                deviceOrderState
            };

            device.OrderStates = deviceOrderStatesList;

            DeviceConsumption deviceConsumption = CreateDeviceConsumption(device);
            List<DeviceConsumption> deviceConsumptionsList = new List<DeviceConsumption>
            {
                deviceConsumption
            };

            device.DeviceConsumptions = deviceConsumptionsList;

            List<DeviceAdditionalArticle> createdDeviceArticleList = new List<DeviceAdditionalArticle>();
            foreach (AdditionalArticleVM additionalArticleVM in SelectedAdditionalArticleList)
            {
                DeviceAdditionalArticle newArticle = new DeviceAdditionalArticle()
                {
                    DeviceGuid = device.Guid,
                    AdditionalArticleGuid = additionalArticleVM.AdditionalArticle.Guid,
                    Quantity = additionalArticleVM.Amount,
                    IsCreatedByApp = true
                };
                createdDeviceArticleList.Add(newArticle);
            }

            device.DeviceAdditionalArticles = createdDeviceArticleList;

            return device;
        }

        private DeviceOrderState CreateDeviceOrderState(Device device)
        {
            DeviceOrderState deviceOrderState = new DeviceOrderState()
            {
                ProcessState = ProcessState.Creating,
                DeviceGuid = device.Guid,
                OrderGuid = nutzeinheitOrder.OrderGuid,
                OrderKind = DeviceOrderKind.Assembly,
                CompletedDate = DateTime.Now
            };

            if (NewSelectedAmwKey != null && NewSelectedAmwKey.Key != -1)
            {
                deviceOrderState.AmwInfoKeyGuid = NewSelectedAmwKey.Guid;
            }

            return deviceOrderState;
        }

        private DeviceConsumption CreateDeviceConsumption(Device device)
        {
            DeviceConsumption deviceConsumption = new DeviceConsumption()
            {
                DeviceGuid = device.Guid,
                Factor = 0,
                Reading = 0,
                Estimation = EstimationKind.Null,
                State = DeviceConsumptionState.Null,
                IsReconstructed = false,
                Origin = DeviceConsumptionOrigin.Manual,
                ReadindKindGuid = GetGuidForReading()
            };

            if (NewDeviceReading != null)
            {
                deviceConsumption.Reading = NewDeviceReading;
                deviceConsumption.ReadingDate = DateTime.Now;
            }

            return deviceConsumption;
        }

        private Guid GetGuidForReading()
        {
            ReadingKind readingKind = deviceService.GetRauchmelderReadingKind();
            return readingKind.Guid;
        }

        private async Task<DialogResponse> DisplayDeleteSignatureWarning(Signature signature)
        {
            string warning = localisationService.Get("DeleteSignatureWarning");
            DialogResponse result = await dialogService.AcceptDeclineAsync(warning, "JA", "NEIN");

            if (result == DialogResponse.Decline)
            {
                wasDeleteDeclined = true;
                return result;
            }

            DeleteSignature(signature);

            return result;
        }

        private void DeleteSignature(Signature signature)
        {
            if (signature.Path != null)
            {
                signatureService.RemoveSignatureFromDevice(signature);
            }
            signatureService.DeleteSignature(signature);
            SetNutzeinheitStateInProgress();
        }

        private void SetNutzeinheitStateInProgress()
        {
            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitOrder);

            if (nutzeinheitOrderState.ProcessState == ProcessState.InProgress)
            {
                return;
            }

            nutzeinheitOrderState.ProcessState = ProcessState.InProgress;
            nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);
        }

        private void UpdateExistingDeviceArticle(AdditionalArticleVM additionalArticle, DeviceAdditionalArticle existingArticle)
        {
            if (existingArticle.Quantity == additionalArticle.Amount)
            {
                return;
            }
            existingArticle.Quantity = additionalArticle.Amount;
            deviceService.UpdateDeviceAdditonalArticle(existingArticle);
        }

        private void AddNewDeviceArticles(Device device, AdditionalArticleVM additionalArticle)
        {
            DeviceAdditionalArticle newArticle = new DeviceAdditionalArticle()
            {
                DeviceGuid = device.Guid,
                AdditionalArticleGuid = additionalArticle.AdditionalArticle.Guid,
                Quantity = additionalArticle.Amount,
                IsCreatedByApp = true
            };
            deviceService.SaveDeviceAdditonalArticle(newArticle);
        }

        private void UpdatePreviousVM(DeviceVM deviceVM, IDevice iDevice)
        {
            deviceVM.DeviceNumber = iDevice.DeviceNumber;
            deviceVM.DeviceLabel = iDevice.DeviceLabel;
            deviceVM.DeviceRoom = iDevice.DeviceRoom.Label;
            deviceVM.DeviceOngoingNumber = iDevice.DeviceOngoingNumber;
            deviceVM.Device = iDevice;
            deviceVM.IsDeviceListEditedOrCreated = true;

            if (iDevice.DeviceOrderState.AmwInfoKeyGuid != null)
            {
                deviceVM.IsDeviceMaintained = false;
            }
            else
            {
                deviceVM.IsDeviceMaintained = true;
            }
            deviceVM.DeviceUiState = new DeviceUiState
            {
                IsMaintained = deviceVM.IsDeviceMaintained,
                DeviceOrderState = iDevice.DeviceOrderState
            };
        }

        private async void PhotoExecute()
        {
            try
            {
                if (!await CheckIfCanBeSave())
                {
                    return;
                }

                await CrossMedia.Current.Initialize();

                navigationService.ShowInitPageOnResume = false;

                PermissionStatus statusCamera = await Permissions.CheckStatusAsync<Permissions.Camera>();
                PermissionStatus status = await Permissions.CheckStatusAsync<Permissions.StorageWrite>();

                if (statusCamera == PermissionStatus.Denied || status == PermissionStatus.Denied)
                {
                    statusCamera = await Permissions.RequestAsync<Permissions.Camera>();
                    status = await Permissions.RequestAsync<Permissions.StorageWrite>();
                }
                if (statusCamera == PermissionStatus.Denied || status == PermissionStatus.Denied) return;

                MediaFile picture = await CrossMedia.Current.TakePhotoAsync(new StoreCameraMediaOptions()
                {
                    CompressionQuality = 20,
                    PhotoSize = PhotoSize.Medium,
                    SaveToAlbum = false,
                    AllowCropping = false
                });

                if (picture != null)
                {
                    string photoPath = photoService.SaveDevicePhoto(picture.Path, selectedDevice.DeviceGuid);
                    Photo photo = new Photo()
                    {
                        Name = Path.GetFileName(photoPath),
                        Path = photoPath,
                        RecordedDate = DateTime.Now,
                        CreatedByApp = true,
                        DeviceGuid = selectedDevice.DeviceGuid
                    };

                    photoService.SavePhotoToDb(photo);

                    //Update listOfPhotos
                    UpdatePreviousModelPhotos(photo, selectedDevice.DeviceGuid);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to capture a picture in OrderPageVm!");
                throw;
            }
            finally
            {
                navigationService.ShowInitPageOnResume = true;
            }
        }

        private void UpdatePreviousModelPhotos(Photo photo, Guid deviceGuid)
        {
            NutzeinheitPageViewModel parent = GetParentProperty();
            ObservableCollection<DeviceVM> listOfAllDevices = parent.ListOfAllDevices;
            ObservableCollection<DeviceVM> listCurrentRemainingDeviceVM = parent.ListOfRemainingDevices;
            
            DeviceVM deviceInAllDeviceListVM = listOfAllDevices.Where(x => x.Device.DeviceGuid == deviceGuid).FirstOrDefault();
            DeviceVM deviceInRemainingDeviceListVM = listCurrentRemainingDeviceVM.FirstOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);

            if (deviceInAllDeviceListVM != null)
            {
                deviceInAllDeviceListVM.Photos.Add(photo);
            }

            if (deviceInRemainingDeviceListVM != null)
            {
                deviceInRemainingDeviceListVM.Photos.Add(photo);
            }
        }

        private async void ReadBarcodExecute()
        {
            try
            {
                PermissionStatus statusCamera = await Permissions.CheckStatusAsync<Permissions.Camera>();

                if (statusCamera == PermissionStatus.Denied)
                {
                    statusCamera = await Permissions.RequestAsync<Permissions.Camera>();
                }
                if (statusCamera == PermissionStatus.Denied) return;

                var barcodeScannerResult = await scannerService.ReadBarcode();
                navigationService.ShowInitPageOnResume = false;

                if (barcodeScannerResult != null)
                {
                    AssignBarcodeValues(barcodeScannerResult.Text, "old");
                }
            }
            catch (NotSupportedException exception)
            {
                logger.Error(exception, "Your system does not support this operaton. Please check if you have an available Camera to perform this opertation.");
                string dialogMessage = localisationService.Get("BarcodeFehlar");
                await dialogService.AcceptAsync(dialogMessage, "OK");
            }
            catch (Exception exception)
            {
                logger.Error(exception, "Error attempting to read Barcode!");
                throw;
            }
        }

        private void AssignBarcodeValues(string barcode, string deviceTyp)
        {
            if (deviceTyp == "old")
            {
                if (barcode.Contains(";"))
                {
                    string[] splittedQrCode;
                    splittedQrCode = barcode.Split(new[] { ";" }, StringSplitOptions.None);
                    OldDeviceNumber = splittedQrCode[1];
                    OldDeviceNote = barcode;
                }
                else if (barcode.Contains("\r\n"))
                {
                    string[] splittedQrCode;
                    splittedQrCode = barcode.Split(new[] { "\r\n" }, StringSplitOptions.None);
                    OldDeviceNumber = splittedQrCode[0];
                    OldDeviceNote = barcode;
                }
                else
                {
                    OldDeviceNumber = barcode;
                }
            }
            else
            {
                if (barcode.Contains(";"))
                {
                    string[] splittedQrCode;
                    splittedQrCode = barcode.Split(new[] { ";" }, StringSplitOptions.None);
                    NewDeviceNumber = splittedQrCode[1];
                    NewDeviceNote = barcode;
                }
                else if (barcode.Contains("\r\n"))
                {
                    string[] splittedQrCode;
                    splittedQrCode = barcode.Split(new[] { "\r\n" }, StringSplitOptions.None);
                    NewDeviceNumber = splittedQrCode[0];
                    NewDeviceNote = barcode;
                }
                else
                {
                    NewDeviceNumber = barcode;
                }
            }
        }

        private void AddArticleExecute(AdditionalArticle additionalArticle)
        {
            try
            {
                if (additionalArticle == null)
                {
                    return;
                }

                decimal articlesToAdd = Convert.ToDecimal(AdditionalArticlesNumber, CultureInfo.CurrentCulture);

                AdditionalArticleVM additionalArticleVM = new AdditionalArticleVM()
                {
                    AdditionalArticle = additionalArticle,
                    Amount = articlesToAdd,
                    IsCreatedByApp = true
                };
                SelectedAdditionalArticleList.Add(additionalArticleVM);
                AdditionalArticleList.Remove(additionalArticle);
                SelectedAdditionalArticle = null;
                SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to add an article to list!");
                throw;
            }
        }

        private void RemoveArticleExecute(AdditionalArticleVM additionalArticle)
        {
            try
            {
                if (additionalArticle == null)
                {
                    return;
                }

                SelectedAdditionalArticleList.Remove(additionalArticle);
                AdditionalArticleList.Add(additionalArticle.AdditionalArticle);
                SelectedToRemoveAdditionalAricle = null;
                SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to remove an article from list!");
                throw;
            }
        }

        private void SetInstallationDateExecute()
        {
            try
            {
                OldDeviceInstallationDate = DateTime.Now;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to set installation date to todays date!");
                throw;
            }
        }

        private void SetDeinstallationDateExecute()
        {
            try
            {
                OldDeviceDeinstallationDate = DateTime.Now;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to set deinstallation date to todays date!");
                throw;
            }
        }

        private void AddNewArticleExecute(AdditionalArticle additionalArticle)
        {
            try
            {
                if (additionalArticle == null)
                {
                    return;
                }

                decimal articlesToAdd = Convert.ToDecimal(NewAdditionalArticlesNumber, CultureInfo.CurrentCulture);

                AdditionalArticleVM additionalArticleVM = new AdditionalArticleVM()
                {
                    AdditionalArticle = additionalArticle,
                    Amount = articlesToAdd,
                    IsCreatedByApp = true
                };
                NewSelectedAdditionalArticleList.Add(additionalArticleVM);

                NewAdditionalArticleList.Remove(additionalArticle);
                NewSelectedAdditionalArticle = null;
                NewSelectedAdditionalArticleListCount = NewSelectedAdditionalArticleList.Count;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to add an article to list!");
                throw;
            }
        }

        private void RemoveNewArticleExecute(AdditionalArticleVM additionalArticle)
        {
            try
            {
                if (additionalArticle == null)
                {
                    return;
                }

                NewSelectedAdditionalArticleList.Remove(additionalArticle);
                NewAdditionalArticleList.Add(additionalArticle.AdditionalArticle);
                NewSelectedToRemoveAdditionalAricle = null;
                NewSelectedAdditionalArticleListCount = NewSelectedAdditionalArticleList.Count;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to remove an article from list!");
                throw;
            }
        }

        private async void NewReadBarcodExecute()
        {
            try
            {
                var barcodeScannerResult = await scannerService.ReadBarcode();
                navigationService.ShowInitPageOnResume = false;

                if (barcodeScannerResult != null)
                {
                    AssignBarcodeValues(barcodeScannerResult.Text, "new");
                }
            }
            catch (NotSupportedException exception)
            {
                logger.Error(exception, "Your system does not support this operaton. Please check if you have an available Camera to perform this opertation.");
                string dialogMessage = localisationService.Get("BarcodeFehlar");
                await dialogService.AcceptAsync(dialogMessage, "OK");
            }
            catch (Exception exception)
            {
                logger.Error(exception, "Error attempting to read Barcode!");
                throw;
            }
        }

        private async void NewPhotoExecute()
        {
            try
            {
                if (!await CheckIfCanBeSave())
                {
                    return;
                }

                DialogResponse dialogResponse = await dialogService.AcceptDeclineAsync("Die gemachten Fotos werden nur dann gespeichert, wenn das neu angelegte Gerät gespeichert wird.", "OK", "Abbrechen");

                if (dialogResponse == DialogResponse.Decline)
                {
                    return;
                }

                await CrossMedia.Current.Initialize();
                navigationService.ShowInitPageOnResume = false;
                PermissionStatus statusCamera = await Permissions.CheckStatusAsync<Permissions.Camera>();
                PermissionStatus status = await Permissions.CheckStatusAsync<Permissions.StorageWrite>();

                if (statusCamera == PermissionStatus.Denied || status == PermissionStatus.Denied)
                {
                    statusCamera = await Permissions.RequestAsync<Permissions.Camera>();
                    status = await Permissions.RequestAsync<Permissions.StorageWrite>();
                }
                if (statusCamera == PermissionStatus.Denied || status == PermissionStatus.Denied) return;

                MediaFile picture = await CrossMedia.Current.TakePhotoAsync(new StoreCameraMediaOptions()
                {
                    CompressionQuality = 20,
                    PhotoSize = PhotoSize.Medium,
                    SaveToAlbum = false,
                    AllowCropping = false
                });

                NewDevicePictues.Add(picture);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to capture a picture in OrderPageVm!");
                throw;
            }
            finally
            {
                navigationService.ShowInitPageOnResume = true;
            }
        }

        private void AddNewDevicePhotos(Device createdDevice)
        {
            if (createdDevice == null)
            {
                return;
            }

            foreach (MediaFile picture in NewDevicePictues)
            {
                string photoPath = photoService.SaveDevicePhoto(picture.Path, createdDevice.Guid);
                Photo photo = new Photo()
                {
                    Name = Path.GetFileName(photoPath),
                    Path = photoPath,
                    RecordedDate = DateTime.Now,
                    CreatedByApp = true,
                    DeviceGuid = createdDevice.Guid
                };

                photoService.SavePhotoToDb(photo);
                UpdatePreviousModelPhotos(photo, createdDevice.Guid);
            }
        }

        private async void ConfirmOldDeviceExecute()
        {
            try
            {
                if (OldDeviceDeinstallationDate == null && OldSelectedAmwKey == null)
                {
                    ShowMaintainOrErrorWarning = true;
                    return;
                }
                else
                {
                    ShowMaintainOrErrorWarning = false;
                }

                if (!ValidateOldDevice())
                {
                    return;
                }

                if (OldDeviceDeinstallationDate == null && OldSelectedAmwKey != null)
                {
                    ShowMaintainOrErrorWarning = false;

                    await UpdateOldDeviceState();
                    NutzeinheitPageViewModel previousVM = GetParentProperty();
                    previousVM.SelectedDevice = null;
                    await navigationService.GoBackAsync();
                }

                TabView.SelectedIndex = 1;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to confirm current device in WatermeterExchangePage!");
                throw;
            }
        }

        private async void ChangeDeviceOrderKindExecute()
        {
            try
            {
                DeviceOrderKind selectedDeviceOrderKind = await deviceOrderKindChangeDialog.ShowChangeDeviceOrderKindDialog(DeviceOrderKind.Exchange);

                if (selectedDeviceOrderKind == DeviceOrderKind.Exchange)
                {
                    return;
                }

                DialogResponse dialogResponse = await dialogService.AcceptDeclineAsync("Sie wollen sicher einen Gerätetausch durchführen?", "Ja", "Nein");

                if (dialogResponse == DialogResponse.Decline)
                {
                    return;
                }

                selectedDevice.DeviceOrderState.OrderKind = selectedDeviceOrderKind;
                currentDeviceOrderState.OrderKind = selectedDeviceOrderKind;
                deviceService.UpdateDeviceOrderState(currentDeviceOrderState);

                UpdatePreviousPageAfterOrderKindChange();

                await navigationService.GoBackAsync();
                await Task.Delay(200);
                await navigationService.NavigateToAsync<RauchmelderEditPageViewModel>(ParentViewModel);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while atteption to change deviceOrderKind of the selected Device");
                throw;
            }
        }

        private void UpdatePreviousPageAfterOrderKindChange()
        {
            NutzeinheitPageViewModel previousViewModel = GetParentProperty();

            DeviceVM currentDeviceVM = previousViewModel.ListOfCurrentDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);
            DeviceVM currentAllDeviceVM = previousViewModel.ListOfAllDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);
            DeviceVM currentRemainingDeviceVM = previousViewModel.ListOfRemainingDevices.SingleOrDefault(x => x.Device.DeviceGuid == selectedDevice.DeviceGuid);

            if (currentDeviceVM != null)
            {
                currentDeviceVM.DeviceUiState = new DeviceUiState
                {
                    IsMaintained = currentDeviceVM.IsDeviceMaintained,
                    DeviceOrderState = currentDeviceOrderState
                };
            }

            if (currentRemainingDeviceVM != null)
            {
                currentRemainingDeviceVM.DeviceUiState = new DeviceUiState
                {
                    IsMaintained = currentRemainingDeviceVM.IsDeviceMaintained,
                    DeviceOrderState = currentDeviceOrderState
                };
            }

            if (currentAllDeviceVM != null)
            {
                currentAllDeviceVM.DeviceUiState = new DeviceUiState
                {
                    IsMaintained = currentAllDeviceVM.IsDeviceMaintained,
                    DeviceOrderState = currentDeviceOrderState
                };
            }
        }

        private bool Validate()
        {
            var validator = new RauchmelderExchangePageViewModelValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }

        private bool ValidateOldDevice()
        {
            var validator = new RauchmelderExchangeOldDeviceValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }

        #endregion
    }
}
