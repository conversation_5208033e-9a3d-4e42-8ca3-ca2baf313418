﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:inputLayout="clr-namespace:Syncfusion.XForms.TextInputLayout;assembly=Syncfusion.Core.XForms"
             xmlns:local="clr-namespace:Eras2AmwApp.CustomControls"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.NutzerQuadratmeterPage">
    <ContentPage.Content>

        <ScrollView>

            <Grid RowSpacing="0">

                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="70*"></ColumnDefinition>
                    <ColumnDefinition Width="10*"></ColumnDefinition>
                    <ColumnDefinition Width="10*"></ColumnDefinition>
                    <ColumnDefinition Width="10*"></ColumnDefinition>
                </Grid.ColumnDefinitions>

                <Label  x:Name="NutzerQuadratmeter"
                        Margin="5"
                        Grid.Row="0"
                        Grid.Column="0"
                        Grid.ColumnSpan="4"
                        Text="Nutzer Quadratmeter"
                        FontSize="Large"
                        FontAttributes="Bold"
                        HorizontalOptions="Center"
                        TextColor="Black">
                </Label>

                <Frame  x:Name="CurrentNutzerQuadratmeterFrame"
                        Margin="5"
                        Grid.Row="1"
                        Grid.Column="0"
                        Grid.ColumnSpan="4"
                        BorderColor="Gray"
                        CornerRadius="5"
                        HasShadow="True">

                    <Grid RowSpacing="0">

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <inputLayout:SfTextInputLayout  x:Name="Hzg"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Qm Hzg"
                                                        ContainerType="Outlined"
                                                        Grid.Row="0"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding QuadratmeterHzg}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Ww"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Qm Ww"
                                                        ContainerType="Outlined"
                                                        Grid.Row="0"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding QuadratmeterWw}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="Nk"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Qm Nk"
                                                        ContainerType="Outlined"
                                                        Grid.Row="0"
                                                        Grid.Column="2"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding QuadratmeterNk}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerQuadratmeterVon"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Von Datum"
                                                        ContainerType="Outlined"
                                                        Grid.Row="1"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <DatePicker Date="{Binding NutzerQuadratmeterFromDate}"
                                        VerticalOptions="End"
                                        FontSize="Medium">

                            </DatePicker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerQuadratmeterBis"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Bis Datum"
                                                        ContainerType="Outlined"
                                                        Grid.Row="1"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center">

                            <local:NullableDatePicker   NullableDate="{Binding NutzerQuadratmeterToDate}"
                                                        VerticalOptions="End"
                                                        FontSize="Medium">

                            </local:NullableDatePicker>

                        </inputLayout:SfTextInputLayout>

                        <ImageButton    Source="saveIcon.png"
                                        BackgroundColor="Transparent"
                                        Grid.Row="0"
                                        Grid.Column="3"
                                        HorizontalOptions="Start"
                                        Margin="5,0,5,5"
                                        HeightRequest="40"
                                        WidthRequest="40"
                                        Command="{Binding SaveNutzerQuadratmeterCommand} ">

                        </ImageButton>

                        <ImageButton    Source="newIcon.png"
                                        BackgroundColor="Transparent"
                                        Grid.Row="0"
                                        Grid.Column="4"
                                        HorizontalOptions="Center"
                                        Margin="5,0,5,5"
                                        HeightRequest="40"
                                        WidthRequest="40"
                                        Command="{Binding AddNutzerQuadratmeterCommand} ">

                        </ImageButton>

                        <ImageButton    Source="forwardIcon.png"
                                        BackgroundColor="Transparent"
                                        Grid.Row="1"
                                        Grid.Column="3"
                                        HorizontalOptions="Start"
                                        Margin="5,0,5,5"
                                        HeightRequest="40"
                                        WidthRequest="40"
                                        IsEnabled="{Binding IsLastDateNull}"
                                        IsVisible="{Binding IsLastDateNull}"
                                        Command="{Binding NewEmptyNutzerQuadratmeterCommand} ">

                        </ImageButton>

                        <ImageButton    Source="deleteIcon.png"
                                        Grid.Row="1"
                                        Grid.Column="4"
                                        BackgroundColor="Transparent"
                                        HorizontalOptions="End"
                                        Margin="5,0,5,5"
                                        WidthRequest="40"
                                        HeightRequest="40"
                                        IsEnabled="{Binding CanNutzerQuadradmeterBeDeleted}"
                                        IsVisible="{Binding CanNutzerQuadradmeterBeDeleted}"
                                        Command="{Binding DeleteNutzerQuadratmeterCommand}">

                        </ImageButton>

                    </Grid>

                </Frame>

                <Label  x:Name="NutzerQuadratmeterList"
                        Margin="5"
                        Grid.Row="2"
                        Grid.Column="0"
                        Grid.ColumnSpan="4"
                        Text="Nutzer Quadratmeter Liste"
                        FontSize="Large"
                        FontAttributes="Bold"
                        HorizontalOptions="Center"
                        TextColor="Black">
                </Label>

                <ListView   x:Name="NutzerQuadratmeterListView"
                            Margin="10,0,10,0"
                            Grid.Row="3"
                            Grid.Column="0"
                            Grid.ColumnSpan="4"
                            ItemsSource="{Binding NutzerQuadratmeterObsCollec}"
                            SelectionMode="Single"
                            SelectedItem="{Binding SelectedNutzerQuadratmeter, Mode=TwoWay}"
                            IsPullToRefreshEnabled="False"
                            HasUnevenRows="True"
                            CachingStrategy="RecycleElement">
                    <ListView.ItemTemplate>
                        <DataTemplate x:Name="NutzerQuadratmeterListElement">
                            <ViewCell>
                                <Frame  Margin="2"
                                        Padding="0"
                                        BorderColor="LightGray"
                                        CornerRadius="0"
                                        HasShadow="True">

                                    <Grid RowSpacing="0" ColumnSpacing="0">

                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="50"></RowDefinition>
                                        </Grid.RowDefinitions>

                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                                        </Grid.ColumnDefinitions>

                                        <Label  x:Name="NutzerQuadratmeterHzg"
                                                Grid.Row="0"
                                                Grid.Column="0"
                                                Margin="5,0,30,0"
                                                Text="{Binding SquareMeters_Hzg, StringFormat='Qm Hzg: {0}'}"
                                                TextColor="Black"
                                                FontAttributes="Bold"
                                                FontSize="Small">

                                        </Label>

                                        <Label  x:Name="NutzerQuadratmeterWw"
                                                Grid.Row="0"
                                                Grid.Column="1"
                                                Margin="5,0,30,0"
                                                Text="{Binding SquareMeters_Ww, StringFormat='Qm Ww: {0}'}"
                                                TextColor="Black"
                                                FontAttributes="Bold"
                                                FontSize="Small">

                                        </Label>

                                        <Label  x:Name="NutzerQuadratmeterNk"
                                                Grid.Row="0"
                                                Grid.Column="2"
                                                Margin="5,0,30,0"
                                                Text="{Binding SquareMeters_Nk, StringFormat='Qm Nk: {0}'}"
                                                TextColor="Black"
                                                FontAttributes="Bold"
                                                FontSize="Small">

                                        </Label>

                                        <Label  x:Name="NutzerQuadratmeterFromDate"
                                                Grid.Row="0"
                                                Grid.Column="3"
                                                Margin="5,0,5,0"
                                                Text="{Binding RangeFrom, StringFormat='Von: {0:dd.MM.yyyy}'}"
                                                TextColor="Black"
                                                FontAttributes="Bold"
                                                FontSize="Small">

                                        </Label>

                                        <Label  x:Name="NutzerQuadratmeterToDate"
                                                Grid.Row="0"
                                                Grid.Column="4"
                                                Margin="5,0,5,0"
                                                Text="{Binding RangeTo, StringFormat='Bis: {0:dd.MM.yyyy}', TargetNullValue='Bis: N/A'}"
                                                TextColor="Black"
                                                FontAttributes="Bold"
                                                FontSize="Small">

                                        </Label>
                                        
                                    </Grid>
                                </Frame>
                            </ViewCell>
                        </DataTemplate>
                    </ListView.ItemTemplate>

                </ListView>

            </Grid>
        </ScrollView>
    </ContentPage.Content>
</ContentPage>