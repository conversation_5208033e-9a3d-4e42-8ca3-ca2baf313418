﻿//  <copyright file="NutzeinheitUiState.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Models
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;

    public struct NutzeinheitUiState
    {
        public List<DeviceOrderState> OrderStates { get; set; }

        public ProcessState NutzeinheitProcessState { get; set; }

        public bool HasSignature { get; set; }

        public Guid? AmwInfoKeyGuid { get; set; }

        public NutzeinheitUiState(List<DeviceOrderState> orderStates, bool hasSignature, ProcessState nutzeinheitProcessState, Guid amwInfoKeyGuid)
        {
            OrderStates = orderStates;
            HasSignature = hasSignature;
            NutzeinheitProcessState = nutzeinheitProcessState;
            AmwInfoKeyGuid = amwInfoKeyGuid;
        }
    }
}
