﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceClassTypeConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class DeviceClassTypeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string imageName = "";

            if (value is DeviceClass deviceClass)
            {
                switch (deviceClass)
                {
                    case DeviceClass.RM:
                        imageName = "firedetector.png";
                        break;
                    case DeviceClass.KWZ:
                        imageName = "coldmeter.png";
                        break;
                    case DeviceClass.WWZ:
                        imageName = "warmmeter.png";
                        break;
                    case DeviceClass.WMZ:
                        imageName = "wmzmeter.png";
                        break;
                    case DeviceClass.HKV:
                        imageName = "hkve.png";
                        break;
                    case DeviceClass.SZ:
                        imageName = "szmeter.png";
                        break;
                    default:
                        imageName = "";
                        break;
                }
            }

            return imageName;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
