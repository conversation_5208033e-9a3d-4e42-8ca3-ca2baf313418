﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;

    public class PersonService : IPersonService
    {
        private readonly IDbContextFactory contextFactory;

        public PersonService(IDbContextFactory contextFactory)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public List<Salutation> GetSalutations()
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Salutations.ToList();
            }
        }

        public List<Title> GetTitles()
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Titles.ToList();
            }
        }

        public Title GetTitle(int id)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Titles .Where(x => x.Id == id).SingleOrDefault();
            }
        }

        public Salutation GetSalutation(int id)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Salutations.Where(x => x.Id == id).SingleOrDefault();
            }
        }

        public Person GetPerson(Guid personGuid)
        {
            if (personGuid == null)
            {
                throw new ArgumentNullException(nameof(personGuid));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Persons
                    .Include(x => x.PersonCommunications)
                    .ThenInclude(PersonCommunications => PersonCommunications.CommunicationFeature)
                    .Where(x => x.Guid == personGuid).SingleOrDefault();
            }
        }

        public void UpdatePerson(Person person)
        {
            if (person == null)
            {
                throw new ArgumentNullException(nameof(person));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(person);
                context.Entry(person).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public Dictionary<CommunicationKind, string> GetPersonContacts(List<PersonCommunication> personCommunications)
        {
            if (personCommunications == null)
            {
                throw new ArgumentNullException(nameof(personCommunications));
            }

            Dictionary<CommunicationKind, string> PersonContactMethods = new Dictionary<CommunicationKind, string>();

            foreach (PersonCommunication personCommunication in personCommunications)
            {
                string contactValue = personCommunication.Address ?? string.Empty;
                CommunicationKind contactType = personCommunication.CommunicationFeature.Kind;
                if (!PersonContactMethods.ContainsKey(contactType))
                {
                    PersonContactMethods.Add(contactType, contactValue);
                }
            }

            return PersonContactMethods;
        }

        public Dictionary<CommunicationKind, string> GetPersonContacts(List<NutzerCommunication> nutzerCommunications)
        {
            if (nutzerCommunications == null)
            {
                throw new ArgumentNullException(nameof(nutzerCommunications));
            }

            Dictionary<CommunicationKind, string> PersonContactMethods = new Dictionary<CommunicationKind, string>();

            foreach (NutzerCommunication nutzerCommunication in nutzerCommunications)
            {
                string contactValue = nutzerCommunication.Address ?? string.Empty;
                CommunicationKind contactType = nutzerCommunication.CommunicationFeature.Kind;

                if (!PersonContactMethods.ContainsKey(contactType))
                {
                    PersonContactMethods.Add(contactType, contactValue);
                }
            }

            return PersonContactMethods;
        }

        public string GetPersonMainCommunicationValue(List<NutzerCommunication> nutzerCommunications)
        {
            if (nutzerCommunications == null)
            {
                throw new ArgumentNullException(nameof(nutzerCommunications));
            }

            NutzerCommunication contact = nutzerCommunications.Where(x => x.CommunicationFeature.Kind == CommunicationKind.Mobil).FirstOrDefault();

            if (contact == null)
            {
                contact = nutzerCommunications.Where(x => x.CommunicationFeature.Kind == CommunicationKind.Telefon).FirstOrDefault();
                if (contact == null)
                {
                    contact = nutzerCommunications.FirstOrDefault();
                    if (contact == null)
                    {
                        return string.Empty;
                    }
                }
            }

            return contact.Address;
        }

        public string GetPersonMainCommunicationValue(List<PersonCommunication> personCommunications)
        {
            if (personCommunications == null)
            {
                throw new ArgumentNullException(nameof(personCommunications));
            }

            PersonCommunication contact = personCommunications.Where(x => x.CommunicationFeature.Kind == CommunicationKind.Mobil).FirstOrDefault();

            if (contact == null)
            {
                contact = personCommunications.Where(x => x.CommunicationFeature.Kind == CommunicationKind.Telefon).FirstOrDefault();
                if (contact == null)
                {
                    contact = personCommunications.FirstOrDefault();
                    if (contact == null)
                    {
                        return string.Empty;
                    }
                }
            }

            return contact.Address;
        }

        public void UpdatePersonCommunication(PersonCommunication personCommunication)
        {
            if (personCommunication == null)
            {
                throw new ArgumentNullException(nameof(personCommunication));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(personCommunication);
                context.Entry(personCommunication).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void AddPersonCommunication(PersonCommunication personCommunication)
        {
            if (personCommunication == null)
            {
                throw new ArgumentNullException(nameof(personCommunication));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.PersonCommunications.Add(personCommunication);
                context.SaveChanges();
            }
        }

        public CommunicationFeature GetCommunicationFeature(CommunicationKind kind)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.CommunicationFeatures.Where(x => x.Kind == kind).Single();
            }
        }
    }
}
