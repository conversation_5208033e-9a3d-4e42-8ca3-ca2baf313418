﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:cards="clr-namespace:Syncfusion.XForms.Cards;assembly=Syncfusion.Cards.XForms"
             xmlns:buttons="clr-namespace:Syncfusion.XForms.Buttons;assembly=Syncfusion.Buttons.XForms"
             xmlns:behaviors="clr-namespace:Eras2AmwApp.Behaviors"
             xmlns:busyindicator="clr-namespace:Syncfusion.SfBusyIndicator.XForms;assembly=Syncfusion.SfBusyIndicator.XForms"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.PicturePreviewPage">
    
    <ContentPage.Content>
        <AbsoluteLayout x:Name="MainListViewLayout">
            
            <Grid x:Name="ListViewGrid" AbsoluteLayout.LayoutBounds="1,1,1,1" AbsoluteLayout.LayoutFlags="All">

                <Grid.RowDefinitions>
                    <RowDefinition Height="80*"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="5*"></RowDefinition>
                </Grid.RowDefinitions>

                <cards:SfCardLayout BindableLayout.ItemsSource="{Binding StackPhotos}"
                                    Grid.Row="0"
                                    x:Name="currentImage"
                                    SwipeDirection="Left"
                                    ShowSwipedCard="true"
                                    BackgroundColor="#F0F0F0">

                    <cards:SfCardLayout.Behaviors>
                        <behaviors:EventToCommandBehavior EventName="VisibleCardIndexChanged"
                                                          Command="{Binding PictureChangeCommand}"
                                                          CommandParameter="{Binding Source={x:Reference currentImage}}">
                        </behaviors:EventToCommandBehavior>
                    </cards:SfCardLayout.Behaviors>

                    <BindableLayout.ItemTemplate>
                        <DataTemplate>
                            <cards:SfCardView>
                                <Image Aspect="AspectFill"
                                   Source="{Binding Source}"></Image>
                            </cards:SfCardView>
                        </DataTemplate>
                    </BindableLayout.ItemTemplate>
                    
                </cards:SfCardLayout>

                <Label  Margin="15,5,5,0"
                        Grid.Row="1"
                        HorizontalOptions="Start"
                        FontAttributes="Bold"
                        Text="{Binding PhotoInformation}">

                </Label>

                <buttons:SfButton   Margin="5,5,5,0"
                                    Grid.Row="2"
                                    Text="Löschen"
                                    Command="{Binding DeleteCardCommand}"
                                    CommandParameter="{Binding Source={x:Reference currentImage}}"
                                    CornerRadius="20"
                                    BorderWidth="1">
                    <VisualStateManager.VisualStateGroups>
                        <VisualStateGroup x:Name="CommonStates">
                            <VisualState x:Name="Disabled">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="LightGray" />
                                </VisualState.Setters>
                            </VisualState>
                            <VisualState x:Name="Normal">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="Red" />
                                </VisualState.Setters>
                            </VisualState>
                        </VisualStateGroup>

                    </VisualStateManager.VisualStateGroups>
                </buttons:SfButton>

                <buttons:SfButton   Margin="5,5,5,0"
                                    Grid.Row="3"
                                    Text="Download"
                                    Command="{Binding DownloadPictureCommand}"
                                    CornerRadius="20"
                                    BorderWidth="1">

                    <VisualStateManager.VisualStateGroups>
                        <VisualStateGroup x:Name="CommonStates">
                            <VisualState x:Name="Disabled">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="LightGray" />
                                </VisualState.Setters>
                            </VisualState>
                            <VisualState x:Name="Normal">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="#538EEC" />
                                </VisualState.Setters>
                            </VisualState>
                        </VisualStateGroup>
                    </VisualStateManager.VisualStateGroups>
                </buttons:SfButton>
                
            </Grid>

            <busyindicator:SfBusyIndicator  x:Name="busyIndicatorRecord" 
                                            AbsoluteLayout.LayoutBounds=".5,.4,200,200" 
                                            AbsoluteLayout.LayoutFlags="PositionProportional"
                                            AnimationType="Globe" 
                                            IsBusy="{Binding IsDownloadingData}"
                                            ViewBoxWidth="80" 
                                            ViewBoxHeight="80" 
                                            TextSize="15"
                                            Title="Wird heruntergeladen..."
                                            TitlePlacement="Bottom"/>
            
        </AbsoluteLayout>
    </ContentPage.Content>
</ContentPage>