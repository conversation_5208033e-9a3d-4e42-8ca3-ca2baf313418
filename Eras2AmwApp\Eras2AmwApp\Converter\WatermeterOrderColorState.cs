﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="WatermeterOrderColorState.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Eras2AmwApp.BusinessLogic.Models;
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class WatermeterOrderColorState : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            DeviceUiState uiState = (DeviceUiState)value;
            Color deviceOrderColor;

            Guid? amwInfoKey = uiState.DeviceOrderState.AmwInfoKeyGuid;

            deviceOrderColor = uiState.DeviceOrderState.OrderKind == Domain.Eras2Amw.Enums.DeviceOrderKind.Inspection
                ? (amwInfoKey == null && uiState.DeviceOrderState.ProcessState == Domain.Eras2Amw.Enums.ProcessState.InProgress) ? Color.LightGray
                : (amwInfoKey != null) ? Color.Red
                : (uiState.DeviceOrderState.ProcessState != Domain.Eras2Amw.Enums.ProcessState.InProgress && uiState.IsMaintained) ? Color.Green
                : Color.LightGray
                : (amwInfoKey == null && uiState.DeviceOrderState.ProcessState == Domain.Eras2Amw.Enums.ProcessState.InProgress) ? Color.LightGray
                : (amwInfoKey != null) ? Color.Red
                : Color.Green;

            return deviceOrderColor;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
