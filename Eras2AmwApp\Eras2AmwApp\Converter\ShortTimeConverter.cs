﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ShortTimeConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class ShortTimeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
            {
                return null;
            }

            return value is DateTime dateTime ? dateTime.ToShortTimeString() : value.ToString();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
