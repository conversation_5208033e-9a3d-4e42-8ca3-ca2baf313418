﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:inputLayout="clr-namespace:Syncfusion.XForms.TextInputLayout;assembly=Syncfusion.Core.XForms"
             xmlns:local="clr-namespace:Eras2AmwApp.CustomControls"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.NutzerPersonenPage">

    <ContentPage.Content>
        
        <ScrollView>
            
            <Grid RowSpacing="0">
                
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="70*"></ColumnDefinition>
                    <ColumnDefinition Width="10*"></ColumnDefinition>
                    <ColumnDefinition Width="10*"></ColumnDefinition>
                    <ColumnDefinition Width="10*"></ColumnDefinition>
                </Grid.ColumnDefinitions>

                <Label  x:Name="NutzerPersonen"
                        Margin="5"
                        Grid.Row="0"
                        Grid.Column="0"
                        Grid.ColumnSpan="4"
                        Text="Nutzer Personen"
                        FontSize="Large"
                        FontAttributes="Bold"
                        HorizontalOptions="Center"
                        TextColor="Black">
                </Label>

                <Frame  x:Name="CurrentNutzerPersonFrame"
                        Margin="5"
                        Grid.Row="1"
                        Grid.Column="0"
                        Grid.ColumnSpan="4"
                        BorderColor="Gray"
                        CornerRadius="5"
                        HasShadow="True">

                    <Grid RowSpacing="0">

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <inputLayout:SfTextInputLayout  x:Name="Personen"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Personen"
                                                        ContainerType="Outlined"
                                                        Grid.Row="0"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <Entry  Text="{Binding NutzerPersonenNumber}"
                                    VerticalOptions="End"
                                    FontSize="Medium">

                            </Entry>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerPersonenVon"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Von Datum"
                                                        ContainerType="Outlined"
                                                        Grid.Row="1"
                                                        Grid.Column="0"
                                                        VerticalOptions="Center">

                            <DatePicker Date="{Binding NutzerPersonenFromDate}"
                                        VerticalOptions="End"
                                        FontSize="Medium">

                            </DatePicker>

                        </inputLayout:SfTextInputLayout>

                        <inputLayout:SfTextInputLayout  x:Name="NutzerPersonenBis"
                                                        InputViewPadding="5"
                                                        Margin="0"
                                                        Hint="Bis Datum"
                                                        ContainerType="Outlined"
                                                        Grid.Row="1"
                                                        Grid.Column="1"
                                                        VerticalOptions="Center">

                            <local:NullableDatePicker   NullableDate="{Binding NutzerPersonenToDate}"
                                                        VerticalOptions="End"
                                                        FontSize="Medium">  

                            </local:NullableDatePicker>

                        </inputLayout:SfTextInputLayout>

                        <ImageButton    Source="saveIcon.png"
                                        BackgroundColor="Transparent"
                                        Grid.Row="0"
                                        Grid.Column="3"
                                        HorizontalOptions="End"
                                        Margin="5,0,5,5"
                                        HeightRequest="40"
                                        WidthRequest="40"
                                        Command="{Binding SaveNutzerPersonenCommand} ">

                        </ImageButton>

                        <ImageButton    Source="newIcon.png"
                                        BackgroundColor="Transparent"
                                        Grid.Row="0"
                                        Grid.Column="4"
                                        HorizontalOptions="End"
                                        Margin="5,0,5,5"
                                        HeightRequest="40"
                                        WidthRequest="40"
                                        Command="{Binding AddNutzerPersonenCommand} ">

                        </ImageButton>

                        <ImageButton    Source="forwardIcon.png"
                                        BackgroundColor="Transparent"
                                        Grid.Row="1"
                                        Grid.Column="3"
                                        HorizontalOptions="End"
                                        Margin="5,0,5,5"
                                        HeightRequest="40"
                                        WidthRequest="40"
                                        IsEnabled="{Binding IsLastDateNull}"
                                        IsVisible="{Binding IsLastDateNull}"
                                        Command="{Binding NewEmptyNutzerPersonenCommand} ">

                        </ImageButton>

                        <ImageButton    Source="deleteIcon.png"
                                        Grid.Row="1"
                                        Grid.Column="4"
                                        BackgroundColor="Transparent"
                                        HorizontalOptions="End"
                                        Margin="5,0,5,5"
                                        WidthRequest="40"
                                        HeightRequest="40"
                                        IsEnabled="{Binding CanNutzerPersonenBeDeleted}"
                                        IsVisible="{Binding CanNutzerPersonenBeDeleted}"
                                        Command="{Binding DeleteNutzerPersonenCommand}">

                        </ImageButton>

                    </Grid>

                </Frame>

                <Label  x:Name="NutzerPersonenList"
                        Margin="5"
                        Grid.Row="2"
                        Grid.Column="0"
                        Grid.ColumnSpan="4"
                        Text="Nutzer Personen Liste"
                        FontSize="Large"
                        FontAttributes="Bold"
                        HorizontalOptions="Center"
                        TextColor="Black">
                </Label>

                <ListView   x:Name="NutzerPersonenListView"
                            Margin="10,0,10,0"
                            Grid.Row="3"
                            Grid.Column="0"
                            Grid.ColumnSpan="4"
                            ItemsSource="{Binding NutzerPersonenObsCollec}"
                            SelectionMode="Single"
                            SelectedItem="{Binding SelectedNutzerPersonen, Mode=TwoWay}"
                            IsPullToRefreshEnabled="False"
                            HasUnevenRows="True"
                            CachingStrategy="RecycleElement">
                    <ListView.ItemTemplate>
                        <DataTemplate x:Name="NutzerPersonenListElement">
                            <ViewCell>
                                <Frame  Margin="2"
                                        Padding="0"
                                        BorderColor="LightGray"
                                        CornerRadius="0"
                                        HasShadow="True">

                                    <Grid RowSpacing="0" ColumnSpacing="0">

                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="50"></RowDefinition>
                                        </Grid.RowDefinitions>

                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                                        </Grid.ColumnDefinitions>

                                        <Label  x:Name="NutzerPersonenNumber"
                                                Grid.Row="0"
                                                Grid.Column="0"
                                                Margin="5,0,30,0"
                                                Text="{Binding NumberOfPeople, StringFormat='Personen: {0}'}"
                                                TextColor="Black"
                                                FontAttributes="Bold"
                                                FontSize="Small">

                                        </Label>

                                        <Label  x:Name="NutzerListFromDate"
                                                Grid.Row="0"
                                                Grid.Column="1"
                                                Margin="5,0,5,0"
                                                Text="{Binding RangeFrom, StringFormat='Von: {0:dd.MM.yyyy}'}"
                                                TextColor="Black"
                                                FontAttributes="Bold"
                                                FontSize="Small">

                                        </Label>

                                        <Label  x:Name="NutzerListToDate"
                                                Grid.Row="0"
                                                Grid.Column="2"
                                                Margin="5,0,5,0"
                                                Text="{Binding RangeTo, StringFormat='Bis: {0:dd.MM.yyyy}', TargetNullValue='Bis: N/A'}"
                                                TextColor="Black"
                                                FontAttributes="Bold"
                                                FontSize="Small">

                                        </Label>


                                    </Grid>
                                </Frame>
                            </ViewCell>
                        </DataTemplate>
                    </ListView.ItemTemplate>

                </ListView>

            </Grid>
        </ScrollView>
    </ContentPage.Content>
</ContentPage>