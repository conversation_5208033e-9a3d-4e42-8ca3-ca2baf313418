﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitOrderStateConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Eras2AmwApp.Domain.Eras2Amw.Extensions;
    using Eras2AmwApp.Models;
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using Xamarin.Forms;
    using Eras2AmwApp.Domain.Eras2Amw.Models;

    public class NutzeinheitOrderStateConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            NutzeinheitUiState uiState = (NutzeinheitUiState)value;

            if (uiState.OrderStates == null)
            {
                return null;
            }

            List<DeviceOrderState> devicesOrderStates = uiState.OrderStates;
            bool hasSignature = uiState.HasSignature;
            bool areAllDevicesClosed = true;
            bool isAnyDevicesClosed = false;
            Color deviceOrderColor;

            foreach (DeviceOrderState orderState in devicesOrderStates)
            {
                if(!orderState.IsClosed())
                {
                    areAllDevicesClosed = false;
                }
                else
                {
                    isAnyDevicesClosed = true;
                }
            }

            if (IsFinishedWithoutError(uiState, areAllDevicesClosed, hasSignature))
            {
                deviceOrderColor = Color.Green;
            }
            else if (IsFinishedWithError(uiState))
            {
                deviceOrderColor = Color.Red;
            }
            else if(IsPartiallyFinished(uiState, hasSignature, isAnyDevicesClosed))
            {
                deviceOrderColor = Color.Orange;
            }
            else
            {
                deviceOrderColor = Color.LightGray;
            }

            return deviceOrderColor;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private bool IsFinishedWithoutError(NutzeinheitUiState uiState, bool areAllDevicesClosed, bool hasSignature)
        {
            return (uiState.NutzeinheitProcessState.IsCompleted() ||
                uiState.NutzeinheitProcessState.IsUpdating() ||
                uiState.NutzeinheitProcessState.IsCreating())
                && areAllDevicesClosed && hasSignature &&
                uiState.AmwInfoKeyGuid == null;
        }

        private bool IsFinishedWithError(NutzeinheitUiState uiState)
        {
            return (uiState.NutzeinheitProcessState.IsUpdating() ||
                uiState.NutzeinheitProcessState.IsCreating() ||
                uiState.NutzeinheitProcessState.IsCompleted()) &&
                uiState.AmwInfoKeyGuid != null;
        }

        private bool IsPartiallyFinished(NutzeinheitUiState uiState, bool hasSignature, bool isAnyDevicesClosed)
        {
            return (uiState.NutzeinheitProcessState.InProgress() ||
                uiState.NutzeinheitProcessState.IsUpdating() ||
                uiState.NutzeinheitProcessState.IsCreating())
                && (isAnyDevicesClosed || hasSignature)
                && uiState.AmwInfoKeyGuid == null;
        }
    }
}
