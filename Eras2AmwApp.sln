﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35825.156
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Endiancode.Utilities", "Endiancode.Utilities\Endiancode.Utilities.csproj", "{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Eras2AmwApp", "Eras2AmwApp\Eras2AmwApp\Eras2AmwApp.csproj", "{240175C7-1677-1DAF-2213-1FE0E32BF4C9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Eras2AmwApp.Android", "Eras2AmwApp\Eras2AmwApp.Android\Eras2AmwApp.Android.csproj", "{0D59031E-194B-4C50-8D59-F354EC78BF9C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Eras2AmwApp.BusinessLogic", "Eras2AmwApp.Bl\Eras2AmwApp.BusinessLogic.csproj", "{93BEF591-F821-8695-9451-CE2A8F770F32}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Eras2AmwApp.Database.Migration", "Eras2AmwApp.Database.Migration\Eras2AmwApp.Database.Migration.csproj", "{DBC2089B-7D09-6DB8-DCAD-243A3843F251}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Eras2AmwApp.Domain", "Eras2AmwApp.Domain\Eras2AmwApp.Domain.csproj", "{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Eras2AmwApp.WebService", "Eras2AmwApp.WebService\Eras2AmwApp.WebService.csproj", "{B3268ED8-FCD6-98F1-7984-C87B191D751B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{8EC462FD-D22E-90A8-E5CE-7E832BA40C5D}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Eras2AmwApp.Common", "Eras2AmwApp.Common\Eras2AmwApp.Common.csproj", "{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Eras2AmwApp.Database", "Eras2AmwApp.Database\Eras2AmwApp.Database.csproj", "{7268034D-5615-25E9-EC41-6388B2BB4DEE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Development|Any CPU = Development|Any CPU
		Release|Any CPU = Release|Any CPU
		StandaloneDevelopment|Any CPU = StandaloneDevelopment|Any CPU
		StandaloneRelease|Any CPU = StandaloneRelease|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Development|Any CPU.Build.0 = Development|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Release|Any CPU.Build.0 = Release|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{240175C7-1677-1DAF-2213-1FE0E32BF4C9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{240175C7-1677-1DAF-2213-1FE0E32BF4C9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{240175C7-1677-1DAF-2213-1FE0E32BF4C9}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{240175C7-1677-1DAF-2213-1FE0E32BF4C9}.Development|Any CPU.Build.0 = Development|Any CPU
		{240175C7-1677-1DAF-2213-1FE0E32BF4C9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{240175C7-1677-1DAF-2213-1FE0E32BF4C9}.Release|Any CPU.Build.0 = Release|Any CPU
		{240175C7-1677-1DAF-2213-1FE0E32BF4C9}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{240175C7-1677-1DAF-2213-1FE0E32BF4C9}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{240175C7-1677-1DAF-2213-1FE0E32BF4C9}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{240175C7-1677-1DAF-2213-1FE0E32BF4C9}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.Development|Any CPU.Build.0 = Development|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.Development|Any CPU.Deploy.0 = Development|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.StandaloneDevelopment|Any CPU.Deploy.0 = StandaloneDevelopment|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{0D59031E-194B-4C50-8D59-F354EC78BF9C}.StandaloneRelease|Any CPU.Deploy.0 = StandaloneRelease|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Development|Any CPU.Build.0 = Development|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Release|Any CPU.Build.0 = Release|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{DBC2089B-7D09-6DB8-DCAD-243A3843F251}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DBC2089B-7D09-6DB8-DCAD-243A3843F251}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DBC2089B-7D09-6DB8-DCAD-243A3843F251}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{DBC2089B-7D09-6DB8-DCAD-243A3843F251}.Development|Any CPU.Build.0 = Development|Any CPU
		{DBC2089B-7D09-6DB8-DCAD-243A3843F251}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DBC2089B-7D09-6DB8-DCAD-243A3843F251}.Release|Any CPU.Build.0 = Release|Any CPU
		{DBC2089B-7D09-6DB8-DCAD-243A3843F251}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{DBC2089B-7D09-6DB8-DCAD-243A3843F251}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{DBC2089B-7D09-6DB8-DCAD-243A3843F251}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{DBC2089B-7D09-6DB8-DCAD-243A3843F251}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Development|Any CPU.Build.0 = Development|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Release|Any CPU.Build.0 = Release|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Development|Any CPU.Build.0 = Development|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Release|Any CPU.Build.0 = Release|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Development|Any CPU.Build.0 = Development|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Release|Any CPU.Build.0 = Release|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Development|Any CPU.Build.0 = Development|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Release|Any CPU.Build.0 = Release|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5C9BF02A-6CD4-428C-BE56-A2B08A414FB1}
	EndGlobalSection
EndGlobal
