﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IAppSettings.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using System.IO;

namespace Eras2AmwApp.Common.Interfaces
{
    public interface IAppSettings
    {
        string RootDirectory { get; }

        string DownloadDirectery { get; }

        string DatabaseDirectory { get; }

        string LogFilesDirectory { get; }

        string LogFileName { get; }

        bool IsDevelopment { get; }

        bool IsProductive { get; }

        int UserLoginValidLocalLoginDays { get; }

        string WebserviceDirectory { get; }

        string WebserviceUploadDirectory { get; }

        string WebserviceDownloadDirectory { get; }

        string PicturesDirectory { get; }

        string SignaturesDirectory { get; }

        string IosAppCenterKey { get; }

        string SyncfusionLicenceKey { get; }

        string BackupDirectory { get; }

        string AzureBackupAccountName { get; }

        string AzureBackupAccountKey { get; }

        string AzureBackupShareName { get; }

        Stream AssetsTestStream { get; }

        bool IsStandaloneApp { get; }
    }
}