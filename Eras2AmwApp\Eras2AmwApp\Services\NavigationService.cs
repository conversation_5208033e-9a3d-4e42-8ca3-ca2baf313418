﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NavigationService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Services
{
    using System;
    using System.Globalization;
    using System.Linq;
    using System.Reflection;
    using System.Threading.Tasks;
    using Common.Ioc;
    using Endiancode.Utilities.Extensions;
    using GalaSoft.MvvmLight;
    using Interfaces;

    using ViewModels;
    using Xamarin.Forms;
    
    public class NavigationService : IEcNavigationService
    {
        public bool ShowInitPageOnResume { get; set; } = true;

        public ViewModelBase PreviousPageViewModel
        {
            get
            {
                var mainPage = Application.Current.MainPage as NavigationPage;

                object viewModel = mainPage?.Navigation.NavigationStack[mainPage.Navigation.NavigationStack.Count - 2].BindingContext;
                return viewModel as ViewModelBase;
            }
        }

        public Task InitializeAsync<TViewModel>() where TViewModel : ViewModelBase => NavigateToAsync<TViewModel>();

        public Task NavigateToAsync<TViewModel>() where TViewModel : ViewModelBase => InternalNavigateToAsync(typeof(TViewModel), null);

        public Task NavigateToAsync<TViewModel>(object parameter) where TViewModel : ViewModelBase => InternalNavigateToAsync(typeof(TViewModel), parameter);

        public Task RemoveLastFromBackStackAsync()
        {
            if (Application.Current.MainPage is NavigationPage mainPage)
            {
                mainPage.Navigation.RemovePage(mainPage.Navigation.NavigationStack[mainPage.Navigation.NavigationStack.Count - 2]);
            }

            return Task.FromResult(true);
        }

        public Task RemoveBackStackAsync()
        {
            if (Application.Current.MainPage is NavigationPage mainPage)
            {
                for (int i = 0; i < mainPage.Navigation.NavigationStack.Count - 1; i++)
                {
                    Page page = mainPage.Navigation.NavigationStack[i];
                    mainPage.Navigation.RemovePage(page);
                }
            }

            return Task.FromResult(true);
        }

        public async Task PopToRootAsync()
        {
            if (Application.Current.MainPage is NavigationPage mainPage)
            {
                await mainPage.Navigation.PopToRootAsync();
            }
        }

        public async Task GoBackAsync() => await Application.Current.MainPage.Navigation.PopAsync();

        private static EcViewModelBase CreateViewModel(Type viewModelType)
        {
            MethodInfo method = typeof(NinjectKernel).GetMethod("Get");
            MethodInfo genericMethod = method?.MakeGenericMethod(viewModelType);
            var viewModel = genericMethod?.Invoke(null, null) as EcViewModelBase;

            return viewModel;
        }

        private async Task InternalNavigateToAsync(Type viewModelType, object parameter)  
        {
            if (viewModelType == null)
            {
                throw new ArgumentNullException(nameof(viewModelType));
            }

            Page page = CreatePage(viewModelType);

            if (Application.Current.MainPage is NavigationPage navigationPage)  
            {  
                await navigationPage.PushAsync(page);  
            }
            else
            {
                Application.Current.MainPage = new NavigationPage(page);
            }

            if (page.BindingContext is EcViewModelBase viewModelBase)
            {
                await viewModelBase.SetupAsync(parameter);
            }
        }

        private Type GetPageTypeForViewModel(Type viewModelType)  
        {
            if (viewModelType == null)
            {
                throw new ArgumentNullException(nameof(viewModelType));
            }

            var viewName = viewModelType.FullName?.TrimEnd("ViewModel", StringComparison.InvariantCulture);

            viewName = viewName?.Replace("ViewModels", "Pages");
            var viewModelAssemblyName = viewModelType.GetTypeInfo().Assembly.FullName;  
            var viewAssemblyName = string.Format(CultureInfo.InvariantCulture, "{0}, {1}", viewName, viewModelAssemblyName);  
            Type viewType = Type.GetType(viewAssemblyName);

            return viewType;  
        }  
        
        private Page CreatePage(Type viewModelType)  
        {
            if (viewModelType == null)
            {
                throw new ArgumentNullException(nameof(viewModelType));
            }

            Type pageType = GetPageTypeForViewModel(viewModelType);  
            if (pageType == null)  
            {  
                throw new Exception($"Cannot locate page type for { viewModelType }");  
            }  

            ConstructorInfo constructor = pageType.GetTypeInfo().DeclaredConstructors.FirstOrDefault(
                c =>
                {
                    ParameterInfo[] p = c.GetParameters();
                    return p.Count() == 1 && p[0].ParameterType == viewModelType;
                });

            if (constructor == null)
            {
                throw new InvalidOperationException($"No suitable constructor found for Page {pageType}");
            }

            EcViewModelBase viewModel = CreateViewModel(viewModelType);
            var page = constructor.Invoke(new object[] { viewModel }) as Page;
            
            return page;  
        }
    }
}