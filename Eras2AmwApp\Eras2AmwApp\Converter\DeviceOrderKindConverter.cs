﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceOrderKindConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class DeviceOrderKindConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            DeviceOrderKind orderKind = (DeviceOrderKind)value;
            string orderKindText;

            switch (orderKind)
            {
                case DeviceOrderKind.Assembly:
                    orderKindText = "MONT";
                    break;
                case DeviceOrderKind.Maintenance:
                    orderKindText = "WART";
                    break;
                case DeviceOrderKind.Exchange:
                    orderKindText = "EICH";
                    break;
                case DeviceOrderKind.Reading:
                    orderKindText = "ABLE";
                    break;
                case DeviceOrderKind.Inspection:
                    orderKindText = "PRÜF";
                    break;
                case DeviceOrderKind.MainReading:
                    orderKindText = "HA";
                    break;
                default:
                    orderKindText = "UNBE";
                    break;
            }

            return orderKindText;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
