﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitEditPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using Endiancode.Utilities.Extensions;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.BusinessLogic.Services;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Common.Services;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Domain.Eras2App.Database;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.Models;
    using Eras2AmwApp.Services;
    using Eras2AmwApp.Validators;
    using Eras2AmwApp.WebService.Interfaces;
    using FluentValidation.Results;
    using Plugin.Media;
    using Plugin.Media.Abstractions;
    using Syncfusion.SfBusyIndicator.XForms;
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using Xamarin.Essentials;
    using Xamarin.Forms;

    public class NutzerEditPageViewModel : EcViewModelBase, ISupportParentViewModel
    {
        #region fields

        private readonly BusinessLogic.Interfaces.INutzeinheitService nutzeinheitService;
        private readonly IPersonService personService;
        private readonly IEcDialogService dialogService;
        private readonly INutzerService nutzerService;
        private readonly IPhotoService photoService;
        private readonly ISignatureService signatureService;
        private readonly ILoginService loginService;

        private readonly WebService.Interfaces.IAmwWebservice webservice;

        private readonly IBackupService backupService;

        private Command deleteNutzeinheitCommand;
        private Command saveNutzeinheitCommand;
        private Command nutzeinheitPhotoCommand;
        private Command nutzerPersonenCommand;
        private Command nutzerCoOwnershipCommand;
        private Command nutzerQuadratmeterCommand;

        private EcViewModelBase _parentViewModel;

        private string _nutzeinheitLocation;
        private string _nutzeinheitWalkSequence;
        private string _nutzeinheitNote;

        private string _nutzerName1;
        private string _nutzerName2;
        private string _nutzerName3;
        private string _nutzerName4;
        private string _nutzerNote;
        private string _nutzerCommunicationValue;
        private string _nutzerPersonenNumber;
        private string _nutzerPersonenFromDate;
        private string _nutzerPersonenToDate;
        private string _quadratmeterHzg;
        private string _quadratmeterWw;
        private string _quadratmeterNk;
        private string _nutzerQuadratmeterFromDate;
        private string _nutzerQuadratmeterToDate;
        private string _nutzerMea;
        private string _nutzerCoOwnershipFromDate;
        private string _nutzerCoOwnershipToDate;

        private bool _isDeleteVisible;
        private bool _isLiveSyncOn;

        private string _nutzerSalutationErrorText;
        private string _selectedNutzerKindErrorText;

        private bool _nutzerSalutationHasError;
        private bool _selectedNutzerKindHasError;

        private Title _nutzerTitle;
        private Salutation _nutzerSalutatuion;
        private NutzerKindVM _selectedNutzerKind;
        private CommunicationDetailsVM _selectedNutzerCommunicationType;
        
        Dictionary<CommunicationKind, string> CommunicationDetailsDict;

        private Guid nutzeinheitGuid;
        private NutzeinheitVM selectedNutzeinheitVM;
        private NutzeinheitOrder nutzeinheitOrder;
        #endregion

        #region ctor

        public NutzerEditPageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            BusinessLogic.Interfaces.INutzeinheitService nutzeinheitService,
            INutzerService nutzerService,
            IPersonService personService,
            IEcDialogService dialogService,
            IAmwWebservice webservice,
            IBackupService backupService,
            IPhotoService photoService,
            ISignatureService signatureService,
            ILoginService loginService)
           : base(serviceLocator, navigationService)
        {
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.personService = personService ?? throw new ArgumentNullException(nameof(personService));
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.nutzerService = nutzerService ?? throw new ArgumentNullException(nameof(nutzerService));
            this.photoService = photoService ?? throw new ArgumentNullException(nameof(photoService));
            this.signatureService = signatureService ?? throw new ArgumentNullException(nameof(signatureService));
            this.loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));
            this.webservice = webservice ?? throw new ArgumentNullException(nameof(webservice));
            this.backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));

            InitilizePickersItemLists();
        }

        #endregion

        #region commands

        public Command DeleteNutzeinheitCommand => deleteNutzeinheitCommand ?? (deleteNutzeinheitCommand = new Command(DeleteNutzeinheitExecute, () => IsDeleteVisible));

        public Command SaveNutzeinheitCommand => saveNutzeinheitCommand ?? (saveNutzeinheitCommand = new Command(SaveNutzeinheitExecute));

        public Command NutzeinheitPhotoCommand => nutzeinheitPhotoCommand ?? (nutzeinheitPhotoCommand = new Command(NutzeinheitPhotoExecute));

        public Command NutzerPersonenCommand => nutzerPersonenCommand ?? (nutzerPersonenCommand = new Command(NutzerPersonenExecute));

        public Command NutzerCoOwnershipCommand => nutzerCoOwnershipCommand ?? (nutzerCoOwnershipCommand = new Command(NutzerCoOwnershipExecute));

        public Command NutzerQuadratmeterCommand => nutzerQuadratmeterCommand ?? (nutzerQuadratmeterCommand = new Command(NutzerQuadratmeterExecute));

        #endregion

        #region properties

        public bool IsDeleteVisible
        {
            get { return _isDeleteVisible; }
            set { Set(ref _isDeleteVisible, value); }
        }

        public bool IsLiveSyncOn
        {
            get { return _isLiveSyncOn; }
            set { Set(ref _isLiveSyncOn, value); }
        }

        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { Set(ref _parentViewModel, value); }
        }

        public NutzeinheitPageViewModel GetParentProperty()
        {
            return ParentViewModel as NutzeinheitPageViewModel;
        }

        public string NutzeinheitLocation
        {
            get { return _nutzeinheitLocation; }
            set { Set(ref _nutzeinheitLocation, value); }
        }

        public string NutzeinheitWalkSequence
        {
            get { return _nutzeinheitWalkSequence; }
            set { Set(ref _nutzeinheitWalkSequence, value); }
        }

        public string NutzeinheitNote
        {
            get { return _nutzeinheitNote; }
            set
            {
                if (value != null)
                {
                    value = RemoveUnsupportedCharacters(value);
                }
                Set(ref _nutzeinheitNote, value);
            }
        }

        public Title NutzerTitle
        {
            get { return _nutzerTitle; }
            set { Set(ref _nutzerTitle, value); }
        }

        public Salutation NutzerSalutation
        {
            get { return _nutzerSalutatuion; }
            set
            {
                if (value != _nutzerSalutatuion)
                {
                    Set(ref _nutzerSalutatuion, value);
                    if (!Validate())
                    {
                        return;
                    }
                }
            }
        }

        public string NutzerSalutationErrorText
        {
            get { return _nutzerSalutationErrorText; }
            set { Set(ref _nutzerSalutationErrorText, value); }
        }

        public bool NutzerSalutationHasError
        {
            get { return _nutzerSalutationHasError; }
            set { Set(ref _nutzerSalutationHasError, value); }
        }

        public NutzerKindVM SelectedNutzerKind
        {
            get { return _selectedNutzerKind; }
            set
            {
                Set(ref _selectedNutzerKind, value);
                if (value.NutzerKindType == NutzerKind.Leerstand.GetDescription())
                {
                    UpdateLeerstandNutzerInfo();
                }
            }
        }

        public string SelectedNutzerKindErrorText
        {
            get { return _selectedNutzerKindErrorText; }
            set { Set(ref _selectedNutzerKindErrorText, value); }
        }

        public bool SelectedNutzerKindHasError
        {
            get { return _selectedNutzerKindHasError; }
            set { Set(ref _selectedNutzerKindHasError, value); }
        }

        public string NutzerName1
        {
            get { return _nutzerName1; }
            set
            {
                if (value != _nutzerName1)
                {
                    Set(ref _nutzerName1, value);
                    if (!Validate())
                    {
                        return;
                    }
                }
            }
        }

        public string NutzerName2
        {
            get { return _nutzerName2; }
            set { Set(ref _nutzerName2, value); }
        }

        public string NutzerName3
        {
            get { return _nutzerName3; }
            set { Set(ref _nutzerName3, value); }
        }

        public string NutzerName4
        {
            get { return _nutzerName4; }
            set { Set(ref _nutzerName4, value); }
        }

        public string NutzerNote
        {
            get { return _nutzerNote; }
            set
            {
                if (value != null)
                {
                    value = RemoveUnsupportedCharacters(value);
                }
                Set(ref _nutzerNote, value);
            }
        }

        public CommunicationDetailsVM SelectedNutzerCommunicationType
        {
            get { return _selectedNutzerCommunicationType; }
            set
            {
                if (value != _selectedNutzerCommunicationType)
                {
                    Set(ref _selectedNutzerCommunicationType, value);
                    UpdateNutzerCommunicationValue(value);
                }
            }
        }

        public string NutzerCommunicationValue
        {
            get { return _nutzerCommunicationValue; }
            set { Set(ref _nutzerCommunicationValue, value); }
        }

        public string NutzerPersonenNumber
        {
            get { return _nutzerPersonenNumber; }
            set { Set(ref _nutzerPersonenNumber, value); }
        }

        public string NutzerPersonenFromDate
        {
            get { return _nutzerPersonenFromDate; }
            set { Set(ref _nutzerPersonenFromDate, value); }
        }

        public string NutzerPersonenToDate
        {
            get { return _nutzerPersonenToDate; }
            set { Set(ref _nutzerPersonenToDate, value); }
        }

        public string QuadratmeterHzg
        {
            get { return _quadratmeterHzg; }
            set { Set(ref _quadratmeterHzg, value); }
        }

        public string QuadratmeterWw
        {
            get { return _quadratmeterWw; }
            set { Set(ref _quadratmeterWw, value); }
        }

        public string QuadratmeterNk
        {
            get { return _quadratmeterNk; }
            set { Set(ref _quadratmeterNk, value); }
        }

        public string NutzerQuadratmeterFromDate
        {
            get { return _nutzerQuadratmeterFromDate; }
            set { Set(ref _nutzerQuadratmeterFromDate, value); }
        }

        public string NutzerQuadratmeterToDate
        {
            get { return _nutzerQuadratmeterToDate; }
            set { Set(ref _nutzerQuadratmeterToDate, value); }
        }

        public string NutzerMea
        {
            get { return _nutzerMea; }
            set { Set(ref _nutzerMea, value); }
        }

        public string NutzerCoOwnershipFromDate
        {
            get { return _nutzerCoOwnershipFromDate; }
            set { Set(ref _nutzerCoOwnershipFromDate, value); }
        }

        public string NutzerCoOwnershipToDate
        {
            get { return _nutzerCoOwnershipToDate; }
            set { Set(ref _nutzerCoOwnershipToDate, value); }
        }

        public List<Salutation> NutzerSalutationList { get; set; }

        public List<Title> NutzerTitleList { get; set; }

        public List<NutzerKindVM> NutzerKindList { get; set; }

        public List<CommunicationDetailsVM> NutzerCommunicationTypeList { get; set; }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is NutzeinheitVM nutzeinheitVM)
            {
                selectedNutzeinheitVM = nutzeinheitVM;
                ParentViewModel = nutzeinheitVM.ParentViewModel;
                nutzeinheitGuid = selectedNutzeinheitVM.NutzeinheitGuid;
                InitilizeProperties();
            }
            return base.SetupAsync(navigationData);
        }

        #endregion

        #region private methods

        private void InitilizePickersItemLists()
        {
            try
            {
                List<Salutation> listOfSalutations = personService.GetSalutations();
                NutzerSalutationList = new List<Salutation>();
                foreach (Salutation salutation in listOfSalutations)
                {
                    NutzerSalutationList.Add(salutation);
                }

                List<Title> listOfTitles = personService.GetTitles();
                NutzerTitleList = new List<Title>();
                foreach (Title title in listOfTitles)
                {
                    NutzerTitleList.Add(title);
                }

                List<NutzerKind> listOfNutzerKinds = Enum.GetValues(typeof(NutzerKind)).OfType<NutzerKind>().ToList();
                NutzerKindList = new List<NutzerKindVM>();
                foreach (NutzerKind nutzerKind in listOfNutzerKinds)
                {
                    NutzerKindVM nutzerKindVM = new NutzerKindVM()
                    {
                        NutzerKindType = nutzerKind.GetDescription()
                    };

                    NutzerKindList.Add(nutzerKindVM);
                }

                NutzerCommunicationTypeList = new List<CommunicationDetailsVM>();
                List<CommunicationKind> listOfCommunicationTypes = Enum.GetValues(typeof(CommunicationKind)).OfType<CommunicationKind>().ToList();

                foreach(CommunicationKind communicationEnum in listOfCommunicationTypes)
                {
                    CommunicationDetailsVM communicationDetail = new CommunicationDetailsVM()
                    {
                        CommunicationType = communicationEnum.GetDescription()
                    };
                    NutzerCommunicationTypeList.Add(communicationDetail);
                }
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while initializing content of pickers list in NutzeinheitEditPageVM");
                throw;
            }
        }

        private void InitilizeProperties()
        {
            try
            {
                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(nutzeinheitGuid);
                DateTime appointmentTime = selectedNutzeinheitVM.AppointmentDate;

                InitNutzeinheit(nutzeinheit);

                Nutzer currentNutzer = nutzerService.GetNutzerForDate(nutzeinheit, appointmentTime);
                IsLiveSyncOn = loginService.GetUserLiveSyncState();
                IsDeleteVisible = nutzeinheit.IsCreatedByApp;
                InitNutzerName(currentNutzer);
                InitNutzerNote(currentNutzer);
                InitNutzerKind(currentNutzer);
                InitNutzerSalutationAndTitle(currentNutzer);
                InitNutzerCommunication(currentNutzer);
                InitNutzerPersonen(currentNutzer);
                InitNutzerQuadratmeter(currentNutzer);
                InitNutzerCoOwnership(currentNutzer);
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while initializing properties for NutzeinheitEditPageVM!");
                throw;
            }
        }

        private void InitNutzeinheit(Nutzeinheit nutzeinheit)
        {
            NutzeinheitNote = nutzeinheit.Note;
            NutzeinheitWalkSequence = nutzeinheit.WalkSequence;
            NutzeinheitLocation = nutzeinheit.Location;
        }

        private void InitNutzerName(Nutzer currentNutzer)
        {
            NutzerName1 = currentNutzer.Name1;
            NutzerName2 = currentNutzer.Name2;
            NutzerName3 = currentNutzer.Name3;
            NutzerName4 = currentNutzer.Name4;
        }

        private void InitNutzerNote(Nutzer currentNutzer)
        {
            NutzerNote = currentNutzer.Note;
        }

        private void InitNutzerKind(Nutzer currentNutzer)
        {
            NutzerKindVM nutzerKindVM = new NutzerKindVM()
            {
                NutzerKindType = currentNutzer.Kind.GetDescription()
            };
            SelectedNutzerKind = NutzerKindList.First(x => x.NutzerKindType == nutzerKindVM.NutzerKindType);
        }

        private void InitNutzerSalutationAndTitle(Nutzer currentNutzer)
        {
            Salutation salutation = currentNutzer.Salutation;
            NutzerSalutation = NutzerSalutationList.First(x => x.Label == salutation.Label);

            Title title = currentNutzer.Title;

            if (title != null)
            {
                NutzerTitle = NutzerTitleList.First(x => x.Label == title.Label);
            }
        }

        private void InitNutzerCommunication(Nutzer currentNutzer)
        {
            List<NutzerCommunication> nutzerCommunications = currentNutzer.NutzerCommunications.ToList();

            CommunicationDetailsDict = personService.GetPersonContacts(nutzerCommunications);

            NutzerCommunicationValue = personService.GetPersonMainCommunicationValue(nutzerCommunications);
            if (nutzerCommunications.Count != 0)
            {
                CommunicationDetailsVM communication = new CommunicationDetailsVM
                {
                    CommunicationType = CommunicationDetailsDict.FirstOrDefault(x => x.Value == NutzerCommunicationValue).Key.GetDescription()
                };
                SelectedNutzerCommunicationType = NutzerCommunicationTypeList.First(x => x.CommunicationType == communication.CommunicationType);
            }
        }

        private void InitNutzerPersonen(Nutzer currentNutzer)
        {
            List<NutzerPersonen> nutzerPersonen = currentNutzer.NutzerPersonen.ToList();

            if (nutzerPersonen.Count != 0)
            {
                NutzerPersonen nutzerPerson = nutzerPersonen.OrderByDescending(x => x.RangeFrom).First();
                if(nutzerPerson.NumberOfPeople.HasValue)
                {
                    NutzerPersonenNumber = "Personen : " + nutzerPerson.NumberOfPeople.Value.ToString();
                }
                else
                {
                    NutzerPersonenNumber = "Personen : -";
                }
                    
                NutzerPersonenFromDate = "Von : " + nutzerPerson.RangeFrom.ToString("dd.MM.yyyy");
                if (nutzerPerson.RangeTo.HasValue)
                {
                    NutzerPersonenToDate = "Bis : " + nutzerPerson.RangeTo.Value.ToString("dd.MM.yyyy");
                }
                else
                {
                    NutzerPersonenToDate = "Bis : -";
                }
            }
            else
            {
                NutzerPersonenNumber = "Personen : -";
                NutzerPersonenFromDate = "Von : -";
                NutzerPersonenToDate = " Bis : -";
            }
        }

        private void InitNutzerQuadratmeter(Nutzer currentNutzer)
        {
            List<NutzerQuadratmeter> nutzerQuadratemeters = currentNutzer.NutzerQuadratmeter.ToList();

            if (nutzerQuadratemeters.Count != 0)
            {
                NutzerQuadratmeter nutzerQuadratemeter = nutzerQuadratemeters.OrderByDescending(x => x.RangeFrom).First();

                if (nutzerQuadratemeter.SquareMeters_Hzg.HasValue)
                {
                    QuadratmeterHzg = "Qm Hzg : " + nutzerQuadratemeter.SquareMeters_Hzg.Value.ToString();
                }
                else
                {
                    QuadratmeterHzg = "Qm Hzg : -";
                }

                if (nutzerQuadratemeter.SquareMeters_Ww.HasValue)
                {
                    QuadratmeterWw = "Qm Ww : " + nutzerQuadratemeter.SquareMeters_Ww.Value.ToString();
                }
                else
                {
                    QuadratmeterWw = "Qm Ww : -";
                }

                if (nutzerQuadratemeter.SquareMeters_Nk.HasValue)
                {
                    QuadratmeterNk = "Qm Nk : " + nutzerQuadratemeter.SquareMeters_Nk.Value.ToString();
                }
                else
                {
                    QuadratmeterNk = "Qm Nk : -";
                }

                NutzerQuadratmeterFromDate = "Von : " + nutzerQuadratemeter.RangeFrom.ToString("dd.MM.yyyy");
                if (nutzerQuadratemeter.RangeTo.HasValue)
                {
                    NutzerQuadratmeterToDate = "Bis : " + nutzerQuadratemeter.RangeTo.Value.ToString("dd.MM.yyyy");
                }
                else
                {
                    NutzerQuadratmeterToDate = "Bis : -";
                }
            }
            else
            {
                QuadratmeterHzg = "Qm Hzg : -";
                QuadratmeterWw = "Qm Ww : -";
                QuadratmeterNk = "Qm Nk : -";
                NutzerQuadratmeterFromDate = "Von : -";
                NutzerQuadratmeterToDate = "Bis : -";
            }
        }

        private void InitNutzerCoOwnership(Nutzer currentNutzer)
        {
            List<NutzerCoOwnership> nutzerCoOwnerships = currentNutzer.NutzerCoOwnership.ToList();

            if (nutzerCoOwnerships.Count != 0)
            {
                NutzerCoOwnership nutzerCoOwnership = nutzerCoOwnerships.OrderByDescending(x => x.RangeFrom).First();
                NutzerMea = "Mea : " + nutzerCoOwnership.ShareValue.Value.ToString();
                NutzerCoOwnershipFromDate = "Von : " + nutzerCoOwnership.RangeFrom.ToString("dd.MM.yyyy");
                if (nutzerCoOwnership.RangeTo.HasValue)
                {
                    NutzerCoOwnershipToDate = "Bis : " + nutzerCoOwnership.RangeTo.Value.ToString("dd.MM.yyyy");
                }
                else
                {
                    NutzerCoOwnershipToDate = "Bis : -";
                }
            }
            else
            {
                NutzerMea = "Mea : -";
                NutzerCoOwnershipFromDate = "Von : -";
                NutzerCoOwnershipToDate = " Bis : -";
            }
        }

        private void UpdateNutzerCommunicationValue(CommunicationDetailsVM selectedCommunicationType)
        {
            try
            {
                Enum.TryParse(selectedCommunicationType.CommunicationType, out CommunicationKind commKind);
                CommunicationDetailsDict.TryGetValue(commKind, out string selectedCommunicationValue);

                if(selectedCommunicationValue == NutzerCommunicationValue)
                {
                    return;
                }

                NutzerCommunicationValue = selectedCommunicationValue;
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while trying to retreive value for selected communication type!");
                throw;
            }
        }

        private async void DeleteNutzeinheitExecute()
        {
            try
            {
                string dialogMessage = localisationService.Get("DeleteConfirmation");
                DialogResponse dialogResponse = await dialogService.AcceptDeclineAsync(dialogMessage, "Ja", "Nein", "Löschen bestätigen");

                if (dialogResponse == DialogResponse.Accept)
                {
                    Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(nutzeinheitGuid);
                    nutzeinheitService.DeleteNutzeinheit(nutzeinheit);
                    await navigationService.GoBackAsync();
                }
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attepting to delete selected Nutzeinheit!");
                throw;
            }
        }

        private async void SaveNutzeinheitExecute()
        {
            try
            {
                if (!Validate())
                {
                    return;
                }

                if (!await CheckIfCanBeSave())
                {
                    return;
                }

                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(nutzeinheitGuid);
                Nutzeinheit modifiedNutzeinheit = UpdateNutzeinheit();
                bool isNutzeinheitModified = WasNutzeinheitModified(nutzeinheit, modifiedNutzeinheit);

                if(isNutzeinheitModified)
                {
                    nutzeinheitService.UpdateNutzeinheit(nutzeinheit);
                }

                DateTime appointmentTime = selectedNutzeinheitVM.AppointmentDate;
                UpdateNutzer(nutzeinheit, appointmentTime);
                UpdateNutzerCommunication(nutzeinheit, appointmentTime);

                if (loginService.GetUserLiveSyncState())
                {
                    await DoNutzerEditWebserviceSync();
                }

                await navigationService.GoBackAsync();
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attepting to save selected Nutzeinheit!");
                throw;
            }
        }

        private async Task<bool> CheckIfCanBeSave()
        {
            if (selectedNutzeinheitVM == null)
            {
                return false;
            }

            nutzeinheitOrder = new NutzeinheitOrder()
            {
                NutzeinheitGuid = nutzeinheitGuid,
                OrderGuid = selectedNutzeinheitVM.NutzeinheitOrder.OrderGuid
            };

            //Check if there is AmwKey in Ne
            NutzeinheitPageViewModel pageViewModel = GetParentProperty();
            if (pageViewModel == null)
            {
                return false;
            }
            bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

            if (!result)
            {
                return false;
            }

            //Check if user wants to procede and delete signature if exists
            Signature signature = GetSignatureForThisOrder();

            if (signature != null)
            {
                DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                if (signatureDeleteWarningResult == DialogResponse.Decline)
                {
                    return false;
                }
            }

            return true;
        }

        private Signature GetSignatureForThisOrder()
        {
            return signatureService.GetSignatureForOrder(nutzeinheitOrder.NutzeinheitGuid, nutzeinheitOrder.OrderGuid);
        }

        private async Task<DialogResponse> DisplayDeleteSignatureWarning(Signature signature)
        {
            string warning = localisationService.Get("DeleteSignatureWarning");
            DialogResponse result = await dialogService.AcceptDeclineAsync(warning, "JA", "NEIN");

            if (result == DialogResponse.Decline)
            {
                return result;
            }

            DeleteSignature(signature);

            return result;
        }

        private void DeleteSignature(Signature signature)
        {
            if (signature.Path != null)
            {
                signatureService.RemoveSignatureFromDevice(signature);
            }
            signatureService.DeleteSignature(signature);
            SetNutzeinheitStateInProgress();
        }

        private void SetNutzeinheitStateInProgress()
        {
            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitOrder);

            if (nutzeinheitOrderState.ProcessState == ProcessState.InProgress)
            {
                return;
            }

            nutzeinheitOrderState.ProcessState = ProcessState.InProgress;
            nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);
        }

        private Nutzeinheit UpdateNutzeinheit()
        {
            Nutzeinheit nutzeinheit = new Nutzeinheit
            {
                Location = NutzeinheitLocation,
                WalkSequence = NutzeinheitWalkSequence,
                Note = NutzeinheitNote
            };

            return nutzeinheit;
        }

        private bool WasNutzeinheitModified(Nutzeinheit nutzeinheit, Nutzeinheit modifiedNutzeinheit)
        {
            if((nutzeinheit.Location != modifiedNutzeinheit.Location) ||
                (nutzeinheit.WalkSequence != modifiedNutzeinheit.WalkSequence) ||
                (nutzeinheit.Note != modifiedNutzeinheit.Note))
            {
                nutzeinheit.Location = modifiedNutzeinheit.Location;
                nutzeinheit.WalkSequence = modifiedNutzeinheit.WalkSequence;
                nutzeinheit.Note = modifiedNutzeinheit.Note;
                return true;
            }

            return false;
        }

        private void UpdateNutzer(Nutzeinheit nutzeinheit, DateTime appointmentTime)
        {
            Nutzer nutzer = nutzerService.GetNutzerForDate(nutzeinheit, appointmentTime);
            nutzer.Name1 = NutzerName1;
            nutzer.Name2 = NutzerName2;
            nutzer.Name3 = NutzerName3;
            nutzer.Name4 = NutzerName4;
            nutzer.Note = NutzerNote;

            nutzer.SalutationId = NutzerSalutation.Id;
            nutzer.Salutation = personService.GetSalutation(NutzerSalutation.Id);

            if (NutzerTitle != null)
            {
                nutzer.TitleId = NutzerTitle?.Id ?? null;
            }
            
            Enum.TryParse(SelectedNutzerKind.NutzerKindType, out NutzerKind nutzerKind);
            nutzer.Kind = nutzerKind;

            bool isNutzerModified = WasNutzerModified(nutzer, nutzeinheit, appointmentTime);
            if(isNutzerModified)
            {
                nutzerService.UpdateNutzer(nutzer);
            }
        }

        private bool WasNutzerModified(Nutzer oldNutzer, Nutzeinheit nutzeinheit, DateTime appointmentTime)
        {
            Nutzer unmodifiedNutzer = nutzerService.GetNutzerForDate(nutzeinheit, appointmentTime);

            if((unmodifiedNutzer.Name1 != oldNutzer.Name1) ||
                (unmodifiedNutzer.Name2 != oldNutzer.Name2) ||
                (unmodifiedNutzer.Name3 != oldNutzer.Name3) ||
                (unmodifiedNutzer.Name4 != oldNutzer.Name4) ||
                (unmodifiedNutzer.SalutationId != oldNutzer.SalutationId) ||
                (unmodifiedNutzer.TitleId != oldNutzer.TitleId) ||
                (unmodifiedNutzer.Kind != oldNutzer.Kind))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        private void UpdateNutzerCommunication(Nutzeinheit nutzeinheit, DateTime appointmentTime)
        {
            Nutzer nutzer = nutzerService.GetNutzerForDate(nutzeinheit, appointmentTime);
            List<NutzerCommunication> nutzerCommunications = nutzer.NutzerCommunications.ToList();

            if(SelectedNutzerCommunicationType == null)
            {
                return;
            }

            Enum.TryParse(SelectedNutzerCommunicationType.CommunicationType, out CommunicationKind kind);
            NutzerCommunication nutzerCommunication = nutzerCommunications.FirstOrDefault(x => x.CommunicationFeature.Kind == kind);

            if (nutzerCommunication == null)
            {
                nutzerCommunication = GetNewNutzerCommunication(nutzer, kind);
                nutzerService.SaveNutzerCommunication(nutzerCommunication);
            }
            else
            {
                NutzerCommunication modifiedNutzerCommunication = GetNutzerCommunication(nutzer,kind);

                if (WasCommunicationModified(nutzerCommunication, modifiedNutzerCommunication, kind))
                {
                    nutzerService.UpdateNutzerCommunication(nutzerCommunication);
                }
            }
        }

        private async Task DoNutzerEditWebserviceSync()
        {
            try
            {
                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    return;
                }

                Xamarin.Forms.Device.BeginInvokeOnMainThread(() => { dialogService.ShowBusyIndicator(AnimationTypes.Globe, "Der Nutzerstatus wird übertragen..."); });

                await backupService.DatabaseDackup();
                await webservice.SyncLiveNutzerEditAsync(nutzeinheitGuid);
            }
            catch (Exception exp)
            {
                logger.Error(exp, "SyncLiveAsync(): Exception.");
            }
            finally
            {
                dialogService.HideBusyIndicator();
            }
        }

        private bool WasCommunicationModified(NutzerCommunication nutzerCommunication, NutzerCommunication modifiedNutzerCommunication, CommunicationKind kind)
        {
            if((nutzerCommunication.Address != modifiedNutzerCommunication.Address) ||
                (nutzerCommunication.CommunicationFeature.Kind != kind))
            {
                CommunicationFeature communicationFeature = personService.GetCommunicationFeature(kind);
                nutzerCommunication.Address = NutzerCommunicationValue;
                nutzerCommunication.CommunicationFeature.Guid = communicationFeature.Guid;
                return true;
            }
            else
            {
                return false;
            }
           
        }

        private NutzerCommunication GetNewNutzerCommunication(Nutzer nutzer, CommunicationKind kind)
        {
            CommunicationFeature communicationFeature = personService.GetCommunicationFeature(kind);

            if(NutzerCommunicationValue == null)
            {
                NutzerCommunicationValue = string.Empty;
            }

            NutzerCommunication newNutzerCommunication = new NutzerCommunication()
            {
                NutzerGuid = nutzer.Guid,
                Address = NutzerCommunicationValue,
                CommunicationFeatureGuid = communicationFeature.Guid
            };

            return newNutzerCommunication;
        }

        private NutzerCommunication GetNutzerCommunication(Nutzer nutzer, CommunicationKind kind)
        {
            CommunicationFeature communicationFeature = personService.GetCommunicationFeature(kind);
            NutzerCommunication newNutzerCommunication = new NutzerCommunication()
            {
                Address = NutzerCommunicationValue,
                NutzerGuid = nutzer.Guid,
                CommunicationFeatureGuid = communicationFeature.Guid
            };

            return newNutzerCommunication;
        }

        private void UpdateLeerstandNutzerInfo()
        {
            try
            {
                NutzerName1 = "Leerstand";
                NutzerName2 = string.Empty;
                NutzerName3 = string.Empty;
                NutzerName4 = string.Empty;
                NutzerTitle = null;
                NutzerSalutation = new Salutation() { Id = 6 };
                NutzerCommunicationValue = string.Empty;
                NutzerNote = string.Empty;
            }
            catch(Exception e)
            {
                logger.Error("Exception occured while attempting to set leerstand data in NutzerEditPage!",e);
                throw;
            }
        }

        private async void NutzeinheitPhotoExecute()
        {
            try
            {
                if (!await CheckIfCanBeSave())
                {
                    return;
                }

                await CrossMedia.Current.Initialize();
                navigationService.ShowInitPageOnResume = false;

                PermissionStatus statusCamera = await Permissions.CheckStatusAsync<Permissions.Camera>();
                PermissionStatus status = await Permissions.CheckStatusAsync<Permissions.StorageWrite>();

                if (statusCamera == PermissionStatus.Denied || status == PermissionStatus.Denied)
                {
                    statusCamera = await Permissions.RequestAsync<Permissions.Camera>();
                    status = await Permissions.RequestAsync<Permissions.StorageWrite>();
                }
                if (statusCamera == PermissionStatus.Denied || status == PermissionStatus.Denied) return;

                MediaFile picture = await CrossMedia.Current.TakePhotoAsync(new StoreCameraMediaOptions()
                {
                    CompressionQuality = 20,
                    PhotoSize = PhotoSize.Medium,
                    SaveToAlbum = false,
                    AllowCropping = false
                });

                if (picture != null)
                {
                    string photoPath = photoService.SaveNutzeinheitPhoto(picture.Path, selectedNutzeinheitVM.NutzeinheitGuid);
                    Photo photo = new Photo()
                    {
                        Name = Path.GetFileName(photoPath),
                        Path = photoPath,
                        RecordedDate = DateTime.UtcNow,
                        CreatedByApp = true,
                        NutzeinheitGuid = selectedNutzeinheitVM.NutzeinheitGuid
                    };

                    photoService.SavePhotoToDb(photo);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to capture a picture in OrderPageVm!");
                throw;
            }
            finally
            {
                navigationService.ShowInitPageOnResume = true;
            }
        }

        private async void NutzerPersonenExecute()
        {
            try
            {
                selectedNutzeinheitVM.ParentViewModel = this;
                await navigationService.NavigateToAsync<NutzerPersonenPageViewModel>(selectedNutzeinheitVM);
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to navigate to NutzerPersonenPage");
                throw;
            }
        }

        private async void NutzerQuadratmeterExecute()
        {
            try
            {
                selectedNutzeinheitVM.ParentViewModel = this;
                await navigationService.NavigateToAsync<NutzerQuadratmeterPageViewModel>(selectedNutzeinheitVM);
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to navigate to NutzerQuadratmeterPage");
                throw;
            }
        }

        private async void NutzerCoOwnershipExecute()
        {
            try
            {
                selectedNutzeinheitVM.ParentViewModel = this;
                await navigationService.NavigateToAsync<NutzerEditPageViewModel>(selectedNutzeinheitVM);
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to navigate to NutzerCoOwnershipPage");
                throw;
            }
        }

        private bool Validate()
        {
            var validator = new NutzerEditPageViewModelValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }

        #endregion
    }
}
