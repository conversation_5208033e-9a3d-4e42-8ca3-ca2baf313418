﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AbstractContext.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Contexts
{
    using System;
    using System.IO;
    using Common.Interfaces;

    using Eras2AmwApp.Common.Implementations;
    
    using Microsoft.Data.Sqlite;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Logging.Console;

    public abstract class AbstractContext : DbContext
    {
        protected readonly IAppSettings appSettings;

        private static LoggerFactory myLoggerFactory;
      
        protected AbstractContext(IAppSettings appSettings)
        {
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
        }

        public static LoggerFactory MyLoggerFactory => myLoggerFactory ?? (myLoggerFactory = new LoggerFactory(new ILoggerProvider[]
        {
#if DEBUG
            new ConsoleLoggerProvider((category, level) => category == DbLoggerCategory.Database.Command.Name && level == LogLevel.Error, true),
#endif

#if !DEBUG
            new SimpleLoggerProvider((category, level) => category == DbLoggerCategory.Database.Command.Name && level == LogLevel.Error )
#endif
        }));

        protected virtual string DatabaseName { get; set; } 

        protected string DatabasePath => Path.Combine(appSettings.DatabaseDirectory, DatabaseName);

        public void RecreateDatabase()
        {
            DeleteDatabase();
            Database.Migrate();
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (optionsBuilder == null)
            {
                throw new ArgumentNullException(nameof(optionsBuilder));
            }

            SqliteConnection connection = InitializeSQLiteConnection();

            optionsBuilder.UseLoggerFactory(MyLoggerFactory).EnableSensitiveDataLogging();

            optionsBuilder.UseSqlite(connection);
        }

        private void DeleteDatabase()
        {
            if (File.Exists(DatabasePath))
            {
                File.Delete(DatabasePath);
            }
        }

        private SqliteConnection InitializeSQLiteConnection()
        {
            var connection = new SqliteConnection($"DataSource={DatabasePath}");
            
            return connection;
        }
    }
}