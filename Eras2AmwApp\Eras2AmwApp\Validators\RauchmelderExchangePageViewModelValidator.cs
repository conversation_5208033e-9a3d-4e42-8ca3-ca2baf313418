﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="RauchmelderExchangePageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using FluentValidation;
    using ViewModels;
    using Eras2AmwApp.Common.Ioc;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using System.Linq;
    using System;

    public class RauchmelderExchangePageViewModelValidator : AbstractValidator<RauchmelderExchangePageViewModel>
    {
        private readonly IDbContextFactory contextFactory = NinjectKernel.Get<IDbContextFactory>();

        public RauchmelderExchangePageViewModelValidator()
        {
            RuleFor(x => x.OldDeviceNumber)
                .NotEmpty().WithMessage("Die Gerätenummer darf nicht leer sein.");

            RuleFor(x => x.NewDeviceNumber)
                .Must(ValidateDeviceNumber)
                .WithMessage("Die Gerätenummer existiert bereits in dieser Nutzeinheit und kann nicht noch einmal vergeben werden.")
                .NotEmpty().WithMessage("Die Gerätenummer darf nicht leer sein.");

            //RuleFor(x => x.NewOngoingNumber)
            //    .Must(ValidateOnGoingNumber)
            //    .WithMessage("Die Laufende Nummer existiert bereits in dieser Nutzeinheit und kann nicht noch einmal vergeben werden.")
            //    .NotEmpty().WithMessage("Die Laufende Nummer darf nicht leer sein.");

            RuleFor(x => x.NewSelectedRoom)
                .NotEmpty().WithMessage("Der Raum darf nicht leer sein.");

            RuleFor(x => x.NewSelectedDeviceKind)
                .NotEmpty().WithMessage("Die Gerätetyp darf nicht leer sein.");

            RuleFor(x => x.NewSelectedDeviceCatalog)
                .NotEmpty().WithMessage("Das Feld darf nicht leer sein.");

            RuleFor(x => x.OldDeviceDeinstallationDate)
                .NotNull()
                .WithMessage("Das Ausbaudatum darf nicht leer sein.");

            RuleFor(x => x.NewDeviceInstallationDate)
                .Must(ValidateCorrectDate)
                .WithMessage("Das neue Einzugsdatum muss größer sein als das vorherige Auszugsdatum.");
        }

        //private bool ValidateOnGoingNumber(RauchmelderExchangePageViewModel device, string ongoingNumber)
        //{
        //    using (Eras2AmwContext context = contextFactory.CreateAmw())
        //    {
        //        return !context.Devices.Any(y => y.OngoingNumber == ongoingNumber && y.NutzeinheitGuid == device.NutzeinheitGuid);
        //    }
        //}

        private bool ValidateDeviceNumber(RauchmelderExchangePageViewModel device, string deviceNumber)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return !context.Devices.Any(y => y.Number == deviceNumber && y.NutzeinheitGuid == device.NutzeinheitGuid);
            }
        }

        private bool ValidateCorrectDate(RauchmelderExchangePageViewModel device, DateTime newDeviceInstallationDate)
        {
            if (device.OldDeviceDeinstallationDate.HasValue)
            {
                return device.OldDeviceDeinstallationDate.Value < newDeviceInstallationDate;
            }

            return true;
        }
    }
}
