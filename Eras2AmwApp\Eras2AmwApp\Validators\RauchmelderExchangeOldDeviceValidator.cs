﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="RauchmelderExchangeOldDeviceValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using FluentValidation;
    using ViewModels;

    public class RauchmelderExchangeOldDeviceValidator : AbstractValidator<RauchmelderExchangePageViewModel>
    {
        public RauchmelderExchangeOldDeviceValidator()
        {
            RuleFor(x => x.OldDeviceNumber)
                .NotEmpty().WithMessage("Die Gerätenummer darf nicht leer sein.");
        }
    }
}
