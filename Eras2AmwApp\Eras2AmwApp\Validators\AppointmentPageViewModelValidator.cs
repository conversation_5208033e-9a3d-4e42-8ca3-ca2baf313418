﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="LoginPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using FluentValidation;
    using ViewModels;

    public class AppointmentPageViewModelValidator : AbstractValidator<AppointmentPageViewModel>
    {
        public AppointmentPageViewModelValidator()
        {
            RuleFor(x => x.UserEmail)
                .EmailAddress().When(x => !string.IsNullOrEmpty(x.UserEmail));
        }
    }
}
