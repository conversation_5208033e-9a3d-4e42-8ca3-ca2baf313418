﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AddDevicePageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Interfaces;
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using Device = Domain.Eras2Amw.Models.Device;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Validators;
    using FluentValidation.Results;
    using System.Collections.ObjectModel;
    using Eras2AmwApp.Models;
    using System.Globalization;
    using System.Linq;
    using Eras2AmwApp.Services;
    using Eras2AmwApp.BusinessLogic.Factory;
    using Plugin.Media.Abstractions;
    using Plugin.Media;
    using System.IO;

    public class AddDevicePageViewModel : EcViewModelBase, ISupportParentViewModel
    {
        #region fields

        private readonly INutzeinheitService nutzeinheitService;
        private readonly IDeviceService deviceService;
        private readonly IScannerService scannerService;
        private readonly IEcDialogService dialogService;
        private readonly IStammdatenService stammdatenService;
        private readonly ISignatureService signatureService;
        private readonly IPhotoService photoService;

        private Command saveDeviceCommand;
        private Command readBarcodCommand;
        private Command addArticleCommand;
        private Command removeArticleCommand;
        private Command setInstallationDateCommand;
        private Command newPhotoCommand;

        private EcViewModelBase _parentViewModel;

        private string _deviceNumber;
        private DeviceClass _deviceClass;
        private Room _selectedRoom;
        private string _ongoingNumber;
        private DeviceKind _selectedDeviceKind;
        private DeviceCatalog _selectedDeviceCatalog;
        private string _deviceCalibrationDate;
        private DateTime _deviceInstallationDate;
        private double? _deviceReading;
        private bool _isDeviceMaintained;
        private AmwInfoKey _selectedAmwKey;
        private string _deviceNote;
        
        private bool _isDeviceCatalogEnabled;
        private List<DeviceCatalog> _deviceCatalogList;
        private List<AmwInfoKey> _amwInfoKeyList;
        private List<string> _pickerYearList;
        private string _additionalArticlesNumber;
        private AdditionalArticle _selectedAdditionalArticle;
        private int _selectedAdditionalArticleListCount;
        private AdditionalArticleVM _selectedToRemoveAdditionalAricle;
        
        private string _deviceNumberErrorText;
        private string _selectedRoomErrorText;
        private string _ongoingNumberErrorText;
        private string _selectedDeviceKindErrorText;
        private string _selectedDeviceCatalogErrorText;

        private bool _deviceNumberHasError;
        private bool _selectedRoomHasError;
        private bool _ongoingNumberHasError;
        private bool _selectedDeviceKindHasError;
        private bool _selectedDeviceCatalogHasError;

        NutzeinheitOrder nutzeinheitOrder;

        #endregion 

        #region ctor

        public AddDevicePageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            INutzeinheitService nutzeinheitService,
            IDeviceService deviceService,
            IScannerService scannerService,
            IEcDialogService dialogService,
            IStammdatenService stammdatenService,
            ISignatureService signatureService,
            IPhotoService photoService)
           : base(serviceLocator, navigationService)
        {
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
            this.scannerService = scannerService ?? throw new ArgumentNullException(nameof(scannerService));
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.stammdatenService = stammdatenService ?? throw new ArgumentNullException(nameof(stammdatenService));
            this.signatureService = signatureService ?? throw new ArgumentNullException(nameof(signatureService));
            this.photoService = photoService ?? throw new ArgumentNullException(nameof(photoService));

            SelectedAdditionalArticleList = new ObservableCollection<AdditionalArticleVM>();
            NewDevicePictues = new List<MediaFile>();
            SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;
            InitPickerList();
            InitilizeProperties();
        }

        #endregion

        #region commands

        public Command SaveDeviceCommand => saveDeviceCommand ?? (saveDeviceCommand = new Command(SaveDeviceExecute));

        public Command ReadBarcodCommand => readBarcodCommand ?? (readBarcodCommand = new Command(ReadBarcodExecute));

        public Command AddArticleCommand => addArticleCommand ?? (addArticleCommand = new Command<AdditionalArticle>(AddArticleExecute));

        public Command RemoveArticleCommand => removeArticleCommand ?? (removeArticleCommand = new Command<AdditionalArticleVM>(RemoveArticleExecute));

        public Command SetInstallationDateCommand => setInstallationDateCommand ?? (setInstallationDateCommand = new Command(SetInstallationDateExecute));

        public Command NewPhotoCommand => newPhotoCommand ?? (newPhotoCommand = new Command(NewPhotoExecute));

        #endregion

        #region properties

        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { Set(ref _parentViewModel, value); }
        }

        public Guid NutzeinheitGuid { get; set; }

        public string DeviceNumber
        {
            get { return _deviceNumber; }
            set { Set(ref _deviceNumber, value); }
        }

        public string DeviceNumberErrorText
        {
            get { return _deviceNumberErrorText; }
            set { Set(ref _deviceNumberErrorText, value); }
        }

        public bool DeviceNumberHasError
        {
            get { return _deviceNumberHasError; }
            set { Set(ref _deviceNumberHasError, value); }
        }

        public DeviceClass DeviceClass
        {
            get { return _deviceClass; }
            set { Set(ref _deviceClass, value); }
        }

        public DeviceKind SelectedDeviceKind
        {
            get { return _selectedDeviceKind; }
            set
            {
                if (value != _selectedDeviceKind)
                {
                    Set(ref _selectedDeviceKind, value);
                    GetDeviceCatalogOptions(value);
                    GetCalibrationDateOptionsForDevice();
                    GetAmwInfoKeyList();
                }
            }
        }

        public string SelectedDeviceKindErrorText
        {
            get { return _selectedDeviceKindErrorText; }
            set { Set(ref _selectedDeviceKindErrorText, value); }
        }

        public bool SelectedDeviceKindHasError
        {
            get { return _selectedDeviceKindHasError; }
            set { Set(ref _selectedDeviceKindHasError, value); }
        }

        public Room SelectedRoom
        {
            get { return _selectedRoom; }
            set { Set(ref _selectedRoom, value); }
        }

        public string SelectedRoomErrorText
        {
            get { return _selectedRoomErrorText; }
            set { Set(ref _selectedRoomErrorText, value); }
        }

        public bool SelectedRoomHasError
        {
            get { return _selectedRoomHasError; }
            set { Set(ref _selectedRoomHasError, value); }
        }

        public string OngoingNumber
        {
            get { return _ongoingNumber; }
            set { Set(ref _ongoingNumber, value); }
        }

        public string OngoingNumberErrorText
        {
            get { return _ongoingNumberErrorText; }
            set { Set(ref _ongoingNumberErrorText, value); }
        }

        public bool OngoingNumberHasError
        {
            get { return _ongoingNumberHasError; }
            set { Set(ref _ongoingNumberHasError, value); }
        }

        public string DeviceCalibrationDate
        {
            get { return _deviceCalibrationDate; }
            set
            {
                if (value == " ")
                {
                    value = null;
                }
                Set(ref _deviceCalibrationDate, value);
            }
        }

        public DateTime DeviceInstallationDate
        {
            get { return _deviceInstallationDate; }
            set { Set(ref _deviceInstallationDate, value); }
        }

        public double? DeviceReading
        {
            get { return _deviceReading; }
            set { Set(ref _deviceReading, value); }
        }

        public bool IsDeviceMaintained
        {
            get { return _isDeviceMaintained; }
            set { Set(ref _isDeviceMaintained, value); }
        }

        public AmwInfoKey SelectedAmwKey
        {
            get { return _selectedAmwKey; }
            set
            {
                if (value != _selectedAmwKey)
                {
                    if (value.Info == "<Infoschlüssel löschen>")
                    {
                        value = null;
                    }
                    Set(ref _selectedAmwKey, value);
                }
            }
        }

        public string DeviceNote
        {
            get { return _deviceNote; }
            set 
            {
                if (value != null)
                {
                    value = RemoveUnsupportedCharacters(value);
                }
                Set(ref _deviceNote, value);
            }
        }

        public DeviceCatalog SelectedDeviceCatalog
        {
            get { return _selectedDeviceCatalog; }
            set { Set(ref _selectedDeviceCatalog, value); }
        }

        public string SelectedDeviceCatalogErrorText
        {
            get { return _selectedDeviceCatalogErrorText; }
            set { Set(ref _selectedDeviceCatalogErrorText, value); }
        }

        public bool SelectedDeviceCatalogHasError
        {
            get { return _selectedDeviceCatalogHasError; }
            set { Set(ref _selectedDeviceCatalogHasError, value); }
        }

        public List<DeviceKind> DeviceKindsList { get; set; }

        public List<DeviceCatalog> DeviceCatalogList
        {
            get { return _deviceCatalogList; }
            set { Set(ref _deviceCatalogList, value); }
        }

        public bool IsDeviceCatalogEnabled
        {
            get { return _isDeviceCatalogEnabled; }
            set { Set(ref _isDeviceCatalogEnabled, value); }
        }

        public ObservableCollection<AdditionalArticle> AdditionalArticleList { get; set; }

        public ObservableCollection<Room> RoomList { get; set; }
  
        public ObservableCollection<AdditionalArticleVM> SelectedAdditionalArticleList { get; set; }

        public int SelectedAdditionalArticleListCount
        {
            get { return _selectedAdditionalArticleListCount; }
            set { Set(ref _selectedAdditionalArticleListCount, value); }
        }

        public AdditionalArticle SelectedAdditionalArticle
        {
            get { return _selectedAdditionalArticle; }
            set { Set(ref _selectedAdditionalArticle, value); }
        }

        public AdditionalArticleVM SelectedToRemoveAdditionalAricle
        {
            get { return _selectedToRemoveAdditionalAricle; }
            set { Set(ref _selectedToRemoveAdditionalAricle, value); }
        }

        public string AdditionalArticlesNumber
        {
            get { return _additionalArticlesNumber; }
            set { Set(ref _additionalArticlesNumber, value); }
        }

        public List<string> PickerYearList
        {
            get { return _pickerYearList; }
            set { Set(ref _pickerYearList, value); }
        }

        public List<AmwInfoKey> AmwInfoKeyList
        {
            get { return _amwInfoKeyList; }
            set { Set(ref _amwInfoKeyList, value); }
        }

        public List<MediaFile> NewDevicePictues { get; set; }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is NutzeinheitVM parentVM)
            {
                ParentViewModel = parentVM.ParentViewModel;
                nutzeinheitOrder = parentVM.NutzeinheitOrder;
                NutzeinheitGuid = nutzeinheitOrder.NutzeinheitGuid;

                InitOngoingNumberPlaceholder();
            }
            return base.SetupAsync(navigationData);
        }

        public NutzeinheitPageViewModel GetParentProperty()
        {
            return ParentViewModel as NutzeinheitPageViewModel;
        }

        #endregion

        #region private methods

        private void InitPickerList()
        {
            try
            {
                List<AdditionalArticle> articleList = stammdatenService.GetAdditionalArticles();
                AdditionalArticleList = new ObservableCollection<AdditionalArticle>();
                foreach (AdditionalArticle additionalArticle in articleList)
                {
                    AdditionalArticleList.Add(additionalArticle);
                }

                List<Room> roomList = stammdatenService.GetRooms();
                RoomList = new ObservableCollection<Room>();
                foreach(Room room in roomList)
                {
                    RoomList.Add(room);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while initilizing AdditionalArticleList liste!");
                throw;
            }
        }

        private void GetDeviceCatalogOptions(DeviceKind deviceKind)
        {
            try
            {
                if (deviceKind is null)
                {
                    return;
                }

                DeviceClass = deviceKind.Class;

                List<DeviceCatalog> deviceCatalogList = deviceService.GetDeviceCatalogList(deviceKind.Guid);
                if(deviceCatalogList.Any())
                {
                    IsDeviceCatalogEnabled = true;
                }
                else
                {
                    IsDeviceCatalogEnabled = false;
                }
                DeviceCatalogList = deviceCatalogList;

                if(DeviceCatalogList.Count == 1)
                {
                    SelectedDeviceCatalog = DeviceCatalogList[0];
                }
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while initilizing DeviceCatalogOption list!");
                throw;
            }
        }

        private void GetAmwInfoKeyList()
        {
            List<AmwInfoKey> infoKeyList = new List<AmwInfoKey>();

            if(DeviceClass == DeviceClass.RM)
            {
                infoKeyList = stammdatenService.GetRauchmelderInfoKey();
            }
            else if(DeviceClass == DeviceClass.KWZ || DeviceClass == DeviceClass.WWZ || DeviceClass == DeviceClass.SZ)
            {
                infoKeyList = stammdatenService.GetWasserzählerInfoKey();
            }
            else if (DeviceClass == DeviceClass.HKV )
            {
                infoKeyList = stammdatenService.GetHkvInfoKey();
            }

            AmwInfoKey emptyKey = new AmwInfoKey()
            {
                Key = -1,
                Info = "<Infoschlüssel löschen>"
            };
            infoKeyList.Insert(0, emptyKey);

            AmwInfoKeyList = infoKeyList;
        }

        private void GetCalibrationDateOptionsForDevice()
        {
            try
            {
                int deviceMaxCalibrationDate = 10;

                if (DeviceClass != DeviceClass.RM)
                {
                    deviceMaxCalibrationDate = 6;
                }

                List<string >pickerYearList = new List<string>
                {
                    " "
                };
                for (int x = 0; x <= deviceMaxCalibrationDate; x++)
                {
                    string year = DateTime.Now.AddYears(x).ToString("yyyy");
                    pickerYearList.Add(year);
                }
                PickerYearList = pickerYearList;

                DeviceCalibrationDate = PickerYearList.Max();
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to init values for CalibrationDate for selected deviceClass!");
                throw;
            }
        }

        private void InitilizeProperties()
        {
            IsDeviceMaintained = false;
            DeviceInstallationDate = DateTime.Now;
            DeviceCatalogList = new List<DeviceCatalog>();
            AmwInfoKeyList = new List<AmwInfoKey>();
            PickerYearList = new List<string>();
            List<DeviceKind> deviceKindsList = deviceService.GetDeviceKinds();

            DeviceKindsList = deviceKindsList.Where(x => x.LabelShort == "Allg. RM" 
            || x.LabelShort == "Allg. KWZ" 
            || x.LabelShort == "Allg. WWZ" 
            || x.LabelShort == "Allg. WMZ"
            || x.LabelShort == "Allg. HKV" 
            || x.LabelShort == "Allg. SZ").ToList();
            if (DeviceKindsList.Count == 1)
            {
                SelectedDeviceKind = DeviceKindsList[0];
            }
        }

        private void InitOngoingNumberPlaceholder()
        {
            try
            {
                OngoingNumber = deviceService.GetPlaceholderOngoingNumber(NutzeinheitGuid);
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to init suggested placeholder number for ongoing number!");
                throw;
            }
        }

        private async void SaveDeviceExecute()
        {
            try
            {
                if (!Validate())
                {
                    return;
                }

                if (!await CheckIfCanBeSave())
                {
                    return;
                }

                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(nutzeinheitOrder.NutzeinheitGuid);

                Device newDevice = CreateNewDevice(nutzeinheit);
                deviceService.AddDevice(newDevice);

                //get new instance of the new device that contains DeviceCatalog property
                Device createdDevice = deviceService.GetDevice(newDevice.Guid);
                DeviceOrderState deviceOrderState = createdDevice.OrderStates.Single();

                IDevice iDevice = DeviceFactory.Create(createdDevice, deviceOrderState);
                DeviceClass deviceClass = createdDevice.DeviceCatalog.DeviceKind.Class;

                NutzeinheitPageViewModel previousViewModel = GetParentProperty();
                DeviceVM deviceVM = new DeviceVM
                {
                    DeviceClass = deviceClass,
                    DeviceNumber = iDevice.DeviceNumber,
                    DeviceLabel = iDevice.DeviceLabel,
                    DeviceRoom = iDevice.DeviceRoom.Label,
                    DeviceOngoingNumber = iDevice.DeviceOngoingNumber,
                    ParentViewModel = ParentViewModel,
                    Device = iDevice,
                    DeviceUiState = new DeviceUiState
                    {
                        DeviceOrderState = iDevice.DeviceOrderState,
                        IsMaintained = createdDevice.IsMaintained
                    },
                    Photos = new List<Photo>(),
                    IsDeviceListEditedOrCreated = true,
                    IsDeviceMaintained = createdDevice.IsMaintained
                };

                if(DeviceClass == DeviceClass.RM && !IsDeviceMaintained)
                {
                    deviceVM.IsDeviceListEditedOrCreated = false;
                }

                previousViewModel.ListOfCurrentDevices.Add(deviceVM);
                previousViewModel.ListOfAllDevices.Add(deviceVM);

                if (NewDevicePictues.Count() != 0)
                {
                    AddNewDevicePhotos(createdDevice);
                }
                await navigationService.GoBackAsync();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to save new device details!");
                throw;
            }
        }

        private Device CreateNewDevice(Nutzeinheit nutzeinheit)
        {
            Device device = new Device
            {
                NutzeinheitGuid = nutzeinheit.Guid,
                Number = DeviceNumber,
                InstallationDate = DeviceInstallationDate,
                Note = DeviceNote,
                OngoingNumber = OngoingNumber,
                Subtraction = false,
                IsCreatedByApp = true,
                IsMaintained = false,
                IsLeased = false,
                RoomGuid = SelectedRoom.Guid,
                DeviceCatalogGuid = SelectedDeviceCatalog.Guid
            };

            if (DeviceClass == DeviceClass.RM)
            {
                device.IsMaintained = IsDeviceMaintained;
            }

            if (!string.IsNullOrEmpty(DeviceCalibrationDate))
            {
                var cultureInfo = new CultureInfo("de-DE");
                string stringCalibrationDate = "1 Jan " + DeviceCalibrationDate;
                DateTime dateTime = DateTime.Parse(stringCalibrationDate, cultureInfo);
                device.CalibrationDate = dateTime;
            }
            else
            {
                device.CalibrationDate = null;
            }

            DeviceOrderState deviceOrderState = CreateDeviceOrderState(device);
            List<DeviceOrderState> deviceOrderStatesList = new List<DeviceOrderState>
            {
                deviceOrderState
            };

            device.OrderStates = deviceOrderStatesList;

            DeviceConsumption deviceConsumption = CreateDeviceConsumption(device);
            List<DeviceConsumption> deviceConsumptionsList = new List<DeviceConsumption>
            {
                deviceConsumption
            };

            device.DeviceConsumptions = deviceConsumptionsList;

            List<DeviceAdditionalArticle> createdDeviceArticleList = new List<DeviceAdditionalArticle>();
            foreach (AdditionalArticleVM additionalArticleVM in SelectedAdditionalArticleList)
            {
                DeviceAdditionalArticle newArticle = new DeviceAdditionalArticle()
                {
                    DeviceGuid = device.Guid,
                    AdditionalArticleGuid = additionalArticleVM.AdditionalArticle.Guid,
                    Quantity = additionalArticleVM.Amount,
                    IsCreatedByApp = true
                };
                createdDeviceArticleList.Add(newArticle);
            }

            device.DeviceAdditionalArticles = createdDeviceArticleList;

            return device;
        }

        private DeviceOrderState CreateDeviceOrderState(Device device)
        {
            DeviceOrderState deviceOrderState = new DeviceOrderState()
            {
                ProcessState = ProcessState.Creating,
                DeviceGuid = device.Guid,
                OrderGuid = nutzeinheitOrder.OrderGuid
            };

            if(!IsDeviceMaintained && SelectedAmwKey != null)
            {
                deviceOrderState.AmwInfoKeyGuid = SelectedAmwKey.Guid;
                device.IsMaintained = false;
            }

            DeviceClass selectedDeviceClass = SelectedDeviceCatalog.DeviceKind.Class;

            if (selectedDeviceClass == DeviceClass.RM)
            {
                deviceOrderState.OrderKind = DeviceOrderKind.Assembly;
            }
            else if(selectedDeviceClass == DeviceClass.KWZ || selectedDeviceClass == DeviceClass.WWZ)
            {
                deviceOrderState.OrderKind = DeviceOrderKind.Assembly;
            }

            return deviceOrderState;
        }

        private DeviceConsumption CreateDeviceConsumption(Device device)
        {
            DeviceConsumption deviceConsumption = new DeviceConsumption()
            {
                DeviceGuid = device.Guid,
                Estimation = EstimationKind.Null,
                State = DeviceConsumptionState.Null,
                IsReconstructed = false,
                Origin = DeviceConsumptionOrigin.Manual,
                ReadindKindGuid = GetGuidForReading()
            };

            if(DeviceClass != DeviceClass.RM && DeviceReading != null)
            {
                deviceConsumption.Reading = DeviceReading;
                deviceConsumption.ReadingDate = DateTime.Now;
            }

            return deviceConsumption;
        }

        private Guid GetGuidForReading()
        {
            ReadingKind readingKind = deviceService.GetRauchmelderReadingKind();
            return readingKind.Guid;
        }

        private void AddArticleExecute(AdditionalArticle additionalArticle)
        {
            try
            {
                if (additionalArticle == null)
                {
                    return;
                }

                decimal articlesToAdd = Convert.ToDecimal(AdditionalArticlesNumber, CultureInfo.CurrentCulture);

                AdditionalArticleVM additionalArticleVM = new AdditionalArticleVM()
                {
                    AdditionalArticle = additionalArticle,
                    Amount = articlesToAdd,
                    IsCreatedByApp = true
                };
                SelectedAdditionalArticleList.Add(additionalArticleVM);

                AdditionalArticleList.Remove(additionalArticle);
                SelectedAdditionalArticle = null;
                SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to add an article to list!");
                throw;
            }
        }

        private void RemoveArticleExecute(AdditionalArticleVM additionalArticle)
        {
            try
            {
                if (additionalArticle == null)
                {
                    return;
                }

                SelectedAdditionalArticleList.Remove(additionalArticle);
                AdditionalArticleList.Add(additionalArticle.AdditionalArticle);
                SelectedToRemoveAdditionalAricle = null;
                SelectedAdditionalArticleListCount = SelectedAdditionalArticleList.Count;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to remove an article from list!");
                throw;
            }
        }

        private Signature GetSignatureForThisOrder()
        {
            return signatureService.GetSignatureForOrder(nutzeinheitOrder.NutzeinheitGuid, nutzeinheitOrder.OrderGuid);
        }

        private async Task<DialogResponse> DisplayDeleteSignatureWarning(Signature signature)
        {
            string warning = localisationService.Get("DeleteSignatureWarning");
            DialogResponse result = await dialogService.AcceptDeclineAsync(warning, "JA", "NEIN");

            if (result == DialogResponse.Decline)
            {
                return result;
            }

            DeleteSignature(signature);

            return result;
        }

        private void DeleteSignature(Signature signature)
        {
            if (signature.Path != null)
            {
                signatureService.RemoveSignatureFromDevice(signature);
            }
            signatureService.DeleteSignature(signature);
           
            SetNutzeinheitStateInProgress();
        }

        private void SetNutzeinheitStateInProgress()
        {
            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitOrder);

            if (nutzeinheitOrderState.ProcessState == ProcessState.InProgress)
            {
                return;
            }

            nutzeinheitOrderState.ProcessState = ProcessState.InProgress;
            nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);
        }

        private void SetInstallationDateExecute()
        {
            try
            {
                DeviceInstallationDate = DateTime.Now;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to set installation date to todays date!");
                throw;
            }
        }

        private async void ReadBarcodExecute()
        {
            try
            {
                var barcodeScannerResult = await scannerService.ReadBarcode();
                navigationService.ShowInitPageOnResume = false;

                if (barcodeScannerResult != null)
                {
                    AssignBarcodeValues(barcodeScannerResult.Text);
                }
            }
            catch (NotSupportedException exception)
            {
                logger.Error(exception, "Your system does not support this operaton. Please check if you have an available Camera to perform this opertation.");
                string dialogMessage = localisationService.Get("BarcodeFehlar");
                await dialogService.AcceptAsync(dialogMessage, "OK");
            }
            catch (Exception exception)
            {
                logger.Error(exception, "Error attempting to read Barcode!");
                throw;
            }
        }

        private void AssignBarcodeValues(string barcode)
        {
            if (barcode.Contains(";"))
            {
                string[] splittedQrCode;
                splittedQrCode = barcode.Split(new[] { ";" }, StringSplitOptions.None);
                DeviceNumber = splittedQrCode[1];
                DeviceNote = barcode; 
            }
            else if(barcode.Contains("\r\n"))
            {
                string[] splittedQrCode;
                splittedQrCode = barcode.Split(new[] { "\r\n" }, StringSplitOptions.None);
                DeviceNumber = splittedQrCode[0];
                DeviceNote = barcode;
            }
            else
            {
                DeviceNumber = barcode;
            }
        }

        private bool Validate()
        {
            var validator = new AddDevicePageViewModelValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }

        private async void NewPhotoExecute()
        {
            try
            {
                if (!await CheckIfCanBeSave())
                {
                    return;
                }

                DialogResponse dialogResponse = await dialogService.AcceptDeclineAsync("Die gemachten Fotos werden nur dann gespeichert, wenn das neu angelegte Gerät gespeichert wird.", "OK", "Abbrechen");

                if (dialogResponse == DialogResponse.Decline)
                {
                    return;
                }

                await CrossMedia.Current.Initialize();
                navigationService.ShowInitPageOnResume = false;

                MediaFile picture = await CrossMedia.Current.TakePhotoAsync(new StoreCameraMediaOptions()
                {
                    CompressionQuality = 20,
                    PhotoSize = PhotoSize.Medium,
                    SaveToAlbum = false,
                    AllowCropping = false
                });

                NewDevicePictues.Add(picture);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to capture a picture in OrderPageVm!");
                throw;
            }
            finally
            {
                navigationService.ShowInitPageOnResume = true;
            }
        }

        private void AddNewDevicePhotos(Device createdDevice)
        {
            foreach (MediaFile picture in NewDevicePictues)
            {
                string photoPath = photoService.SaveDevicePhoto(picture.Path, createdDevice.Guid);
                Photo photo = new Photo()
                {
                    Name = Path.GetFileName(photoPath),
                    Path = photoPath,
                    RecordedDate = DateTime.Now,
                    CreatedByApp = true,
                    DeviceGuid = createdDevice.Guid
                };

                photoService.SavePhotoToDb(photo);
                UpdatePreviousModelPhotos(photo, createdDevice.Guid);
            }
        }

        private void UpdatePreviousModelPhotos(Photo photo, Guid deviceGuid)
        {
            NutzeinheitPageViewModel parent = GetParentProperty();
            ObservableCollection<DeviceVM> listOfAllDevices = parent.ListOfAllDevices;

            DeviceVM deviceInAllDeviceListVM = listOfAllDevices.Where(x => x.Device.DeviceGuid == deviceGuid).FirstOrDefault();
            if (deviceInAllDeviceListVM != null)
            {
                deviceInAllDeviceListVM.Photos.Add(photo);
            }
        }

        private async Task<bool> CheckIfCanBeSave()
        {
            //Check if there is AmwKey in Ne
            NutzeinheitPageViewModel pageViewModel = GetParentProperty();
            bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

            if (!result)
            {
                return false;
            }

            //Check if user wants to procede and delete signature if exists
            Signature signature = GetSignatureForThisOrder();

            if (signature != null)
            {
                DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);

                if (signatureDeleteWarningResult == DialogResponse.Decline)
                {
                    return false;
                }
            }

            return true;
        }

        #endregion

    }
}
