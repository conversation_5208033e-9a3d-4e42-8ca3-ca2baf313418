﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceUnitConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.Domain.Eras2Amw.Enums;
using System;
using System.Globalization;
using Xamarin.Forms;

namespace Eras2AmwApp.Converter
{
    public class DeviceUnitConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
           if (value is UnitKind enumValue)
           {
                if (enumValue == UnitKind.KWH)
                {
                    return "KWH";
                }
                else if (enumValue == UnitKind.MWH)
                {
                    return "MWH";
                }
           }
           return " ";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringVal)
            {
                if (stringVal == "KWH")
                {
                    return UnitKind.KWH;
                }
                else if (stringVal == "MWH")
                {
                    return UnitKind.MWH;
                }
            }

            return UnitKind.Null;
        }
    }
}
