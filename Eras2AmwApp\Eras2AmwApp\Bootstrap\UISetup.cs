﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="UISetip.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Bootstrap
{
    using System;

    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.ViewModels;

    using Interfaces;

    using Microsoft.EntityFrameworkCore.Internal;

    using Serilog;
    
    public class UiSetup
    {
        private readonly IEcNavigationService navigationService;

        private readonly ILogger logger;

        private readonly IDbContextFactory factory;

        public UiSetup(IEcNavigationService navigationService, ILogger logger, IDbContextFactory factory)
        {
            this.navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
            this.factory = factory ?? throw new ArgumentNullException(nameof(factory));
        }

        public void Start()
        {
            try
            {
                using (Eras2AppContext context = factory.CreateApp())
                {
                    if (!context.Customers.Any())
                    {
                        navigationService.InitializeAsync<RegistrationPageViewModel>().Wait();
                    }
                    else
                    {
                        navigationService.InitializeAsync<LoginPageViewModel>().Wait();
                    }
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "UiSystem Setup failed");
                throw;
            }
        }
    }
}