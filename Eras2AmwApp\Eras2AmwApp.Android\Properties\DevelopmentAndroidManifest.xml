﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:versionCode="20" android:versionName="1.20" package="de.erassoftware.eras2amwapp.dev" android:installLocation="preferExternal">
	<uses-sdk android:minSdkVersion="28" android:targetSdkVersion="30" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.CAMERA" />
	<uses-permission android:name="android.permission.FLASHLIGHT" />
	<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
	<application android:largeHeap="true" android:label="Eras2AmwApp" android:roundIcon="@mipmap/icon_round" android:icon="@drawable/appDevelopmentIcon">
		<provider android:name="android.support.v4.content.FileProvider" android:authorities="${applicationId}.fileprovider" android:exported="false" android:grantUriPermissions="true">
			<meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/file_paths"></meta-data>
		</provider>
	</application>
</manifest>