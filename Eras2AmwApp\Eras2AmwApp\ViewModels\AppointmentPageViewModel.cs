﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppointmentPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using Common.Interfaces;
    using Interfaces;
    using Syncfusion.SfCalendar.XForms;
    using System;
    using System.Collections.Generic;
    using Xamarin.Forms;
    using System.Collections.ObjectModel;
    using System.Net;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System.Threading.Tasks;
    using Syncfusion.SfSchedule.XForms;
    using Models;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Xamarin.Essentials;
    using Eras2AmwApp.Domain.Eras2App.Database;
    using Eras2AmwApp.Validators;
    using Eras2AmwApp.WebService.Interfaces;
    using FluentValidation.Results;
    using ServiceStack;
    using Syncfusion.SfBusyIndicator.XForms;
    using DomainNutzeinheitService = BusinessLogic.Interfaces.INutzeinheitService;
    using Device = Xamarin.Forms.Device;
    using Eras2AmwApp.WebService.EventArgs;
    using Syncfusion.SfDataGrid.XForms;
    using System.Linq;
    using DomainDevice = Domain.Eras2Amw.Models.Device;
    using Eras2AmwApp.Models.TreeViewModels;
    using Syncfusion.TreeView.Engine;
    using Eras2AmwApp.BusinessLogic.Models;
    using Syncfusion.XForms.TreeView;
    using SelectionChangedEventArgs = Syncfusion.XForms.TabView.SelectionChangedEventArgs;
    using Eras2AmwApp.Services;
    using Polly.Timeout;

    public class AppointmentPageViewModel : EcViewModelBase
    {
        #region fields

        private readonly IAppointmentService appointmentService;
        private readonly IEcDialogService dialogService;
        private readonly IAmwWebservice webservice;
        private readonly IAppDeviceInformationService appDeviceInformationService;
        private readonly IOptionService optionService;
        private readonly DomainNutzeinheitService nutzeinheitService;
        private readonly INutzerService nutzerService;
        private readonly IAppointmentNutzeinheitDetailsDialog appointmentNutzeinheitDetailsDialog;
        private readonly ICloseApplicationService closeApplicationService;
        private readonly ILoginService loginService;

        private readonly IGlobalCacheTrash globalCacheTrash;

        private Command appearingCommand;
        private Command disappearingCommand;
        private Command _selectedScheduleAppointmentCommand;
        private Command _enableAdminModeCommand;
        private Command updateUserEntryCommand;
        private Command localBackupCommand;
        private Command remoteBackupCommand;
        private Command syncOrdersCommand;
        private Command deleteAmwDatabaseCommand;
        private Command factoryResetCommand;
        private Command iconButtonCommand;
        private Command showTerminInfoCommand;
        private Command treeViewItemTappedCommand;
        private Command tabChangedCommand;
        private Command neListCommand;

        private DateTime _selectedDate;
        private string _navigationHeader;
        private string _navigationSecondHeader;
        private AppointmentVm _selectedListAppointment;
        private AppointmentVm _selectedGridAppointment;
        private SfTreeView _treeViewModel;

        private Guid loginTechnicianGuid;

        private string _deviceModel;
        private string _deviceManufacturer;
        private string _devicePlatform;
        private string _devicePlatformVersion;
        private string _appVersion;
        private string _databaseVersion;
        private string _username;
        private string _customername;
        private string _userEmail;
        private string _internetAvailableInfo;
        private string _webserviceUrl;
        private string _userEmailErrorText;
        private bool _userEmailHasError;
        private bool _isInAdminMode;
        private bool showCalendar;
        private string _selectedGroupOption;
        private string _searchbarText;
        private SfDataGrid _sfDataGrid;
        private int enableAdminModeCount;
        private string _terminCount;
        private List<AppointmentTechnician> _appAppointments;
        private bool _isLiveSyncOn;

        private bool _isAdminTestUser;

        internal delegate void FilterChanged();

        private bool isInitFinished;

        #endregion

        #region ctor

        public AppointmentPageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            IEcDialogService dialogService,
            IAmwWebservice webservice,
            IAppointmentService appointmentService,
            IAppDeviceInformationService appDeviceInformationService,
            IOptionService optionService,
            DomainNutzeinheitService nutzeinheitService,
            IGlobalCacheTrash globalCacheTrash,
            INutzerService nutzerService,
            IAppointmentNutzeinheitDetailsDialog appointmentNutzeinheitDetailsDialog,
            ICloseApplicationService closeApplicationService,
            ILoginService loginService)
            : base(serviceLocator, navigationService)
        {
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.webservice = webservice ?? throw new ArgumentNullException(nameof(webservice));
            this.appointmentService = appointmentService ?? throw new ArgumentNullException(nameof(appointmentService));
            this.appDeviceInformationService = appDeviceInformationService ?? throw new ArgumentNullException(nameof(appDeviceInformationService));
            this.optionService = optionService ?? throw new ArgumentNullException(nameof(optionService));
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.globalCacheTrash = globalCacheTrash ?? throw new ArgumentNullException(nameof(globalCacheTrash));
            this.nutzerService = nutzerService ?? throw new ArgumentNullException(nameof(nutzerService));
            this.appointmentNutzeinheitDetailsDialog = appointmentNutzeinheitDetailsDialog ?? throw new ArgumentNullException(nameof(appointmentNutzeinheitDetailsDialog));
            this.closeApplicationService = closeApplicationService ?? throw new ArgumentNullException(nameof(closeApplicationService));
            this.loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));

            InitilizeProperties();
        }

        #endregion

        #region commands

        public Command AppearingCommand => appearingCommand ?? (appearingCommand = new Command(AppearingExecute));

        public Command DisappearingCommand => disappearingCommand ?? (disappearingCommand = new Command(DisappearingExecute));

        public Command SelectedScheduleAppointmentCommand => _selectedScheduleAppointmentCommand ?? (_selectedScheduleAppointmentCommand = new Command(SelectedScheduleAppointmentExecute));

        public Command EnableAdminModeCommand => _enableAdminModeCommand ?? (_enableAdminModeCommand = new Command(EnableAdminModeExecute));

        public Command UpdateUserEntryCommand => updateUserEntryCommand ?? (updateUserEntryCommand = new Command(UpdateUserEntryExecute));

        public Command LocalBackupCommand => localBackupCommand ?? (localBackupCommand = new Command(LocalBackupExecute));

        public Command RemoteBackupCommand => remoteBackupCommand ?? (remoteBackupCommand = new Command(RemoteBackupExecute));

        public Command SyncOrdersCommand => syncOrdersCommand ?? (syncOrdersCommand = new Command(SyncOrdersExecute));

        public Command DeleteAmwDatabaseCommand => deleteAmwDatabaseCommand ?? (deleteAmwDatabaseCommand = new Command(DeleteAmwDatabaseExecute));

        public Command FactoryResetCommand => factoryResetCommand ?? (factoryResetCommand = new Command(FactoryResetExecute));

        public Command IconButtonCommand => iconButtonCommand ?? (iconButtonCommand = new Command<Guid>(IconButtonExecute));

        public Command ShowTerminInfoCommand => showTerminInfoCommand ?? (showTerminInfoCommand = new Command(ShowTerminInfoExecute));

        public Command TreeViewItemTappedCommand => treeViewItemTappedCommand ?? (treeViewItemTappedCommand = new Command<object>(TreeViewItemTappedExecute));

        public Command TabChangedCommand => tabChangedCommand ?? (tabChangedCommand = new Command<SelectionChangedEventArgs>(TabChangedExecute));

        public Command NeListCommand => neListCommand ?? (neListCommand = new Command<Guid>(NeListExecute));

        #endregion

        #region Properties

        public List<AppointmentTechnician> AppAppointments
        {
            get { return _appAppointments; }
            set { Set(ref _appAppointments, value); }
        }

        public SfTreeView TreeViewModel
        {
            get { return _treeViewModel; }
            set { Set(ref _treeViewModel, value); }
        }

        public bool ShowCalendar
        {
            get { return showCalendar; }
            set { Set(ref showCalendar, value); }
        }

        public bool IsInAdminMode
        {
            get { return _isInAdminMode; }
            set { Set(ref _isInAdminMode, value); }
        }

        public string DeviceModel
        {
            get { return _deviceModel; }
            set { Set(ref _deviceModel, value); }
        }

        public string DeviceManufacturer
        {
            get { return _deviceManufacturer; }
            set { Set(ref _deviceManufacturer, value); }
        }

        public string DevicePlatform
        {
            get { return _devicePlatform; }
            set { Set(ref _devicePlatform, value); }
        }

        public string DevicePlatformVersion
        {
            get { return _devicePlatformVersion; }
            set { Set(ref _devicePlatformVersion, value); }
        }

        public string AppVersion
        {
            get { return _appVersion; }
            set { Set(ref _appVersion, value); }
        }

        public string DatabaseVersion
        {
            get { return _databaseVersion; }
            set { Set(ref _databaseVersion, value); }
        }

        public string Username
        {
            get { return _username; }
            set { Set(ref _username, value); }
        }

        public string Customername
        {
            get { return _customername; }
            set { Set(ref _customername, value); }
        }

        public string UserEmail
        {
            get { return _userEmail; }
            set { Set(ref _userEmail, value); }
        }

        public string UserEmailErrorText
        {
            get { return _userEmailErrorText; }
            set { Set(ref _userEmailErrorText, value); }
        }

        public bool UserEmailHasError
        {
            get { return _userEmailHasError; }
            set { Set(ref _userEmailHasError, value); }
        }

        public string WebserviceUrl
        {
            get { return _webserviceUrl; }
            set { Set(ref _webserviceUrl, value); }
        }

        public string InternetAvailableInfo
        {
            get { return _internetAvailableInfo; }
            set { Set(ref _internetAvailableInfo, value); }
        }

        public DateTime SelectedDate
        {
            get { return _selectedDate; }
            set 
            { 
                Set(ref _selectedDate, value);
                DisplayNavigationHeader(_selectedDate);
                if(loginTechnicianGuid != Guid.Empty)
                {
                    GetAppointmentsForSelectedDate(_selectedDate);
                }
            }
        }

        public string NavigationHeader
        {
            get { return _navigationHeader; }
            set { Set(ref _navigationHeader, value); }
        }

        public string NavigationSecondHeader
        {
            get { return _navigationSecondHeader; }
            set { Set(ref _navigationSecondHeader, value); }
        }

        public AppointmentVm SelectedListAppointment
        {
            get { return _selectedListAppointment; }
            set
            { 
                
                if (value != _selectedListAppointment)
                {
                    Set(ref _selectedListAppointment, value);
                    SelectedCalendarAppointment(_selectedListAppointment);
                }
            }
        }

        public AppointmentVm SelectedGridAppointment
        {
            get { return _selectedGridAppointment; }
            set
            {

                if (value != _selectedGridAppointment)
                {
                    Set(ref _selectedGridAppointment, value);
                    SelectedCalendarAppointment(_selectedGridAppointment);
                }
            }
        }

        public CalendarEventCollection CalendarInlineEvents { get; set; }

        public ScheduleAppointmentCollection ScheduleAppointmentCollection { get; set; }

        public ObservableCollection<AppointmentVm> AppointmentsList { get; set; }

        public ObservableCollection<AppointmentVm> DataGridAppointmentList { get; set; }

        public List<string> GroupOptionList { get; set; }

        public string SelectedGroupOption
        {
            get { return _selectedGroupOption; }
            set
            {
                if (value != _selectedGroupOption)
                {
                    Set(ref _selectedGroupOption, value);
                    if(!string.IsNullOrEmpty(value))
                    {
                        GroupListByFilter(value);
                    }
                }
            }
        }

        public string SearchbarText
        {
            get { return _searchbarText; }
            set
            {
                if (value != _searchbarText)
                {
                    Set(ref _searchbarText, value);
                    SearchForString(value);
                }
            }
        }

        public SfDataGrid SfDataGrid
        {
            get { return _sfDataGrid; }
            set { Set(ref _sfDataGrid, value); }
        }

        public string TerminCount
        {
            get { return _terminCount; }
            set { Set(ref _terminCount, value); }
        }

        public ObservableCollection<TechnicianAppointmentVM> TechnicianAppointmentVMs { get; set; }

        public bool IsLiveSyncOn
        {
            get { return _isLiveSyncOn; }
            set 
            {
                if (isInitFinished && _isLiveSyncOn != value)
                {
                    UpdateLiveSyncState(value);
                }
                Set(ref _isLiveSyncOn, value);
            }
        }

        public bool IsAdminTestUser
        {
            get { return _isAdminTestUser; }
            set { Set(ref _isAdminTestUser, value); }
        }

        #endregion

        #region Public Methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is Guid guid)
            {
                loginTechnicianGuid = guid;
                ReadDbAppointments(guid);
                SelectedDate = DateTime.Now;
                isInitFinished = true;
                AssignDeviceInformation();
            }
            return base.SetupAsync(navigationData);
        }

        #endregion

        #region Private Methods

        private void InitilizeProperties()
        {
            try
            {
                AppointmentsList = new ObservableCollection<AppointmentVm>();
                ScheduleAppointmentCollection = new ScheduleAppointmentCollection();
                CalendarInlineEvents = new CalendarEventCollection();
                DataGridAppointmentList = new ObservableCollection<AppointmentVm>();
                TechnicianAppointmentVMs = new ObservableCollection<TechnicianAppointmentVM>();

                GroupOptionList = new List<string>()
                {
                    "<Löschen Gruppierung>",
                    "Auftragsnummer",
                    "Datum"
                };
                SfDataGrid = new SfDataGrid();
                isInitFinished = false;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to initialize page.");
                throw;
            }
        }

        private void FillAppointmentList(List<AppointmentTechnician> technicianList)
        {
            foreach(AppointmentTechnician technicianAppointment in technicianList)
            {
                Address aeAddress = technicianAppointment.Appointment.Order.Abrechnungseinheit.Address;
                Order order = technicianAppointment.Appointment.Order;
                OrderState orderState = order.OrderState;
                bool hasNote = false;
                string dateTime = technicianAppointment.Appointment.From.Date.ToShortDateString();

                if (!string.IsNullOrEmpty(order.Note))
                {
                    hasNote = true;
                }

                AppointmentVm appointmentVm = new AppointmentVm()
                {
                    Guid = technicianAppointment.Appointment.Guid,
                    StartTime = technicianAppointment.Appointment.From,
                    EndTime = technicianAppointment.Appointment.To,
                    HasNote = hasNote,
                    DateTime = dateTime,
                    OrderState = orderState,
                    OrderLabel = order.Label,
                    OrderNumber = order.Number,
                    Address = string.Format("{0} {1}, {2} {3}", aeAddress.Street, aeAddress.StreetNumber, aeAddress.Zipcode, aeAddress.City),
                    OrderNote = order.Note,
                    ParentViewModel = this
                };
                DataGridAppointmentList.Add(appointmentVm);
            }

            TerminCount = $"Es sind {DataGridAppointmentList.Count()} Termine in der Auftragsliste vorhanden.";
        }

        private void SearchForString(string searchBarText)
        {
            try
            {
                if (string.IsNullOrEmpty(searchBarText))
                {
                    SfDataGrid.View.Filter = null;
                    SfDataGrid.View.RefreshFilter();
                    return;
                }

                SfDataGrid.View.Filter = FilerRecords;
                SfDataGrid.View.RefreshFilter();
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to search for value in searchbar!");
                throw;
            }
        }

        private void DisplayNavigationHeader(DateTime selectedDate)
        {
            try
            {
                if (selectedDate == DateTime.MinValue)
                {
                    return;
                }

                NavigationHeader = "Termine für ";
                if (selectedDate.ToShortDateString() == DateTime.Now.ToShortDateString())
                {
                    NavigationHeader = NavigationHeader + "heute, " + _selectedDate.ToShortDateString();
                }
                else
                {
                    var day = selectedDate.ToString("dddd");
                    NavigationHeader = NavigationHeader + day + ", " + selectedDate.ToShortDateString();
                }
            }
            catch(Exception e)
            {
                logger.Error(e, "Error while assigning the NavigationHeader value");
                throw;
            }
        }

        private void DisplaySecondNavigationHeader(List<AppointmentTechnician> appointmentsListForToday)
        {
            try
            {
                int appointmentCount = AppointmentsList.Count;
                List<DomainDevice> allDevices = appointmentsListForToday.SelectMany(x => x.Appointment.AppointmentNutzeinheiten).SelectMany(y => y.Nutzeinheit.Devices).ToList();
                List<DomainDevice> installedDevices = allDevices.Where(x => x.IsMaintained).ToList();

                NavigationSecondHeader = $"Es sind: {appointmentCount} Termine vorhanden.\nEs sind {allDevices.Count()} Geräte in diesem Termin vorhanden.\nEs wurden {installedDevices.Count} Geräte montiert.";
            }
            catch (Exception e)
            {
                logger.Error(e, "Error while assigning the NavigationHeader value");
                throw;
            }
        }

        private void AssignDeviceInformation()
        {
            User user = appDeviceInformationService.GetAppUser();

            if(user.Name != "AdminTest")
            {
                IsAdminTestUser = true;
            }
            else
            {
                IsAdminTestUser = false;
            }

            IsInAdminMode = false;
            DeviceModel = DeviceInfo.Model;
            DeviceManufacturer = DeviceInfo.Manufacturer;
            DevicePlatform = DeviceInfo.Platform.ToString();
            DevicePlatformVersion = DeviceInfo.VersionString;
            AppVersion = appDeviceInformationService.AppVersion + " Version: " + Xamarin.Essentials.AppInfo.VersionString;
            DatabaseVersion = appDeviceInformationService.DatabaseVersion;

            bool currentLiveSyncState = loginService.GetUserLiveSyncState();

            if(IsLiveSyncOn != currentLiveSyncState)
            {
                IsLiveSyncOn = currentLiveSyncState;
            }

            if (Connectivity.NetworkAccess != Xamarin.Essentials.NetworkAccess.Internet)
            {
                InternetAvailableInfo = "Not Connected";
            }
            else
            {
                InternetAvailableInfo = "Connected";
            }

            User appUser = appDeviceInformationService.GetAppUser();
            Username = appUser.Name;
            UserEmail = appUser.Email;

            Domain.Eras2App.Database.Customer appCustomer = appDeviceInformationService.GetAppCustomer();
            Customername = appCustomer.Name;

            Webservice appWebService = appDeviceInformationService.GetAppWebservice();
            WebserviceUrl = appWebService.Url;
        }

        private void ReadDbAppointments(Guid technicianGuid)
        {
            try
            {
                List<AppointmentTechnician> technicianList = appointmentService.GetTechnitianAppointments(technicianGuid);
                technicianList = technicianList.Where(x => !x.Appointment.Order.Number.Contains("#x#")).ToList();
                AppAppointments = technicianList;
                var uniqueDateList = technicianList.GroupBy(x => x.Appointment.From.Date).Select(x => new { Date = x.Key, Appointment = x.Select(z => z.Appointment).First() });

                foreach (var technicianAppointment in uniqueDateList)
                {
                    CalendarInlineEvent calendarEvent = new CalendarInlineEvent
                    {
                        StartTime = technicianAppointment.Appointment.From,
                        EndTime = technicianAppointment.Appointment.To,
                        Subject = technicianAppointment.Appointment.Order.Label,
                        Color = GetAppointmentColor()
                    };

                    CalendarInlineEvents.Add(calendarEvent);
                }
                ShowCalendar = true;
            }
            catch (Exception e)
            {
                logger.Error(e, "Error while loading the database appointments!");
                throw;
            }
        }

        private void FillTreeViewList(List<AppointmentTechnician> technicianList)
        {
            TechnicianAppointmentVMs.Clear();
            IEnumerable<DateTime?> allNextAppointments = technicianList
                .Select(x => x.Appointment)
                .SelectMany(y => y.AppointmentNutzeinheiten)
                .Select(z => z.Nutzeinheit)
                .SelectMany(t => t.Nutzer)
                .Select(n => n.NextAppointmentDate)
                .Where(a => a.HasValue);

            if (!allNextAppointments.Any())
            {
                return;
            }

            ///////////////////TEMPORARY TRASH CODE/////////////////////////////////////
            globalCacheTrash.LastSecondAppointment = allNextAppointments.Select(t => t.Value).Max();
            /////////////////////////////////////////////////////////////////////////////

            List<string> newAppointmentDayList = allNextAppointments
                .Select(t => t.Value)
                .OrderBy(x => x.Date)
                .Select(x => x.ToString("dddd dd/MM/yyyy")).Distinct().ToList();

            foreach (string appointmentDay in newAppointmentDayList)
            {
                TechnicianAppointmentVM dateVM = new TechnicianAppointmentVM
                {
                    FileName = appointmentDay
                };

                dateVM.NutzerList = technicianList.Select(x => x.Appointment)
                        .SelectMany(y => y.AppointmentNutzeinheiten)
                        .Select(z => z.Nutzeinheit)
                        .SelectMany(t => t.Nutzer).Distinct()
                        .Where(x => x.NextAppointmentDate.HasValue && x.NextAppointmentDate.Value.Date.ToString("dddd dd/MM/yyyy") == appointmentDay).ToList();

                List<DateTime> newAppointmentTimeList = technicianList.Select(x => x.Appointment)
                .SelectMany(y => y.AppointmentNutzeinheiten)
                .Select(z => z.Nutzeinheit)
                .SelectMany(t => t.Nutzer)
                .Select(x => x.NextAppointmentDate)
                .Where(x => x.HasValue && x.Value.ToString("dddd dd/MM/yyyy") == appointmentDay && x.Value != null)
                .Select(x => x.Value).Distinct()
                .OrderBy(y => y.Hour).ToList();

                dateVM.AppointmentTermine = new ObservableCollection<TechnicianAppointmentVM>();

                foreach (DateTime timeAppointment in newAppointmentTimeList)
                {
                    DateTime appointmentStartTime = timeAppointment;
                    DateTime appointmentEndTime = timeAppointment.AddHours(2); 

                    TechnicianAppointmentVM timeVM = new TechnicianAppointmentVM
                    {
                        FileName = appointmentStartTime.ToShortTimeString() + " - " + appointmentEndTime.ToShortTimeString()
                    };

                    dateVM.AppointmentTermine.Add(timeVM);
                    timeVM.AppointmentTermine = new ObservableCollection<TechnicianAppointmentVM>();

                    List<Nutzer> nutzerList = technicianList.Select(x => x.Appointment)
                        .SelectMany(y => y.AppointmentNutzeinheiten)
                        .Select(z => z.Nutzeinheit)
                        .SelectMany(t => t.Nutzer).Distinct()
                        .Where(x => x.NextAppointmentDate.HasValue && x.NextAppointmentDate.Value == appointmentStartTime).ToList();

                    timeVM.NutzerList = nutzerList;

                    foreach (Nutzer nutzer in nutzerList)
                    {
                        string address = nutzer.Nutzeinheit.Address.Street + " " + nutzer.Nutzeinheit.Address.StreetNumber + " "
                            + nutzer.Nutzeinheit.Address.Zipcode + " " + nutzer.Nutzeinheit.Address.City;

                        TechnicianAppointmentVM nutzeinheitVM = new TechnicianAppointmentVM
                        {
                            NutzerGuid = nutzer.Guid,
                            FileName = nutzer.Name1 + " " + nutzer.Name2 + ", " + address,
                            Appointment = technicianList.Select(x => x.Appointment).FirstOrDefault(x => x.AppointmentNutzeinheiten
                                                        .Select(y => y.Nutzeinheit)
                                                        .SelectMany(u => u.Nutzer).Any(p => p.Guid == nutzer.Guid))
                        };
                        dateVM.NutzerGuid = nutzer.Guid;
                        timeVM.AppointmentTermine.Add(nutzeinheitVM);
                    }
                }

                TechnicianAppointmentVMs.Add(dateVM);
            }
        }

        private void GetAppointmentsForSelectedDate(DateTime selectedDate)
        {
            try
            {
                int day = selectedDate.Day;
                int month = selectedDate.Month;
                int year = selectedDate.Year;

                List<AppointmentTechnician> daysAppointments = AppAppointments.Where(x => x.Appointment.From.Day == day &&
                    x.Appointment.From.Month == month &&
                    x.Appointment.From.Year == year & !x.Appointment.Order.Number.Contains("_#x#")).ToList();

                daysAppointments.Sort((x, y) => DateTime.Compare(x.Appointment.From, y.Appointment.From));

                AppointmentsList.Clear();

                foreach (AppointmentTechnician dbAppointment in daysAppointments)
                {
                    Order order = dbAppointment.Appointment.Order;
                    OrderState orderState = order.OrderState;
                    Address aeAddress = order.Abrechnungseinheit.Address;
                    bool hasNote = false;

                    if(!string.IsNullOrEmpty(order.Note))
                    {
                        hasNote = true;
                    }

                    List<NutzeinheitOrderState> appointmentNutzeinheits = dbAppointment.Appointment.AppointmentNutzeinheiten.Select(x => x.Nutzeinheit)
                                                                                                    .SelectMany(x => x.OrderStates)
                                                                                                    .Where(y => y.OrderGuid == order.Guid).ToList();

                    List<NutzeinheitOrderState> completedNe = appointmentNutzeinheits.Where(x => x.ProcessState == ProcessState.Completed).ToList();
                    List<NutzeinheitOrderState> startedProgressNe = appointmentNutzeinheits.Where(x => x.ProcessState == ProcessState.Updating).ToList();

                    ProcessState nutzeinheitenStates = ProcessState.InProgress;
                    int sumOfCompletedAndUpdatedNe = completedNe.Count() + startedProgressNe.Count();

                    if (appointmentNutzeinheits.Count() == 0)
                    {
                        nutzeinheitenStates = ProcessState.InProgress;
                    }
                    else if (sumOfCompletedAndUpdatedNe == appointmentNutzeinheits.Count())
                    {
                        nutzeinheitenStates = ProcessState.Completed;
                    }
                    else if (startedProgressNe.Any() || completedNe.Any())
                    {
                        nutzeinheitenStates = ProcessState.Updating;
                    }
                    else
                    {
                        nutzeinheitenStates = ProcessState.InProgress;
                    }

                    AppointmentVm appointment = new AppointmentVm
                    {
                        Guid = dbAppointment.Appointment.Guid,
                        HasNote = hasNote,
                        OrderState = orderState,
                        NutzeinheitenState = nutzeinheitenStates,
                        OrderNumber = order.Number,
                        StartTime = dbAppointment.Appointment.From,
                        EndTime = dbAppointment.Appointment.To,
                        OrderLabel = order.Label,
                        Address = string.Format("{0} {1} {2} {3}", aeAddress.Street, aeAddress.StreetNumber, aeAddress.Zipcode, aeAddress.City),
                        ParentViewModel = this
                    };
                    AppointmentsList.Add(appointment);
                }

                DisplaySecondNavigationHeader(daysAppointments);

            }
            catch(Exception e)
            {
                logger.Error(e, "Error while loading appointment for the day!");
                throw;
            }
        }

        private async void SelectedScheduleAppointmentExecute(object obj)
        {
            try
            {
                if (obj is CellTappedEventArgs args && args.Appointment != null)
                {
                    await navigationService.NavigateToAsync<OrderPageViewModel>(args.Appointment);
                }
            }
            catch(Exception e)
            {
                logger.Error(e, "Error while processing selected appointment!");
                throw;
            }
        }

        private async void SelectedCalendarAppointment(AppointmentVm appointment)
        {
            try
            {
                await navigationService.NavigateToAsync<OrderPageViewModel>(appointment);
            }
            catch (Exception e)
            {
                logger.Error(e, "Error while processing selected appointment");
                throw;
            }
        }

        private Color GetAppointmentColor()
        {
            return Color.FromHex("#538EEC");
        }

        private void EnableAdminModeExecute()
        {
            try
            {
                enableAdminModeCount++;
                if (enableAdminModeCount == 10)
                {
                    IsInAdminMode = true;
                }
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured during enabling admin mode!");
                throw;
            }
        }

        private void UpdateUserEntryExecute()
        {
            try
            {
                if (!Validate())
                {
                    return;
                }

                User appUser = appDeviceInformationService.GetAppUser();
                appUser.Email = UserEmail;
                appDeviceInformationService.UpdateUser(appUser);
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured during update to user db!");
                throw;
            }
        }

        private async void LocalBackupExecute()
        {
            try
            {
                dialogService.ShowBusyIndicator(AnimationTypes.Globe, "Das Backup wird ausgeführt. Bitte warten...");
                await Task.Run(async () => await optionService.LocalBackup());
                await dialogService.AcceptAsync("Das Backup wurde erfolgreich gespeichert.", "OK");
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured during local backup.");
                throw;
            }
            finally
            {
                dialogService.HideBusyIndicator();
            }
        }

        private async void RemoteBackupExecute()
        {
            try
            {
                dialogService.ShowBusyIndicator(AnimationTypes.Globe, "Das Backup wird übertragen. Bitte warten...");
                await Task.Run(async () => await optionService.RemoteBackup());
                await dialogService.AcceptAsync("Das Backup wurde erfolgreich gesendet.", "OK");
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured during remote backup.");
                throw;
            }
            finally
            {
                dialogService.HideBusyIndicator();
            }
        }

        private void RegisterWebserviceEventHandlers()
        {
            webservice.Info += FileInfoLogging;
            webservice.Info += DialogLogging;

            webservice.Error += FileErrorLogging;
            webservice.Error += DialogLogging;
            
            webservice.Warning += FileWarningLogging;
            webservice.Warning += DialogLogging;
        }

        private void FileErrorLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Error(arg.Label);
        }

        private void FileWarningLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Warning(arg.Label);
        }

        private void FileInfoLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Information(arg.Label);
        }

        private void UnregisterWebserviceEventHandlers()
        {
            webservice.Info -= DialogLogging;
            webservice.Info -= FileInfoLogging;

            webservice.Error -= DialogLogging;
            webservice.Error -= FileErrorLogging;

            webservice.Warning -= DialogLogging;
            webservice.Warning -= FileWarningLogging;
        }

        private void DialogLogging(object sender, WebserviceEventArgs arg)
        {
            Device.BeginInvokeOnMainThread(() =>
                {
                    dialogService.SetBusyIndicatorText(arg.Label);
                });
        }

        private async void SyncOrdersExecute()
        {
            try
            {
                if (Connectivity.NetworkAccess != Xamarin.Essentials.NetworkAccess.Internet)
                {
                    await dialogService.AcceptAsync(localisationService.Get("NetworkUnavailableForSyncOrders"), localisationService.Get("Ok"), localisationService.Get("Information"));
                    InternetAvailableInfo = "Not Connected";
                    return;
                }

                Device.BeginInvokeOnMainThread(() =>
                {
                    dialogService.ShowBusyIndicator(AnimationTypes.Globe, "Nutzeinheiten überprüfen");
                });

                InternetAvailableInfo = "Connected";
                bool unfinishedNeExist = await Task.Run(() => RunNutzeinheitProcessStateCheck());

                if(unfinishedNeExist)
                {
                    DialogResponse response = await dialogService.AcceptDeclineAsync(localisationService.Get("UnfinishedNutzeinheit"), "OK", "Cancel");
                    if (response == DialogResponse.Decline)
                    {
                        return;
                    }
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to check nutzeinheit states.");
                throw;
            }
            finally
            {
                dialogService.HideBusyIndicator();
            }
            
            try
            {
                dialogService.ShowBusyIndicator(AnimationTypes.Globe, localisationService.Get("LoginInProgress"));

                await optionService.SyncOrdersAsync();
            }
            catch (UnauthorizedAccessException exp)
            {
                logger.Warning(exp, "SyncOrdersExecute(): UnauthorizedAccessException.");
                await ShowSyncOrdersErrorDialog(exp.Message);
            }
            catch (WebServiceException exp) when (exp.IsUnauthorized())
            {
                logger.Warning(exp, "SyncOrdersExecute(): WebServiceException.");
                await ShowSyncOrdersErrorDialog(localisationService.Get("UserLoginFailed4"));
            }
            catch (WebException exp) when (webservice.IsServerNotAvailableStatusError(exp))
            {
                string message = localisationService.Get("UserLoginFailed7");
                logger.Error(exp, localisationService.Get(message));
                await ShowSyncOrdersErrorDialog(message);
            }
            catch (TimeoutRejectedException exp)
            {
                logger.Error(exp, "LoginExecute(): TimeoutRejectedException.");
                await ShowSyncOrdersErrorDialog("Verbindung konnte nicht hergestellt werden...");
            }
            catch (Exception exp)
            {
                logger.Error(exp, "LoginExecute(): Exception.");
                await ShowSyncOrdersErrorDialog(localisationService.Get("UserLoginFailed5"));
            }
            finally
            {
                await Task.Delay(750);
                dialogService.HideBusyIndicator();
            }
            try
            {
                ReloadNextAppointmentList();
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to load db data after sync with remote db!");
                throw;
            }
        }

        private void ReloadNextAppointmentList()
        {
            CalendarInlineEvents.Clear();
            List<AppointmentTechnician> technicianList = appointmentService.GetTechnitianAppointments(loginTechnicianGuid);
            technicianList = technicianList.Where(x => !x.Appointment.Order.Number.Contains("#x#")).ToList();
            var uniqueDateList = technicianList.GroupBy(x => x.Appointment.From.Date).Select(x => new { Date = x.Key, Appointment = x.Select(z => z.Appointment).First()});

            foreach (var technicianAppointment in uniqueDateList)
            {
                CalendarInlineEvent calendarEvent = new CalendarInlineEvent
                {
                    StartTime = technicianAppointment.Appointment.From,
                    EndTime = technicianAppointment.Appointment.To,
                    Subject = technicianAppointment.Appointment.Order.Label,
                    Color = GetAppointmentColor()
                };

                CalendarInlineEvents.Add(calendarEvent);
            }
            AppAppointments = technicianList;
            GetAppointmentsForSelectedDate(SelectedDate);
        }

        private Task<bool> RunNutzeinheitProcessStateCheck()
        {
            bool isAnyNeUnfinished = false;

            if (IsAnyNutzeinheitUnfinished())
            {
                isAnyNeUnfinished = true;
            }

            return Task.FromResult(isAnyNeUnfinished);
        }

        private bool IsAnyNutzeinheitUnfinished()
        {
            return nutzeinheitService.DoesUnfinishNutzeinheitExist();
        }

        private async Task ShowSyncOrdersErrorDialog(string message)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            await dialogService.AcceptAsync(message, localisationService.Get("Ok"), localisationService.Get("Error"));
        }

        private async void DeleteAmwDatabaseExecute()
        {
            try
            {
                DialogResponse response = await dialogService.AcceptDeclineAsync("Sind Sie sicher, dass Sie die Datenbank löschen wollen?", "JA", "NEIN");
                if (response == DialogResponse.Accept)
                {
                    optionService.DeleteAmwDatabase();
                    await navigationService.InitializeAsync<LoginPageViewModel>();
                    await navigationService.RemoveBackStackAsync();
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured during delete database.");
                throw;
            }
        }

        private async void FactoryResetExecute()
        {
            try
            {
                DialogResponse response = await dialogService.AcceptDeclineAsync("Sind Sie sicher, dass Sie die App zurücksetzen möchten?", "JA", "NEIN");
                if (response == DialogResponse.Accept)
                {
                    optionService.FactoryReset();
                    await navigationService.InitializeAsync<RegistrationPageViewModel>();
                    await navigationService.RemoveBackStackAsync();
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured during factory reset.");
                throw;
            }
        }

        private bool Validate()
        {
            var validator = new AppointmentPageViewModelValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }

        private void IconButtonExecute(Guid guid)
        {
            try
            {
                Appointment appointment = appointmentService.GetAppointment(guid);
                string orderNote = appointment.Order.Note;
                dialogService.AcceptAsync(orderNote,"OK","Auftrag Information");
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to execute the command assign to the info Icon!");
                throw;
            }
        }

        private void AppearingExecute()
        {
            try
            {
                RegisterWebserviceEventHandlers();
                if (AppAppointments != null)
                {
                    List<AppointmentTechnician> technicianList = appointmentService.GetTechnitianAppointments(loginTechnicianGuid);
                    technicianList = technicianList.Where(x => !x.Appointment.Order.Number.Contains("#x#")).ToList();
                    AppAppointments = technicianList;
                    FillTreeViewList(AppAppointments);
                    GetAppointmentsForSelectedDate(SelectedDate);
                }
            }
            catch (Exception exception)
            {
                logger.Error(exception, "Exception occured while executing Appearing AppointmentPage Method!");
                throw;
            }
        }

        private void DisappearingExecute()
        {
            try
            {
                UnregisterWebserviceEventHandlers();
            }
            catch (Exception exception)
            {
                logger.Error(exception, "DisappearingExecute failed");
                throw;
            }
        }

        public bool FilerRecords(object appointment)
        {
            try
            {
                bool checkNumeric = double.TryParse(SearchbarText, out double res);
                bool checkDateTime = DateTime.TryParse(SearchbarText, out DateTime time);

                var item = appointment as AppointmentVm;
                if (item != null && SearchbarText.Equals(""))
                {
                    return true;
                }
                else
                {
                    if (item != null)
                    {
                        if (checkNumeric)
                        {
                            bool result = MakeNumericFilter(item, "OrderNumber", "Equals");
                            return result;
                        }
                        else if(checkDateTime)
                        {
                            if (item.StartTime.ToString().Contains(SearchbarText) ||
                                item.EndTime.ToString().Contains(SearchbarText))
                                return true;
                            return false;
                        }
                        else
                        {
                            bool result = MakeStringFilter(item, "Address","Contains");
                            return result;
                        }
                    }
                }
                return false;
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to filter the appointments!");
                throw;
            }
        }

        private bool MakeStringFilter(AppointmentVm appointment, string option, string condition)
        {
            var value = appointment.GetType().GetProperty(option);
            var exactValue = value.GetValue(appointment, null);
            exactValue = exactValue.ToString().ToLower();
            string text = SearchbarText.ToLower();
            var methods = typeof(string).GetMethods();
            if (methods.Count() != 0)
            {
                var methodInfo = methods.FirstOrDefault(method => method.Name == condition);
                bool result1 = (bool)methodInfo.Invoke(exactValue, new object[] { text });
                return result1;
            }
            else
                return false;
        }

        private bool MakeNumericFilter(AppointmentVm appointment, string option, string condition)
        {
            var value = appointment.GetType().GetProperty(option);
            var exactValue = value.GetValue(appointment, null);
            bool checkNumeric = double.TryParse(exactValue.ToString(), out _);
            if (checkNumeric)
            {
                switch (condition)
                {
                    case "Equals":
                        try
                        {
                            if (exactValue.ToString().Contains(SearchbarText))
                            {
                                return true;
                            }
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(e);
                        }
                        break;
                    case "NotEquals":
                        try
                        {
                            if (Convert.ToDouble(SearchbarText) != Convert.ToDouble(exactValue))
                                return true;
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(e);
                            return true;
                        }
                        break;
                }
            }
            return false;
        }

        private void GroupListByFilter(string selectedGroup)
        {
            try
            {
                string columnName;
                if(selectedGroup == "Auftragsnummer")
                {
                    columnName = "OrderNumber";
                }
                else if(selectedGroup == "Datum")
                {
                    columnName = "DateTime";
                }
                else
                {
                    columnName = "";
                }

                if(string.IsNullOrEmpty(columnName))
                {
                    SfDataGrid.GroupColumnDescriptions.Clear();
                    SelectedGroupOption = "";
                    return;
                }


                SfDataGrid.GroupColumnDescriptions.Add(new GroupColumnDescription()
                {
                    ColumnName = columnName,
                });
                
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to group the DataGrid!");
                throw;
            }
        }

        private void ShowTerminInfoExecute()
        {
            try
            {
                dialogService.AcceptAsync(NavigationSecondHeader, "OK", "Termin Information");
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to display termin informaton dialog");
                throw;
            }
        }

        private async void TreeViewItemTappedExecute(object itemTapped)
        {
            try
            {
                if(itemTapped is TreeViewNode selectedItem && !selectedItem.HasChildNodes)
                {
                    if (selectedItem.Content is TechnicianAppointmentVM content)
                    {
                        Appointment appointment = content.Appointment;

                        Nutzer nutzer = nutzerService.GetNutzer(content.NutzerGuid);
                        if(nutzer != null)
                        {
                            NutzeinheitVM nutzeinheitVM = CreateNutzeinheitVm(nutzer, appointment);
                            await navigationService.NavigateToAsync<NutzeinheitPageViewModel>(nutzeinheitVM);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to display termin informaton dialog");
                throw;
            }
        }

        private NutzeinheitVM CreateNutzeinheitVm(Nutzer nutzer, Appointment appointment)
        {
            Nutzeinheit nutzeinheit = nutzer.Nutzeinheit;
            Guid orderGuid = appointment.Order.Guid;
            NutzeinheitOrder nutzeinheitOrder = new NutzeinheitOrder(orderGuid, nutzeinheit.Guid);

            string nutzerSalutationAndName = nutzerService.GetNutzerTitleAndSalutation(nutzer) + nutzerService.GetNutzerName(nutzer);
            string nutzeinheitLocation = nutzeinheit.Location + " " + nutzeinheit.WalkSequence;
            string nutzerAddress = nutzerService.GetNutzerAddress(nutzeinheit);

            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheit.OrderStates.Where(x => x.OrderGuid == orderGuid).Single();
            ProcessState nutzeinheitProcessState = nutzeinheitOrderState.ProcessState;

            bool isNeLocked = false;
            if (nutzeinheitProcessState == ProcessState.Completed)
            {
                isNeLocked = true;
            }

            return new NutzeinheitVM()
            {
                ParentViewModel = this,
                NutzeinheitGuid = nutzeinheit.Guid,
                NutzerGuid = nutzer.Guid,
                AppointmentDate = appointment.From,
                NutzeinheitOrder = nutzeinheitOrder,
                NutzerNameLocation = $"{nutzerSalutationAndName},{nutzeinheitLocation}",
                NutzeinheitNote = nutzeinheit.Note,
                NutzeinheitNumber = nutzeinheit.Number,
                NutzeinheitAddress = nutzerAddress,
                NutzeinheitState = nutzeinheitProcessState,
                NutzerKind = nutzer.Kind,
                IsNeLocked = isNeLocked,
                HasNewAppointment = nutzer.NextAppointmentDate.HasValue,
                NutzeinheitUiState = new NutzeinheitUiState()
                {
                    OrderStates = nutzeinheitService.GetNutzeinheitDevicesOrderStates(nutzeinheit, orderGuid),
                    HasSignature = nutzeinheitService.IsNutzeinheitSigned(nutzeinheit, orderGuid),
                    NutzeinheitProcessState = nutzeinheitProcessState,
                    AmwInfoKeyGuid = nutzeinheitOrderState.AmwInfoKeyGuid
                }
            };
        }

        private void TabChangedExecute(SelectionChangedEventArgs selectedTab)
        {
            if (selectedTab == null)
            {
                return;
            }

            if (selectedTab.Name == "Aufträge Liste")
            {
                if (DataGridAppointmentList.Count != 0)
                {
                    return;
                }
                FillAppointmentList(AppAppointments);
            }
            else if(selectedTab.Name == "Nachtermine")
            {
                if (TechnicianAppointmentVMs.Count != 0)
                {
                    return;
                }
                FillTreeViewList(AppAppointments);
            }
        }

        private async void NeListExecute(Guid appointmentGuid)
        {
            try
            {
                Appointment appointment = appointmentService.GetAppointmentWithNutzeinheitAddresses(appointmentGuid);
                var listOfNeAddresses = appointment.Order.Abrechnungseinheit.Nutzeinheiten.Select(x => x.Address);
                var listOfUniqueNeAddresses = listOfNeAddresses.GroupBy(x => new { x.Street, x.StreetNumber }).Select(x => new { x.Key.Street, x.Key.StreetNumber });

                Dictionary<string, string> dictOfAddressNutzeinheiten = new Dictionary<string, string>();

                foreach(var neAddress in listOfUniqueNeAddresses)
                {
                    int neCount = listOfNeAddresses.Where(x => x.Street == neAddress.Street && x.StreetNumber == neAddress.StreetNumber).Count();
                    dictOfAddressNutzeinheiten.Add(neAddress.Street + " " + neAddress.StreetNumber, neCount.ToString());
                }
                await appointmentNutzeinheitDetailsDialog.ShowAppointmentNutzeinheitInfo(dictOfAddressNutzeinheiten);
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to load Nutzeinheit Listed in AppointmentPageVM!");
                throw;
            }
        }

        private void UpdateLiveSyncState(bool liveSyncState)
        {
            loginService.UpdateUserLiveSyncState(liveSyncState);
        }

        public async Task DisplayExitWarning()
        {
            DialogResponse response = await dialogService.AcceptDeclineAsync("Sind Sie sicher, dass Sie die Anwendung schließen wollen ?", "JA", "NEIN");
            if (response == DialogResponse.Accept)
            {
                closeApplicationService.CloseApplication();
            }
        }

        #endregion
    }
}
