﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="RegistrationPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Interfaces;
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Xamarin.Forms;
    using ZXing;
    using ZXing.Mobile;

    public class RegistrationPageViewModel : EcViewModelBase
    {
        #region fields

        private readonly IEcDialogService dialogService;
        private readonly IRegistrationService registrationService;

        //ZXING CONFIG
        private const int DelayBetweenAnalyzingFramesValue = 150;
        private const int InitialDelayBeforeAnalyzingFramesValue = 300;
        private const int DelayBetweenContinuousScansValue = 1000;
        private const bool UseNativeScanning = false;

        private string _appTitle;

        private Command loadQrCodeCommand;
        private Command loadTestAdminCommand;

        #endregion

        public string AppTitle
        {
            get { return _appTitle; }
            set { Set(ref _appTitle, value); }
        }

        #region ctor

        public RegistrationPageViewModel(
            IServiceLocator serviceLocator,
            IEcDialogService dialogService,
            IEcNavigationService navigationService,
            IRegistrationService registrationService)
            : base(serviceLocator, navigationService)
        {
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.registrationService = registrationService ?? throw new ArgumentNullException(nameof(registrationService));

            AppTitle = registrationService.GetAppTitle();
        }

        #endregion

        #region commands

        public Command LoadQrCodeCommand => loadQrCodeCommand ?? (loadQrCodeCommand = new Command(LoadQrCodeExecute));

        public Command LoadTestAdminCommand => loadTestAdminCommand ?? (loadTestAdminCommand = new Command(LoadTestAdminExecute));

        #endregion

        #region private methods

        private async void LoadQrCodeExecute()
        {
            try
            {
                Result result = await ReadQrCode();
               
                if (result == null)
                {
                    return;
                }

                string resultText = result.Text;
                if (string.IsNullOrEmpty(resultText))
                {
                    await ShowQrCodeError();
                    return;
                }

                string[] splittedResultText = resultText.Split(' ');
                if (splittedResultText.Length != 2)
                {
                    await ShowQrCodeError();
                    return;
                }

                var config = new WebserviceCustomerConfig()
                {
                    CustomerName = splittedResultText[0],
                    Url = splittedResultText[1]
                };

                registrationService.UpdateWebserviceCustomer(config);

                await navigationService.InitializeAsync<LoginPageViewModel>();
                await navigationService.RemoveBackStackAsync();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to load QrCode data!");
                throw;
            }
            finally
            {
                navigationService.ShowInitPageOnResume = true;
            }
        }

        private async void LoadTestAdminExecute()
        {
            try
            {
                var config = new WebserviceCustomerConfig()
                {
                    CustomerName = "AdminTest",
                    Url = "AdminTest"
                };

                registrationService.UpdateWebserviceCustomer(config);

                await navigationService.InitializeAsync<LoginPageViewModel>();
                await navigationService.RemoveBackStackAsync();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to load data for TestAdmin!");
                throw;
            }
            finally
            {
                navigationService.ShowInitPageOnResume = true;
            }
        }
        
        private async Task ShowQrCodeError()
        {
            await dialogService.AcceptAsync("Der QrCode hat das falsche Format", localisationService.Get("Ok"), localisationService.Get("Error")); 
        }

        private async Task<Result> ReadQrCode()
        {
            var scanner = new MobileBarcodeScanner();
            var opt = new MobileBarcodeScanningOptions
            {
                DelayBetweenAnalyzingFrames = DelayBetweenAnalyzingFramesValue,
                InitialDelayBeforeAnalyzingFrames = InitialDelayBeforeAnalyzingFramesValue,
                DelayBetweenContinuousScans = DelayBetweenContinuousScansValue,
                UseNativeScanning = UseNativeScanning,
                PossibleFormats = new List<BarcodeFormat>() { BarcodeFormat.QR_CODE }
            };

            navigationService.ShowInitPageOnResume = false;
            Result result = await scanner.Scan(opt);
            return result;
        }

        #endregion

    }

}
