﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:buttons="clr-namespace:Syncfusion.XForms.Buttons;assembly=Syncfusion.Buttons.XForms"
             xmlns:inputLayout="clr-namespace:Syncfusion.XForms.TextInputLayout;assembly=Syncfusion.Core.XForms"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:behaviors="clr-namespace:Eras2AmwApp.Behaviors;assembly=Eras2AmwApp"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.LoginPage">

    <ContentPage.Behaviors>
        <behaviors:EventToCommandBehavior EventName="Appearing" Command="{Binding AppearingCommand}" />
        <behaviors:EventToCommandBehavior EventName="Disappearing" Command="{Binding DisappearingCommand}" />
    </ContentPage.Behaviors>

    <Grid BackgroundColor="White">

        <Grid.RowDefinitions>
            <RowDefinition Height="25*"></RowDefinition>
            <RowDefinition Height="15*"></RowDefinition>
            <RowDefinition Height="15*"></RowDefinition>
            <RowDefinition Height="18*"></RowDefinition>
            <RowDefinition Height="10*"></RowDefinition>
            <RowDefinition Height="10*"></RowDefinition>
            <RowDefinition Height="10*"></RowDefinition>
            <RowDefinition Height="7*"></RowDefinition>
            <RowDefinition Height="7*"></RowDefinition>
            <RowDefinition Height="9*"></RowDefinition>
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="20*" />
            <ColumnDefinition Width="10*" />
            <ColumnDefinition Width="10*" />
            <ColumnDefinition Width="10*" />
            <ColumnDefinition Width="10*" />
            <ColumnDefinition Width="10*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Label  Text="{Binding AppTitle}"
                Grid.Row="0"
                Grid.Column="1"
                Grid.ColumnSpan="3"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                TextColor="Black"
                FontSize="35"/>

        <Label  Text="Anmelden"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                Grid.Row="1"
                Grid.Column="1"
                Grid.ColumnSpan="3"
                TextColor="Black"
                FontSize="32"/>

        <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                        Margin="5" 
                                        Hint="Nutzername:" 
                                        ContainerType="Outlined"  
                                        Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" 
                                        VerticalOptions="Center"
                                        HasError="{Binding UsernameHasError}"
                                        ErrorText="{Binding UsernameErrorText}">

            <Entry  Text="{Binding Username}" 
                    VerticalOptions="End"
                    BackgroundColor="White">
            </Entry>

        </inputLayout:SfTextInputLayout>

        <inputLayout:SfTextInputLayout  InputViewPadding="5"
                                        Margin="5" 
                                        Hint="Passwort:" 
                                        ContainerType="Outlined"  
                                        Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="3" 
                                        VerticalOptions="Start"
                                        EnablePasswordVisibilityToggle="True"
                                        HasError="{Binding PasswordHasError}"
                                        ErrorText="{Binding PasswordErrorText}">

            <Entry  Text="{Binding Password}" 
                    VerticalOptions="End"
                    BackgroundColor="White"
                    IsPassword="True">
            </Entry>

        </inputLayout:SfTextInputLayout>

        <buttons:SfButton   Margin="5,5,5,0" 
                            Text= "{markupExtensions:Localisation Login}"
                            Command="{Binding LoginCommand}" 
                            Grid.Row="4" 
                            Grid.Column="1" 
                            Grid.ColumnSpan="3" 
                            Style="{StaticResource SyncfusionButtonBlue}"/>

        <Label  Text="{Binding LoginErrorText}"
                Grid.Row="5"
                Grid.Column="1"
                Grid.ColumnSpan="4"
                TextColor="Red"
                FontSize="Small" />

        <buttons:SfButton   Margin="5,5,5,0" 
                            Text= "Nutzer löschen"
                            Command="{Binding ResetUserCommand}" 
                            Grid.Row="9" 
                            Grid.Column="4" 
                            Grid.ColumnSpan="3" 
                            Style="{StaticResource SyncfusionButtonBlue}"/>

        <buttons:SfButton   Margin="5,5,5,0" 
                            Text= "Daten löschen"
                            Command="{Binding WipeDatabaseCommand}" 
                            Grid.Row="9" 
                            Grid.Column="2" 
                            Grid.ColumnSpan="2" 
                            Style="{StaticResource SyncfusionButtonBlue}"/>

        <Label x:Name="AppVersion"
               Margin="5,5,5,0"
               Text="{Binding AppVersion}"
               Grid.Row="7"
               Grid.Column="4"
               Grid.ColumnSpan="2"
               HorizontalOptions="End"
               VerticalOptions="End">

        </Label>

        <Label x:Name="Webservice"
               Margin="5,5,10,0"
               Text="{Binding WebserviceUrl}"
               Grid.Row="8"
               Grid.Column="4"
               Grid.ColumnSpan="3"
               FontSize="Medium"
               HorizontalTextAlignment="End"
               VerticalOptions="End">

        </Label>

    </Grid>
</ContentPage>