﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="FileSystemSetup.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Bootstrap
{
    using System;
    using System.IO;
    using Common.Interfaces;
    using Serilog;

    public class FileSystemCreator
    {
        private readonly IAppSettings appSettings;

        private readonly ILogger logger;

        public FileSystemCreator(IAppSettings appSettings, ILogger logger)
        {
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public void Start()
        {
            try
            {
                CreateDatabaseDirectory();
                CreateWebserviceDirectories();
                CreatePicturesDirectory();
                CreateSignaturesDirectory();
            }
            catch (Exception e)
            {
                logger.Error(e, "FileSystem Setup failed");
                throw;
            }
        }

        private void CreateDatabaseDirectory()
        {
            if (!Directory.Exists(appSettings.DatabaseDirectory))
            {
                Directory.CreateDirectory(appSettings.DatabaseDirectory);
                string targetPath = Path.Combine(appSettings.DatabaseDirectory, "test_eras2_amw.sqlite3");
                using (var assetStream = appSettings.AssetsTestStream)
                using (var fileStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write))
                {
                    assetStream.CopyTo(fileStream);
                }
            }
        }

        private void CreateWebserviceDirectories()
        {
            if (!Directory.Exists(appSettings.WebserviceDirectory))
            {
                Directory.CreateDirectory(appSettings.WebserviceDirectory);
            }

            if (!Directory.Exists(appSettings.WebserviceDownloadDirectory))
            {
                Directory.CreateDirectory(appSettings.WebserviceDownloadDirectory);
            }

            if (!Directory.Exists(appSettings.WebserviceUploadDirectory))
            {
                Directory.CreateDirectory(appSettings.WebserviceUploadDirectory);
            }
        }

        private void CreatePicturesDirectory()
        {
            if (!Directory.Exists(appSettings.PicturesDirectory))
            {
                Directory.CreateDirectory(appSettings.PicturesDirectory);
            }
        }

        private void CreateSignaturesDirectory()
        {
            if (!Directory.Exists(appSettings.SignaturesDirectory))
            {
                Directory.CreateDirectory(appSettings.SignaturesDirectory);
            }
        }
    }
}