﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PlatformService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Droid.Services
{
    using System.IO;

    using Android.App;
    using Android.Content;
    using Common.Interfaces;

    using Endiancode.Utilities.Extensions;

    public class PlatformService : IPlatformService
    {
        public string AppVersion
        {
            get
            {
                Context context = Application.Context;
                return context.PackageManager.GetPackageInfo(context.PackageName, 0).VersionName;
            }
        }

        public virtual string RootDirectory 
        {
            get
            {
                // Get the context, this assumes you are within an Activity, Service, or have context available
                Context context = Application.Context;
                return context.FilesDir.AbsolutePath;
            }
        } 

        public string GetDownloadFolderPath => DownloadFolderPath;

        private string DownloadFolderPath
        {
            get
            {
                Java.IO.File downloadsFolder = Application.Context.GetExternalFilesDir(Android.OS.Environment.DirectoryDownloads);
                return downloadsFolder.AbsolutePath;
            }
        }

        public string ExternalPath
        {
            get
            {
                Java.IO.File[] directories = Application.Context.GetExternalFilesDirs(null);

                return Path.Combine(directories[0].AbsolutePath);
            }
        }

        public bool ExistsSdCard => !SdCardPath.IsNullOrEmpty();

        public string SdCardPath
        {
            get
            {
                Java.IO.File[] directories  = Application.Context.GetExternalFilesDirs(null);

                foreach (Java.IO.File item in directories)
                {
                    bool valid = Android.OS.Environment.InvokeIsExternalStorageRemovable(item);
                    valid &= !Android.OS.Environment.InvokeIsExternalStorageEmulated(item);

                    if (valid)
                    {
                        return item.AbsolutePath;
                    }
                }

                return null;
            }
        }

        public Stream GetAssetDatabaseStream
        {
            get
            {
                return Application.Context.Assets.Open("test_eras2_amw.sqlite3");
            }
        }
    }
}