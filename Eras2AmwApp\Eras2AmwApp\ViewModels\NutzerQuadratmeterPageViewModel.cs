﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerQuadratmeterPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.ViewModels
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using Xamarin.Essentials;
    using Xamarin.Forms;
    using Endiancode.Utilities.Extensions;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.Models;
    using Eras2AmwApp.Services;
    using System.Collections.ObjectModel;
    using Eras2Amw.Service.Server.ServiceModel.Common;
    using NutzerQuadratmeter = Domain.Eras2Amw.Models.NutzerQuadratmeter;
    using Nutzer = Domain.Eras2Amw.Models.Nutzer;

    public class NutzerQuadratmeterPageViewModel : EcViewModelBase, ISupportParentViewModel
    {
        #region fields

        private readonly INutzerService nutzerService;

        private Command saveNutzerQuadratmeterCommand;
        private Command deleteNutzerQuadratmeterCommand;
        private Command addNutzerQuadratmeterCommand;
        private Command newEmptyNutzerQuadratmeterCommand;

        private EcViewModelBase _parentViewModel;
        private NutzerQuadratmeter _selectedNutzerQuadratmeter;

        private bool _isLastDateNull;
        private bool _canNutzerQuadradmeterBeDeleted;

        private string _quadratmeterHzg;
        private string _quadratmeterWw;
        private string _quadratmeterNk;
        private DateTime _nutzerQuadratmeterFromDate;
        private DateTime? _nutzerQuadratmeterToDate;

        private NutzeinheitVM selectedNutzer;

        #endregion

        #region ctor

        public NutzerQuadratmeterPageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            INutzerService nutzerService)
           : base(serviceLocator, navigationService)
        {
            this.nutzerService = nutzerService ?? throw new ArgumentNullException(nameof(nutzerService));

            NutzerQuadratmeterObsCollec = new ObservableCollection<NutzerQuadratmeter>();
        }

        #endregion

        #region commands

        public Command AddNutzerQuadratmeterCommand => addNutzerQuadratmeterCommand ?? (addNutzerQuadratmeterCommand = new Command(AddNutzerQuadratmeterExecute));

        public Command SaveNutzerQuadratmeterCommand => saveNutzerQuadratmeterCommand ?? (saveNutzerQuadratmeterCommand = new Command(SaveNutzerQuadratmeterExecute));

        public Command DeleteNutzerQuadratmeterCommand => deleteNutzerQuadratmeterCommand ?? (deleteNutzerQuadratmeterCommand = new Command(DeleteNutzerQuadratmeterExecute, () => CanNutzerQuadradmeterBeDeleted));

        public Command NewEmptyNutzerQuadratmeterCommand => newEmptyNutzerQuadratmeterCommand ?? (newEmptyNutzerQuadratmeterCommand = new Command(NewEmptyNutzerQuadratmeterExecute, () => IsLastDateNull));

        #endregion

        #region properties

        public bool CanNutzerQuadradmeterBeDeleted
        {
            get { return _canNutzerQuadradmeterBeDeleted; }
            set { Set(ref _canNutzerQuadradmeterBeDeleted, value); }
        }

        public bool IsLastDateNull
        {
            get { return _isLastDateNull; }
            set { Set(ref _isLastDateNull, value); }
        }

        public string QuadratmeterHzg
        {
            get { return _quadratmeterHzg; }
            set { Set(ref _quadratmeterHzg, value); }
        }

        public string QuadratmeterWw
        {
            get { return _quadratmeterWw; }
            set { Set(ref _quadratmeterWw, value); }
        }

        public string QuadratmeterNk
        {
            get { return _quadratmeterNk; }
            set { Set(ref _quadratmeterNk, value); }
        }

        public DateTime NutzerQuadratmeterFromDate
        {
            get { return _nutzerQuadratmeterFromDate; }
            set { Set(ref _nutzerQuadratmeterFromDate, value); }
        }

        public DateTime? NutzerQuadratmeterToDate
        {
            get { return _nutzerQuadratmeterToDate; }
            set { Set(ref _nutzerQuadratmeterToDate, value); }
        }

        public ObservableCollection<NutzerQuadratmeter> NutzerQuadratmeterObsCollec { get; set; }

        public NutzerQuadratmeter SelectedNutzerQuadratmeter
        {
            get { return _selectedNutzerQuadratmeter; }
            set
            {
                if (value != _selectedNutzerQuadratmeter)
                {
                    Set(ref _selectedNutzerQuadratmeter, value);
                    DisplaySelectedNutzerQuadratmeter(_selectedNutzerQuadratmeter);
                }
            }
        }

        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { Set(ref _parentViewModel, value); }
        }

        public NutzerEditPageViewModel GetParentProperty()
        {
            return ParentViewModel as NutzerEditPageViewModel;
        }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is NutzeinheitVM nutzerVM)
            {
                selectedNutzer = nutzerVM;
                ParentViewModel = nutzerVM.ParentViewModel;

                Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                List<NutzerQuadratmeter> nutzerQuadratemeters = currentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();

                InitializeObservCollection(nutzerQuadratemeters);
                InitilizeProperties(currentNutzer, nutzerQuadratemeters);
            }
            return base.SetupAsync(navigationData);
        }

        #endregion

        #region private methods

        private void InitilizeProperties(Nutzer currentNutzer, List<NutzerQuadratmeter> nutzerQuadratemeters)
        {
            try
            {
                NutzerQuadratmeter currentFirstNutzerQuadratmeter = nutzerQuadratemeters.FirstOrDefault();
                if (currentFirstNutzerQuadratmeter != null)
                {
                    SelectedNutzerQuadratmeter = currentFirstNutzerQuadratmeter;
                    if (currentFirstNutzerQuadratmeter.RangeTo.HasValue)
                    {
                        IsLastDateNull = true;
                    }
                }
                else
                {
                    QuadratmeterHzg = "";
                    QuadratmeterWw = "";
                    QuadratmeterNk = "";
                    NutzerQuadratmeterFromDate = currentNutzer.MoveInDate;
                    IsLastDateNull = false;
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while initializing properties for NutzeinheitEditPageVM!");
                throw;
            }
        }

        private void InitializeObservCollection(List<NutzerQuadratmeter> nutzerQuadratmeter)
        {
            NutzerQuadratmeterObsCollec.Clear();
            foreach (NutzerQuadratmeter neQuadratmeter in nutzerQuadratmeter)
            {
                NutzerQuadratmeterObsCollec.Add(neQuadratmeter);
            }
        }

        private void DisplaySelectedNutzerQuadratmeter(NutzerQuadratmeter selectedNutzerQuadratmeter)
        {
            if (selectedNutzerQuadratmeter.SquareMeters_Hzg.HasValue)
            {
                QuadratmeterHzg = selectedNutzerQuadratmeter.SquareMeters_Hzg.Value.ToString();
            }
            else
            {
                QuadratmeterHzg = "";
            }

            if (selectedNutzerQuadratmeter.SquareMeters_Ww.HasValue)
            {
                QuadratmeterWw = selectedNutzerQuadratmeter.SquareMeters_Ww.Value.ToString();
            }
            else
            {
                QuadratmeterWw = "";
            }

            if (selectedNutzerQuadratmeter.SquareMeters_Nk.HasValue)
            {
                QuadratmeterNk = selectedNutzerQuadratmeter.SquareMeters_Nk.Value.ToString();
            }
            else
            {
                QuadratmeterNk = "";
            }

            NutzerQuadratmeterFromDate = selectedNutzerQuadratmeter.RangeFrom;
            if (selectedNutzerQuadratmeter.RangeTo.HasValue)
            {
                NutzerQuadratmeterToDate = selectedNutzerQuadratmeter.RangeTo.Value;
            }
            else
            {
                NutzerQuadratmeterToDate = null;
            }
            CanNutzerQuadradmeterBeDeleted = selectedNutzerQuadratmeter.IsCreatedByApp;
        }

        private void AddNutzerQuadratmeterExecute()
        {
            try
            {
                //currentNutzer that we are currently inside
                Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                NutzerQuadratmeter newNutzerQuadratmeter = new NutzerQuadratmeter
                {
                    NutzerGuid = currentNutzer.Guid,
                    IsCreatedByApp = true
                };

                if (decimal.TryParse(QuadratmeterHzg, out decimal quadratmeterHzg))
                {
                    newNutzerQuadratmeter.SquareMeters_Hzg = quadratmeterHzg;
                }

                if (decimal.TryParse(QuadratmeterWw, out decimal quadratmeterWw))
                {
                    newNutzerQuadratmeter.SquareMeters_Ww = quadratmeterWw;
                }

                if (decimal.TryParse(QuadratmeterNk, out decimal quadratmeterNk))
                {
                    newNutzerQuadratmeter.SquareMeters_Nk = quadratmeterNk;
                }

                List<NutzerQuadratmeter> currentNutzerQuadratmeterList = currentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();

                if (currentNutzerQuadratmeterList.Count > 0)
                {
                    NutzerQuadratmeter currentNutzerQuadratmeter = currentNutzerQuadratmeterList[0];
                    if(currentNutzerQuadratmeter.RangeTo != null && currentNutzerQuadratmeter.RangeFrom < NutzerQuadratmeterFromDate)
                    {
                        newNutzerQuadratmeter.RangeFrom = currentNutzerQuadratmeter.RangeTo.Value.AddDays(1);
                        newNutzerQuadratmeter.RangeTo = null;
                        nutzerService.AddNutzerQuadratmeter(newNutzerQuadratmeter);
                    }
                    else if(currentNutzerQuadratmeter.RangeTo == null && currentNutzerQuadratmeter.RangeFrom < NutzerQuadratmeterFromDate)
                    {
                        newNutzerQuadratmeter.RangeFrom = NutzerQuadratmeterFromDate;
                        currentNutzerQuadratmeter.RangeTo = newNutzerQuadratmeter.RangeFrom.AddDays(-1);
                        nutzerService.UpdateNutzerQuadratmeter(currentNutzerQuadratmeter);
                        nutzerService.AddNutzerQuadratmeter(newNutzerQuadratmeter);
                    }
                }
                else
                {
                    newNutzerQuadratmeter.RangeFrom = NutzerQuadratmeterFromDate;
                    newNutzerQuadratmeter.RangeTo = NutzerQuadratmeterToDate;
                    nutzerService.AddNutzerQuadratmeter(newNutzerQuadratmeter);
                }
                
                Nutzer updatedCurrentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                List<NutzerQuadratmeter> updatedNutzerQuadratmeterList = updatedCurrentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();
                InitializeObservCollection(updatedNutzerQuadratmeterList);

                NutzerQuadratmeter currentFirstNutzerQuadratmeter = updatedNutzerQuadratmeterList.FirstOrDefault();
                if (currentFirstNutzerQuadratmeter != null)
                {
                    SelectedNutzerQuadratmeter = currentFirstNutzerQuadratmeter;
                    if (currentFirstNutzerQuadratmeter.RangeTo.HasValue)
                    {
                        IsLastDateNull = true;
                    }
                    else
                    {
                        IsLastDateNull = false;
                    }
                }

                //update list on previous view 
                var previousViewModel = GetParentProperty();
                if(updatedNutzerQuadratmeterList[0].SquareMeters_Hzg.HasValue)
                {
                    previousViewModel.QuadratmeterHzg = "Qm Hzg : " + updatedNutzerQuadratmeterList[0].SquareMeters_Hzg;
                }
                else
                {
                    previousViewModel.QuadratmeterHzg = "Qm Hzg : -";
                }

                if(updatedNutzerQuadratmeterList[0].SquareMeters_Nk.HasValue)
                {
                    previousViewModel.QuadratmeterNk = "Qm Nk : " + updatedNutzerQuadratmeterList[0].SquareMeters_Nk;
                }
                else
                {
                    previousViewModel.QuadratmeterHzg = "Qm Nk : -";
                }

                if(updatedNutzerQuadratmeterList[0].SquareMeters_Ww.HasValue)
                {
                    previousViewModel.QuadratmeterWw = "Qm Ww : " + updatedNutzerQuadratmeterList[0].SquareMeters_Ww;
                }
                else
                {
                    previousViewModel.QuadratmeterWw = "Qm Ww : -";
                }
                previousViewModel.NutzerQuadratmeterFromDate = "Von : " + updatedNutzerQuadratmeterList[0].RangeFrom.ToString("dd.MM.yyyy");
                if (updatedNutzerQuadratmeterList[0].RangeTo.HasValue)
                {
                    previousViewModel.NutzerQuadratmeterToDate = "Bis : " + updatedNutzerQuadratmeterList[0].RangeTo.Value.ToString("dd.MM.yyyy");
                }
                else
                {
                    previousViewModel.NutzerQuadratmeterToDate = "Bis : -";
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to execute AddNutzerPersonenExecute");
                throw;
            }
        }

        private void SaveNutzerQuadratmeterExecute()
        {
            try
            {
                if (SelectedNutzerQuadratmeter != null)
                {
                    Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                    List<NutzerQuadratmeter> nutzerQuadratmeterList = currentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();
                    int numberOfNutzerQuadratmeterEntries = nutzerQuadratmeterList.Count;

                    NutzerQuadratmeter selectedNutzerQuadratmeter = nutzerQuadratmeterList.Where(x => x.Guid == SelectedNutzerQuadratmeter.Guid).SingleOrDefault();
                    if(selectedNutzerQuadratmeter != null)
                    {
                        int updatedNutzerQuadratmeterIndex = nutzerQuadratmeterList.IndexOf(selectedNutzerQuadratmeter);

                        if (decimal.TryParse(QuadratmeterHzg, out decimal quadratmeterHzg))
                        {
                            selectedNutzerQuadratmeter.SquareMeters_Hzg = quadratmeterHzg;
                        }

                        if (decimal.TryParse(QuadratmeterWw, out decimal quadratmeterWw))
                        {
                            selectedNutzerQuadratmeter.SquareMeters_Ww = quadratmeterWw;
                        }

                        if (decimal.TryParse(QuadratmeterNk, out decimal quadratmeterNk))
                        {
                            selectedNutzerQuadratmeter.SquareMeters_Nk = quadratmeterNk;
                        }

                        selectedNutzerQuadratmeter.RangeFrom = NutzerQuadratmeterFromDate;
                        selectedNutzerQuadratmeter.RangeTo = NutzerQuadratmeterToDate;

                        nutzerService.UpdateNutzerQuadratmeter(selectedNutzerQuadratmeter);
                        //check if there is previous entry in the table to update
                        if ((updatedNutzerQuadratmeterIndex + 1) >= 0 && (updatedNutzerQuadratmeterIndex + 1) < numberOfNutzerQuadratmeterEntries)
                        {
                            NutzerQuadratmeter previousNutzerQuadratmeter = nutzerQuadratmeterList[updatedNutzerQuadratmeterIndex + 1];
                            previousNutzerQuadratmeter.RangeTo = selectedNutzerQuadratmeter.RangeFrom.AddDays(-1);
                            nutzerService.UpdateNutzerQuadratmeter(previousNutzerQuadratmeter);
                        }
                        //check if there is next entry in the table to update
                        if ((updatedNutzerQuadratmeterIndex - 1) >= 0)
                        {
                            NutzerQuadratmeter nextNutzerQuadratmeter = nutzerQuadratmeterList[updatedNutzerQuadratmeterIndex - 1];
                            nextNutzerQuadratmeter.RangeFrom = selectedNutzerQuadratmeter.RangeTo.Value.AddDays(1);
                            nutzerService.UpdateNutzerQuadratmeter(nextNutzerQuadratmeter);
                        }
                    }
                }
                else
                {
                    Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                    List<NutzerQuadratmeter> nutzerQuadratmeterList = currentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();
                    NutzerQuadratmeter nutzerQuadratmeter = nutzerQuadratmeterList.FirstOrDefault();

                    if(nutzerQuadratmeter != null)
                    {
                        if (decimal.TryParse(QuadratmeterHzg, out decimal quadratmeterHzg))
                        {
                            nutzerQuadratmeter.SquareMeters_Hzg = quadratmeterHzg;
                        }

                        if (decimal.TryParse(QuadratmeterWw, out decimal quadratmeterWw))
                        {
                            nutzerQuadratmeter.SquareMeters_Ww = quadratmeterWw;
                        }

                        if (decimal.TryParse(QuadratmeterNk, out decimal quadratmeterNk))
                        {
                            nutzerQuadratmeter.SquareMeters_Nk = quadratmeterNk;
                        }

                        nutzerQuadratmeter.RangeFrom = NutzerQuadratmeterFromDate;
                        nutzerQuadratmeter.RangeTo = NutzerQuadratmeterToDate;

                        int numberOfNutzerQuadratmeterEntries = nutzerQuadratmeterList.Count;
                        int updatedNutzerQuadratmeterIndex = nutzerQuadratmeterList.IndexOf(nutzerQuadratmeter);

                        nutzerService.UpdateNutzerQuadratmeter(nutzerQuadratmeter);

                        //check if there is next entry in the table to update
                        if ((updatedNutzerQuadratmeterIndex + 1) >= 0 && (updatedNutzerQuadratmeterIndex + 1) < numberOfNutzerQuadratmeterEntries)
                        {
                            NutzerQuadratmeter previousNutzerQuadratmeter = nutzerQuadratmeterList[updatedNutzerQuadratmeterIndex + 1];
                            previousNutzerQuadratmeter.RangeTo = nutzerQuadratmeter.RangeFrom.AddDays(-1);
                            nutzerService.UpdateNutzerQuadratmeter(previousNutzerQuadratmeter);
                        }
                        //check if there is next entry in the table to update
                        if ((updatedNutzerQuadratmeterIndex - 1) >= 0)
                        {
                            NutzerQuadratmeter nextNutzerQuadratmeter = nutzerQuadratmeterList[updatedNutzerQuadratmeterIndex - 1];
                            nextNutzerQuadratmeter.RangeFrom = nutzerQuadratmeter.RangeTo.Value.AddDays(1);
                            nutzerService.UpdateNutzerQuadratmeter(nextNutzerQuadratmeter);
                        }
                    }
                    else
                    {
                        NutzerQuadratmeter newNutzerQuadratmeter = new NutzerQuadratmeter();
                        newNutzerQuadratmeter.NutzerGuid = currentNutzer.Guid;

                        if (decimal.TryParse(QuadratmeterHzg, out decimal quadratmeterHzg))
                        {
                            newNutzerQuadratmeter.SquareMeters_Hzg = quadratmeterHzg;
                        }

                        if (decimal.TryParse(QuadratmeterWw, out decimal quadratmeterWw))
                        {
                            newNutzerQuadratmeter.SquareMeters_Ww = quadratmeterWw;
                        }

                        if (decimal.TryParse(QuadratmeterNk, out decimal quadratmeterNk))
                        {
                            newNutzerQuadratmeter.SquareMeters_Nk = quadratmeterNk;
                        }

                        newNutzerQuadratmeter.RangeFrom = NutzerQuadratmeterFromDate;
                        newNutzerQuadratmeter.RangeTo = NutzerQuadratmeterToDate;
                        nutzerService.AddNutzerQuadratmeter(newNutzerQuadratmeter);
                    }
                }

                Nutzer updatedCurrentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                List<NutzerQuadratmeter> updatedNutzerQuadratmeterList = updatedCurrentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();
                InitializeObservCollection(updatedNutzerQuadratmeterList);

                NutzerQuadratmeter currentFirstNutzerQuadratmeter = updatedNutzerQuadratmeterList.FirstOrDefault();
                if (currentFirstNutzerQuadratmeter != null)
                {
                    SelectedNutzerQuadratmeter = currentFirstNutzerQuadratmeter;
                    if (currentFirstNutzerQuadratmeter.RangeTo.HasValue)
                    {
                        IsLastDateNull = true;
                    }
                    else
                    {
                        IsLastDateNull = false;
                    }
                }

                //update list on previous view 
                var previousViewModel = GetParentProperty();
                if (updatedNutzerQuadratmeterList[0].SquareMeters_Hzg.HasValue)
                {
                    previousViewModel.QuadratmeterHzg = "Qm Hzg : " + updatedNutzerQuadratmeterList[0].SquareMeters_Hzg;
                }
                else
                {
                    previousViewModel.QuadratmeterHzg = "Qm Hzg : -";
                }

                if (updatedNutzerQuadratmeterList[0].SquareMeters_Nk.HasValue)
                {
                    previousViewModel.QuadratmeterNk = "Qm Nk : " + updatedNutzerQuadratmeterList[0].SquareMeters_Nk;
                }
                else
                {
                    previousViewModel.QuadratmeterHzg = "Qm Nk : -";
                }

                if (updatedNutzerQuadratmeterList[0].SquareMeters_Ww.HasValue)
                {
                    previousViewModel.QuadratmeterWw = "Qm Ww : " + updatedNutzerQuadratmeterList[0].SquareMeters_Ww;
                }
                else
                {
                    previousViewModel.QuadratmeterWw = "Qm Ww : -";
                }
                previousViewModel.NutzerQuadratmeterFromDate = "Von : " + updatedNutzerQuadratmeterList[0].RangeFrom.ToString("dd.MM.yyyy");
                if (updatedNutzerQuadratmeterList[0].RangeTo.HasValue)
                {
                    previousViewModel.NutzerQuadratmeterToDate = "Bis : " + updatedNutzerQuadratmeterList[0].RangeTo.Value.ToString("dd.MM.yyyy");
                }
                else
                {
                    previousViewModel.NutzerQuadratmeterToDate = "Bis : -";
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to execute SaveNutzerQuadratmeterExecute");
                throw;
            }
        }

        private void DeleteNutzerQuadratmeterExecute()
        {
            try
            {
                if (SelectedNutzerQuadratmeter != null)
                {
                    Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                    List<NutzerQuadratmeter> orderedNutzerQuadratmeterList = currentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();
                    int numberOfNutzerQuadratmeterEntries = orderedNutzerQuadratmeterList.Count;

                    NutzerQuadratmeter selectedNutzerQuadratmeter = orderedNutzerQuadratmeterList.Where(x => x.Guid == SelectedNutzerQuadratmeter.Guid).Single();
                    int updatedNutzerQuadratmeterIndex = orderedNutzerQuadratmeterList.IndexOf(selectedNutzerQuadratmeter);

                    if (updatedNutzerQuadratmeterIndex != 0 && (updatedNutzerQuadratmeterIndex + 1) < numberOfNutzerQuadratmeterEntries)
                    {
                        NutzerQuadratmeter previousNutzerQuadratmeter = orderedNutzerQuadratmeterList[updatedNutzerQuadratmeterIndex + 1];
                        previousNutzerQuadratmeter.RangeTo = selectedNutzerQuadratmeter.RangeTo;
                        nutzerService.UpdateNutzerQuadratmeter(previousNutzerQuadratmeter);
                    }

                    nutzerService.DeleteNutzerQuadratmeter(selectedNutzerQuadratmeter);
                }
                else
                {
                    Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                    List<NutzerQuadratmeter> orderedNutzerQuadratmeterList = currentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();
                    NutzerQuadratmeter nutzerQuadratmeter = orderedNutzerQuadratmeterList.FirstOrDefault();

                    if (nutzerQuadratmeter != null)
                    {
                        int updatedNutzerQuadratmeterIndex = orderedNutzerQuadratmeterList.IndexOf(nutzerQuadratmeter);
                        int numberOfNutzerQuadratmeterEntries = orderedNutzerQuadratmeterList.Count;

                        if (updatedNutzerQuadratmeterIndex == 0 && (updatedNutzerQuadratmeterIndex + 1) < numberOfNutzerQuadratmeterEntries)
                        {
                            NutzerQuadratmeter previousNutzerQuadratmeter = orderedNutzerQuadratmeterList[1];
                            previousNutzerQuadratmeter.RangeTo = null;
                            nutzerService.UpdateNutzerQuadratmeter(previousNutzerQuadratmeter);
                        }

                        if (updatedNutzerQuadratmeterIndex != 0 && (updatedNutzerQuadratmeterIndex + 1) < numberOfNutzerQuadratmeterEntries)
                        {
                            NutzerQuadratmeter previousNutzerQuadratmeter = orderedNutzerQuadratmeterList[updatedNutzerQuadratmeterIndex + 1];
                            previousNutzerQuadratmeter.RangeTo = nutzerQuadratmeter.RangeTo;
                            nutzerService.UpdateNutzerQuadratmeter(previousNutzerQuadratmeter);
                        }

                        nutzerService.DeleteNutzerQuadratmeter(nutzerQuadratmeter);
                    }
                }

                //Update Observable Collection After Deleting
                Nutzer updatedCurrentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                List<NutzerQuadratmeter> updatedNutzerQuadratmeterList = updatedCurrentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();
                InitializeObservCollection(updatedNutzerQuadratmeterList);

                //update list on previous view 
                NutzerQuadratmeter currentFirstNutzerQuadratmeter = updatedNutzerQuadratmeterList.FirstOrDefault();
                if (currentFirstNutzerQuadratmeter != null)
                {
                    SelectedNutzerQuadratmeter = currentFirstNutzerQuadratmeter;
                    if (currentFirstNutzerQuadratmeter.RangeTo.HasValue)
                    {
                        IsLastDateNull = true;
                    }
                    else
                    {
                        IsLastDateNull = false;
                    }
                }
                else
                {
                    QuadratmeterHzg = "0";
                    QuadratmeterWw = "0";
                    QuadratmeterNk = "0";
                    NutzerQuadratmeterFromDate = updatedCurrentNutzer.MoveInDate;
                    NutzerQuadratmeterToDate = null;
                    CanNutzerQuadradmeterBeDeleted = false;
                }

                var previousViewModel = GetParentProperty();
                if (currentFirstNutzerQuadratmeter != null)
                {
                    if (currentFirstNutzerQuadratmeter.SquareMeters_Hzg.HasValue)
                    {
                        previousViewModel.QuadratmeterHzg = "Qm Hzg : " + currentFirstNutzerQuadratmeter.SquareMeters_Hzg.Value.ToString();
                    }
                    else
                    {
                        previousViewModel.QuadratmeterHzg = "Qm Hzg : -";
                    }

                    if (currentFirstNutzerQuadratmeter.SquareMeters_Ww.HasValue)
                    {
                        previousViewModel.QuadratmeterWw = "Qm Ww : " + currentFirstNutzerQuadratmeter.SquareMeters_Ww.Value.ToString();
                    }
                    else
                    {
                        previousViewModel.QuadratmeterWw = "Qm Ww : -";
                    }

                    if (currentFirstNutzerQuadratmeter.SquareMeters_Nk.HasValue)
                    {
                        previousViewModel.QuadratmeterNk = "Qm Nk : " + currentFirstNutzerQuadratmeter.SquareMeters_Nk.Value.ToString();
                    }
                    else
                    {
                        previousViewModel.QuadratmeterNk = "Qm Nk : -";
                    }

                    previousViewModel.NutzerQuadratmeterFromDate = "Von : " + currentFirstNutzerQuadratmeter.RangeFrom.ToString("dd.MM.yyyy");

                    if (currentFirstNutzerQuadratmeter.RangeTo.HasValue)
                    {
                        previousViewModel.NutzerQuadratmeterToDate = "Bis : " + currentFirstNutzerQuadratmeter.RangeTo.Value.ToString("dd.MM.yyyy");
                    }
                    else
                    {
                        previousViewModel.NutzerQuadratmeterToDate = "Bis : -";
                    }
                }
                else
                {
                    previousViewModel.QuadratmeterHzg = "Qm Hzg : -";
                    previousViewModel.QuadratmeterWw = "Qm Ww : -";
                    previousViewModel.QuadratmeterNk = "Qm Nk : -";
                    previousViewModel.NutzerQuadratmeterFromDate = "Von : -";
                    previousViewModel.NutzerQuadratmeterToDate = "Bis : -";
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to execute DeleteNutzerQuadratmeterExecute");
                throw;
            }
        }

        private void NewEmptyNutzerQuadratmeterExecute()
        {
            try
            {
                //currentNutzer that we are currently inside
                Nutzer currentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                NutzerQuadratmeter newNutzerQuadratmeter = new NutzerQuadratmeter
                {
                    NutzerGuid = currentNutzer.Guid,
                    IsCreatedByApp = true
                };

                List<NutzerQuadratmeter> currentNutzerQuadratmeterList = currentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();

                NutzerQuadratmeter currentNutzerQuadratmeter = currentNutzerQuadratmeterList[0];
                newNutzerQuadratmeter.SquareMeters_Hzg = currentNutzerQuadratmeter.SquareMeters_Hzg;
                newNutzerQuadratmeter.SquareMeters_Ww = currentNutzerQuadratmeter.SquareMeters_Ww;
                newNutzerQuadratmeter.SquareMeters_Nk = currentNutzerQuadratmeter.SquareMeters_Nk;
                newNutzerQuadratmeter.RangeFrom = currentNutzerQuadratmeter.RangeTo.Value.AddDays(1);
                newNutzerQuadratmeter.RangeTo = null;
                nutzerService.AddNutzerQuadratmeter(newNutzerQuadratmeter);

                Nutzer updatedCurrentNutzer = nutzerService.GetNutzer(selectedNutzer.NutzerGuid);
                List<NutzerQuadratmeter> updatedNutzerQuadratmeterList = updatedCurrentNutzer.NutzerQuadratmeter.OrderByDescending(x => x.RangeFrom).ToList();
                InitializeObservCollection(updatedNutzerQuadratmeterList);

                NutzerQuadratmeter currentFirstNutzerQuadratmeter = updatedNutzerQuadratmeterList.FirstOrDefault();
                if (currentFirstNutzerQuadratmeter != null)
                {
                    SelectedNutzerQuadratmeter = currentFirstNutzerQuadratmeter;
                    if (currentFirstNutzerQuadratmeter.RangeTo.HasValue)
                    {
                        IsLastDateNull = true;
                    }
                    else
                    {
                        IsLastDateNull = false;
                    }
                }

                //update list on previous view 
                var previousViewModel = GetParentProperty();
                if (updatedNutzerQuadratmeterList[0].SquareMeters_Hzg.HasValue)
                {
                    previousViewModel.QuadratmeterHzg = "Qm Hzg : " + updatedNutzerQuadratmeterList[0].SquareMeters_Hzg;
                }
                else
                {
                    previousViewModel.QuadratmeterHzg = "Qm Hzg : -";
                }

                if (updatedNutzerQuadratmeterList[0].SquareMeters_Nk.HasValue)
                {
                    previousViewModel.QuadratmeterNk = "Qm Nk : " + updatedNutzerQuadratmeterList[0].SquareMeters_Nk;
                }
                else
                {
                    previousViewModel.QuadratmeterHzg = "Qm Nk : -";
                }

                if (updatedNutzerQuadratmeterList[0].SquareMeters_Ww.HasValue)
                {
                    previousViewModel.QuadratmeterWw = "Qm Ww : " + updatedNutzerQuadratmeterList[0].SquareMeters_Ww;
                }
                else
                {
                    previousViewModel.QuadratmeterWw = "Qm Ww : -";
                }
                previousViewModel.NutzerQuadratmeterFromDate = "Von : " + updatedNutzerQuadratmeterList[0].RangeFrom.ToString("dd.MM.yyyy");
                if (updatedNutzerQuadratmeterList[0].RangeTo.HasValue)
                {
                    previousViewModel.NutzerQuadratmeterToDate = "Bis : " + updatedNutzerQuadratmeterList[0].RangeTo.Value.ToString("dd.MM.yyyy");
                }
                else
                {
                    previousViewModel.NutzerQuadratmeterToDate = "Bis : -";
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to execute NewEmptyNutzerQuadratmeterExecute");
                throw;
            }
        }

        #endregion
    }
}
