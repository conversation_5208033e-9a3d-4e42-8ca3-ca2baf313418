﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NinjectModules.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Droid.Ioc
{
    using Common.Interfaces;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using GalaSoft.MvvmLight.Views;
    using Ninject.Modules;
    using Services;

    public class NinjectModules : NinjectModule
    {
        public override void Load()
        {
#if DEVELOPMENT
            BindDevelopmentServices();
#else
            BindReleaseServices();
#endif
        }

        private void BindDevelopmentServices()
        {
            Bind<ICloseApplicationService>().To<CloseApplicationService>().InSingletonScope();
            Bind<IPlatformService>().To<PlatformServiceDevelopment>().InSingletonScope();
            Bind<IDialogService>().To<DialogService>().InSingletonScope();
        }

        // ReSharper disable once UnusedMember.Local
        private void BindReleaseServices()
        {
            Bind<ICloseApplicationService>().To<CloseApplicationService>().InSingletonScope();
            Bind<IPlatformService>().To<PlatformService>().InSingletonScope();
            Bind<IDialogService>().To<DialogService>().InSingletonScope();
        }
    }
}