﻿namespace Eras2AmwApp
{
    using System;
    using AutoMapper;
    using Bootstrap;
    using Common.Implementations;
    using Common.Interfaces;
    using Common.Ioc;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Services;
    using Eras2AmwApp.Interfaces;
    using Eras2AmwApp.ViewModels;
    using Serilog;
    using Xamarin.Forms;

    public partial class App : Application
    {
        public App()
        {
            InitializeComponent();

            // Handle when your app starts
            NinjectKernel.Initialize();
         
            BootstrapSystem();
        }

        private bool ForceLogin
        {
            get
            {
                var navigationService = NinjectKernel.Get<IEcNavigationService>();

                if (!navigationService.ShowInitPageOnResume)
                {
                    return false;
                }

                IAppResumeService appResumeService = new AppResumeService();

                return !appResumeService.IsQrCodeScanMandatory && appResumeService.IsAppLoginMandatory;
            }
        }

        protected override void OnStart()
        {
        }

        protected override void OnSleep()
        { 
            // Handle when your app sleeps
        }

        protected override void OnResume()
        {
            try
            {
                if (ForceLogin)
                {
                    ShowLoginPage();
                }
            }
            catch (Exception e)
            {
                var logger = NinjectKernel.Get<ILogger>();
                logger.Error(e, "Exception during OnResume");
                throw;
            }
        }

        private void ShowLoginPage()
        {
            var navigationService = NinjectKernel.Get<IEcNavigationService>();

            navigationService.InitializeAsync<LoginPageViewModel>().ContinueWith(x => navigationService.RemoveBackStackAsync());
        }

        private void BootstrapSystem()
        {
            IServiceLocator serviceLocator = null;

            try
            {
                serviceLocator = new ServiceLocator(
                    NinjectKernel.Get<IAppSettings>(), 
                    NinjectKernel.Get<ILogger>(), 
                    NinjectKernel.Get<ILocalisationService>(), 
                    NinjectKernel.Get<IMapper>(), 
                    NinjectKernel.Get<IResourceService>());
                
                new Bootstrapper(serviceLocator).Setup();
            }
            catch (Exception e)
            {
                serviceLocator?.Logger.Error(e, "Bootrapping System failed");
                throw;
            }
        }
    }
}
