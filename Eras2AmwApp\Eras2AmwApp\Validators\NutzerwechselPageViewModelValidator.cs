﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerwechselPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Validators
{
    using FluentValidation;
    using ViewModels;

    public class NutzerwechselPageViewModelValidator : AbstractValidator<NutzerwechselPageViewModel>
    {
        public NutzerwechselPageViewModelValidator()
        {
            RuleFor(x => x.OldNutzerMoveOutDate)
                .NotEmpty().WithMessage("Auszugsdatum darf nicht leer sein.")
                .GreaterThan(x => x.OldNutzerMoveInDate).WithMessage("Auszugsdatum darf nicht vor Einzugsdatum liegen.")
                .LessThanOrEqualTo(x => x.NutzerMoveInDate).WithMessage("Auszugsdatum muss vor dem neuen Einzugsdatum liegen.");

            RuleFor(x => x.NutzerMoveInDate)
                .NotEmpty().WithMessage("Auszugsdatum darf nicht leer sein.")
                .GreaterThan(x => x.OldNutzerMoveOutDate).WithMessage("Das neue Einzugsdatum muss größer sein als das vorherige Auszugsdatum.");

            RuleFor(x => x.NutzerSalutation)
                .NotEmpty().WithMessage("Die Anrede darf nicht leer sein.");

            RuleFor(x => x.NutzerName1)
                .NotEmpty().WithMessage("Der Nutzername darf nicht leer sein.")
                .Length(2, 25).WithMessage("Der Nutzername muss zwischen 2 und 25 Zeichen lang sein.");

            RuleFor(x => x.SelectedNutzerKind)
                .NotEmpty().WithMessage("Die Nutzertyp darf nicht leer sein.");
        }
    }
}
