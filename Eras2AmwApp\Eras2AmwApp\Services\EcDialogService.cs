﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DialogService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Services
{
    using System;
    using System.Threading.Tasks;
    using Interfaces;
    using Syncfusion.SfBusyIndicator.XForms;
    using Syncfusion.XForms.PopupLayout;
    using Xamarin.Forms;

    public enum DialogResponse
    {
        Accept,
        Decline
    }

    public class EcDialogService : IEcDialogService
    {
        private SfPopupLayout popupLayout;

        private TaskCompletionSource<DialogResponse> source;

        private SfPopupLayout busyIndicatorPopup;

        private SfBusyIndicator busyIndicator;

        private Command declineButtonCommand;

        private Command acceptButtonCommand;

        public Command DeclineButtonCommand => declineButtonCommand ?? (declineButtonCommand = new Command(DeclineButtonExecuted));

        public Command AcceptButtonCommand => acceptButtonCommand ?? (acceptButtonCommand = new Command(AcceptButtonExecuted));

        public void Info(string message, string title = null)
        {
            var stackLayout = new StackLayout { Orientation = StackOrientation.Vertical };
            Label content = CreateContent(message);
            content.HorizontalTextAlignment = TextAlignment.Center;
            stackLayout.Children.Add(content);

            popupLayout = new SfPopupLayout
            {
                PopupView =
                {
                    Margin = 5,
                    HeaderTitle = title,
                    ContentTemplate = new DataTemplate(() => stackLayout),
                    AutoSizeMode = AutoSizeMode.Height,
                    WidthRequest = 350,
                    ShowFooter = false
                }
            };

            popupLayout.Show();
        }

        public Task<DialogResponse> AcceptAsync(string message, string acceptButtonText = null, string title = null)
        {
            source = new TaskCompletionSource<DialogResponse>();

            var stackLayout = new StackLayout
            {
                Orientation = StackOrientation.Vertical,
                VerticalOptions = LayoutOptions.FillAndExpand,
                HorizontalOptions = LayoutOptions.FillAndExpand
            };

            Label content = CreateContent(message);
            stackLayout.Children.Add(content);

            popupLayout = new SfPopupLayout
            {
                ClosePopupOnBackButtonPressed = false,
                StaysOpen = true,
                PopupView =
                {
                    HeaderTemplate = new DataTemplate(() => CreateHeader(title)),
                    ShowCloseButton = false,
                    AppearanceMode = AppearanceMode.OneButton,
                    AutoSizeMode = AutoSizeMode.Height,
                    WidthRequest = 350,
                    ContentTemplate = new DataTemplate(() => stackLayout),
                    AcceptButtonText = acceptButtonText,
                    AcceptCommand = AcceptButtonCommand
                }
            };

            popupLayout.Show();

            return source.Task;
        }

        public Task<DialogResponse> AcceptDeclineAsync(string message, string acceptButtonText, string declineButtonText, string title = null)
        {
            source = new TaskCompletionSource<DialogResponse>();
            var stackLayout = new StackLayout { Orientation = StackOrientation.Vertical };
            Label content = CreateContent(message);
            stackLayout.Children.Add(content);
            popupLayout = new SfPopupLayout
            {
                ClosePopupOnBackButtonPressed = false,
                StaysOpen = true,
                PopupView =
                {
                      HeaderTemplate = new DataTemplate(() => CreateHeader(title)),
                      ShowCloseButton = false,
                      AppearanceMode = AppearanceMode.TwoButton,
                      ContentTemplate = new DataTemplate(() => stackLayout),
                      AutoSizeMode = AutoSizeMode.Height,
                      ShowFooter = true,
                      WidthRequest = 320,
                      DeclineCommand = DeclineButtonCommand,
                      DeclineButtonText = declineButtonText,
                      AcceptButtonText = acceptButtonText,
                      AcceptCommand = AcceptButtonCommand,
                }
            };

            popupLayout.Show();
            return source.Task;
        }

        public void ShowBusyIndicator(AnimationTypes animationType = AnimationTypes.Gear, string title = "")
        {
            var stackLayout = new StackLayout
            {
                Orientation = StackOrientation.Vertical,
                VerticalOptions = LayoutOptions.FillAndExpand,
                HorizontalOptions = LayoutOptions.FillAndExpand
            };

            busyIndicator = new SfBusyIndicator()
            {
                AnimationType = animationType,
                BackgroundColor = Color.Transparent,
                IsBusy = true,
                ViewBoxHeight = 70,
                ViewBoxWidth = 70,
                TitlePlacement = TitlePlacement.Bottom,
                Title = title,
            };

            stackLayout.Children.Add(busyIndicator);

            busyIndicatorPopup = new SfPopupLayout
            {
                ClosePopupOnBackButtonPressed = false,
                StaysOpen = true,
                PopupView =
                {
                    ShowHeader = false,
                    ShowFooter = false,
                    ShowCloseButton = false,
                    HeightRequest = 200,
                    ContentTemplate = new DataTemplate(() => stackLayout)
                }
            };

            busyIndicatorPopup.Show();
        }

        public void SetBusyIndicatorText(string text)
        {
            if (busyIndicator == null)
            {
                return;
            }

            busyIndicator.Title = text ?? throw new ArgumentNullException(nameof(text));
        }

        public void HideBusyIndicator()
        {
            if (busyIndicator == null)
            {
                return;
            }

            if (busyIndicatorPopup == null)
            {
                return;
            }

            busyIndicator.IsBusy = false;
            busyIndicatorPopup.IsOpen = false;
        }

        private void AcceptButtonExecuted()
        {
            source.TrySetResult(DialogResponse.Accept);
        }

        private void DeclineButtonExecuted()
        {
            source.TrySetResult(DialogResponse.Decline);
        }

        private Label CreateHeader(string text)
        {
            var headerContent = new Label
            {
              Text = text ?? string.Empty,
              FontAttributes = FontAttributes.Bold,
              TextColor = Color.White,
              BackgroundColor = Color.FromHex("#538EEC"),
              FontSize = 20,
              HorizontalTextAlignment = TextAlignment.Center,
              VerticalTextAlignment = TextAlignment.Center
            };

            return headerContent;
        }

        private Label CreateContent(string message)
        {
            var label = new Label
            {
                Margin= new Thickness(5, 0, 5, 0),
                LineBreakMode = LineBreakMode.WordWrap,
                BackgroundColor = Color.White,
                HorizontalOptions = LayoutOptions.CenterAndExpand,
                VerticalOptions = LayoutOptions.CenterAndExpand,
                FontSize = 20,
                TextColor = Color.Black,
                Text = message
            };

            return label;
        }
    }
}