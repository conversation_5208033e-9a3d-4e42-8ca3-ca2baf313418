﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <ProduceReferenceAssembly>true</ProduceReferenceAssembly>
    <Configurations>Debug;Release;StandaloneDevelopment;Development;StandaloneRelease</Configurations>
    <Version>1.21</Version>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneDevelopment|AnyCPU'">
    <DefineConstants>TRACE;DEVELOPMENT,STANDALONE_APP</DefineConstants>
    <Optimize>true</Optimize>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|AnyCPU'">
    <DefineConstants>TRACE;DEVELOPMENT</DefineConstants>
    <Optimize>true</Optimize>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneRelease|AnyCPU'">
    <Optimize>true</Optimize>
    <DefineConstants>TRACE;STANDALONE_APP</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="10.3.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="2.2.6" />
    <PackageReference Include="MvvmLightLibsStd10" Version="5.4.1.1" />
    <PackageReference Include="Ninject" Version="3.3.6" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="Syncfusion.Xamarin.Buttons" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.Cards" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.Core" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.Expander" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.SfBadgeView" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.SfBusyIndicator" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.SfCalendar" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.SfDataGrid" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.SfNumericTextBox" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.SfPopupLayout" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.SfSchedule" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.SfTabView" Version="19.3.0.46" />
    <PackageReference Include="Syncfusion.Xamarin.SfTreeView" Version="19.3.0.46" />
    <PackageReference Include="Xam.Plugin.Media" Version="5.0.1" />
    <PackageReference Include="Xamarin.Controls.SignaturePad.Forms" Version="3.0.0" />
    <PackageReference Include="Xamarin.Essentials" Version="1.7.0" />
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2478" />
    <PackageReference Include="ZXing.Net.Mobile" Version="2.4.1" />
    <PackageReference Include="ZXing.Net.Mobile.Forms" Version="2.4.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Endiancode.Utilities\Endiancode.Utilities.csproj" />
    <ProjectReference Include="..\..\Eras2AmwApp.Bl\Eras2AmwApp.BusinessLogic.csproj" />
    <ProjectReference Include="..\..\Eras2AmwApp.Common\Eras2AmwApp.Common.csproj" />
    <ProjectReference Include="..\..\Eras2AmwApp.Domain\Eras2AmwApp.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="App.xaml">
      <Generator>MSBuild:Compile</Generator>
    </None>
    <None Update="Pages\AppointmentPage.xaml">
      <Generator>MSBuild:Compile</Generator>
    </None>
    <None Update="Pages\CalendarPage.xaml">
      <Generator>MSBuild:Compile</Generator>
    </None>
    <None Update="Pages\LoginPage.xaml">
      <Generator>MSBuild:Compile</Generator>
    </None>
    <None Update="Pages\MainPage.xaml">
      <Generator>MSBuild:Compile</Generator>
    </None>
    <None Update="Pages\OrderPage.xaml">
      <Generator>MSBuild:Compile</Generator>
    </None>
    <None Update="Pages\SchedulePage.xaml">
      <Generator>MSBuild:Compile</Generator>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Pages\AppointmentPage.xaml.cs">
      <DependentUpon>AppointmentPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Pages\RauchmelderEditPage.xaml.cs">
      <DependentUpon>RauchmelderEditPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Pages\NutzeinheitPage.xaml.cs">
      <DependentUpon>NutzeinheitPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Pages\NutzerEditPage.xaml.cs">
      <DependentUpon>NutzerEditPage.xaml</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Pages\AddDevicePage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\AppointmentPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\CreateNutzeinheitPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\NutzerPersonenPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\NutzerQuadratmeterPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\RauchmelderExchangePage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\WatermeterEditPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\RauchmelderEditPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\NutzeinheitPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\LoginPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\NutzerEditPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\NutzerwechselPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\OrderEditPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\OrderPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\PicturePreviewPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\RegistrationPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\SummeryPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Pages\WatermeterExchangePage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>
</Project>