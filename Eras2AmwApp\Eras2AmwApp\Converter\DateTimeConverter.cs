﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DateTimeConverter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Converter
{
    using System;
    using System.Globalization;
    using Xamarin.Forms;

    public class DateTimeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if(value == null)
            {
                return null;
            }

            if(value is DateTime dateTime && dateTime == DateTime.MinValue)
            {
                return value.ToString();
            }


            DateTime date = (DateTime)value;
            string formatedDate = date.ToShortDateString();

            return formatedDate;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
            {
                return null;
            }

            DateTime dateTime = new DateTime();

            if (value is string date)
            {
                dateTime = DateTime.Parse(date);
            }

            return dateTime;
        }
    }
}
